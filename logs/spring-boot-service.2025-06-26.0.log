2025-06-26 08:36:45.036 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 08:36:45.104 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 08:36:45.110 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 08:36:45.110 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 08:36:45.110 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 08:36:45.111 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 08:48:34.895 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 08:48:34.897 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 08:48:34.900 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 08:48:34.900 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 08:48:34.901 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 08:48:34.901 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 08:54:36.603 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 08:54:36.605 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 08:54:36.606 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 08:54:36.606 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 08:54:36.607 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 08:54:36.607 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 08:54:51.966 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 08:54:51.967 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 08:54:51.968 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 08:54:51.968 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 08:54:51.969 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 08:54:51.969 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 08:54:55.788 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 08:54:55.789 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 08:54:55.791 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 08:54:55.792 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 08:54:55.793 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 08:54:55.793 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 08:54:59.851 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 08:54:59.852 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 08:54:59.853 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 08:54:59.853 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 08:54:59.854 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 08:54:59.854 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 08:55:05.951 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 08:55:05.952 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 08:55:05.954 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 08:55:05.955 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 08:55:05.955 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 08:55:05.956 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 08:55:16.453 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 08:55:16.454 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 08:55:16.456 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 08:55:16.456 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 08:55:16.457 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 08:55:16.457 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 08:55:16.555 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 08:55:17.412 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-26 08:55:17.412 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-26 08:55:17.412 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-26 08:55:17.413 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-26 08:55:17.413 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-26 08:55:17.414 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-26 08:55:17.414 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-26 08:55:17.414 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-26 08:55:17.415 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-26 08:55:17.415 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-26 08:55:20.527 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=21, keyword=, startTime=null, endTime=null)
2025-06-26 08:55:20.528 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 21, keyword: , startTime: null, endTime: null
2025-06-26 08:55:20.688 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 6 条数据
2025-06-26 08:55:20.689 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 6
2025-06-26 08:55:47.967 - INFO - [SimulationController.java:170] : 接收到运行仿真任务请求: simulationId=18
2025-06-26 08:55:47.968 - INFO - [SimulationServiceImpl.java:821] : 开始运行仿真任务，仿真ID: 18
2025-06-26 08:55:48.250 - INFO - [SimulationServiceImpl.java:898] : 仿真任务已提交到异步队列，仿真ID: 18
2025-06-26 08:55:48.250 - INFO - [SimulationServiceImpl.java:870] : 开始异步执行DSS仿真，仿真ID: 18
2025-06-26 08:55:48.251 - INFO - [SimulationController.java:176] : 仿真任务运行成功，任务ID: 18
2025-06-26 08:55:48.251 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:5000, DSS文件路径: D:/CETWorkSpace/ngap-server/data/21/18/simulation_script/simulationScript.dss
2025-06-26 08:55:48.607 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 200 OK
2025-06-26 08:55:48.607 - INFO - [SimulationServiceImpl.java:873] : 仿真任务运行结果，HTTP状态码: 200 OK, HTTP信息：Strategy file not found: voltage_control.py, 仿真ID: 18
2025-06-26 08:55:48.717 - INFO - [SimulationServiceImpl.java:918] : 仿真状态已更新为SUCCESS，仿真ID: 18
2025-06-26 08:55:50.686 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=21, keyword=, startTime=null, endTime=null)
2025-06-26 08:55:50.687 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 21, keyword: , startTime: null, endTime: null
2025-06-26 08:55:50.720 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 6 条数据
2025-06-26 08:55:50.722 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 6
2025-06-26 08:55:51.569 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=21, keyword=, startTime=null, endTime=null)
2025-06-26 08:55:51.570 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 21, keyword: , startTime: null, endTime: null
2025-06-26 08:55:51.607 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 6 条数据
2025-06-26 08:55:51.609 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 6
2025-06-26 08:56:55.864 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 08:56:55.866 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 08:56:56.460 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 08:56:56.623 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 08:56:56.625 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 08:56:56.626 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 08:56:56.653 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 09:00:11.229 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:00:11.231 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:00:11.234 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:00:11.235 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:00:11.236 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:00:11.236 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:00:15.508 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:00:15.508 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:00:15.512 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:00:15.512 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:00:15.512 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:00:15.512 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:00:21.345 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:00:21.346 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:00:21.350 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:00:21.351 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:00:21.351 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:00:21.352 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:00:27.089 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:00:27.090 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:00:27.092 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:00:27.092 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:00:27.093 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:00:27.093 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:00:38.328 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:00:38.329 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:00:38.332 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:00:38.332 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:00:38.332 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:00:38.333 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:00:38.357 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 09:02:45.468 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:02:45.483 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:02:45.486 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:02:45.487 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:02:45.489 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:02:45.490 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:02:49.523 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:02:49.523 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:02:49.524 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:02:49.524 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:02:49.524 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:02:49.525 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:02:53.516 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:02:53.518 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:02:53.520 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:02:53.520 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:02:53.521 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:02:53.522 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:03:00.855 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:03:00.856 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:03:00.858 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:03:00.858 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:03:00.858 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:03:00.858 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:03:00.884 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 09:04:22.429 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:04:22.430 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:04:22.431 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:04:22.431 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:04:22.431 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:04:22.431 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:04:28.434 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:04:28.435 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:04:28.437 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:04:28.437 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:04:28.438 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:04:28.438 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:04:43.302 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:04:43.303 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:04:43.305 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:04:43.306 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:04:43.307 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:04:43.308 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:04:46.444 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:04:46.445 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:04:46.447 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:04:46.448 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:04:46.449 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:04:46.449 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:04:53.069 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:04:53.071 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:04:53.073 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:04:53.074 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:04:53.074 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:04:53.075 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:04:53.102 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 09:06:39.517 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:06:39.534 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:06:39.537 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:06:39.537 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:06:39.538 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:06:39.538 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:06:46.031 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:06:46.032 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:06:46.034 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:06:46.034 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:06:46.035 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:06:46.035 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:06:56.483 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:06:56.483 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:06:56.485 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:06:56.486 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:06:56.486 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:06:56.486 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:06:56.508 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 09:07:27.054 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:07:27.056 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:07:27.057 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:07:27.058 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:07:27.059 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:07:27.059 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:07:27.084 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 09:08:32.668 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:08:32.669 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:08:32.671 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:08:32.672 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:08:32.672 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:08:32.672 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:08:37.420 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:08:37.421 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:08:37.421 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:08:37.422 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:08:37.422 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:08:37.422 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:08:43.169 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:08:43.170 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:08:43.173 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:08:43.174 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:08:43.174 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:08:43.175 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:08:43.196 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 09:09:18.083 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:09:18.084 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:09:18.086 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:09:18.086 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:09:18.088 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:09:18.088 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:09:22.007 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:09:22.008 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:09:22.010 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:09:22.011 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:09:22.011 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:09:22.012 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:09:26.808 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:09:26.808 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:09:26.809 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:09:26.809 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:09:26.809 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:09:26.809 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:09:26.829 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 09:11:05.633 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:11:05.634 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:11:05.635 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:11:05.635 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:11:05.636 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:11:05.636 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:11:05.656 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 09:25:21.269 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:25:21.301 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:25:21.303 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:25:21.304 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:25:21.306 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:25:21.306 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:33:23.961 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:33:23.961 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:33:23.962 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:33:23.962 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:33:23.963 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:33:23.963 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:33:29.210 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:33:29.211 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:33:29.213 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:33:29.214 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:33:29.215 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:33:29.215 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:33:32.151 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:33:32.151 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:33:32.152 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:33:32.153 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:33:32.154 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:33:32.154 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:33:32.176 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 09:33:40.367 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:33:40.367 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:33:40.369 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:33:40.369 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:33:40.370 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:33:40.370 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:33:40.387 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 09:35:09.836 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:35:09.837 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:35:09.838 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:35:09.838 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:35:09.838 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:35:09.838 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:35:21.146 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:35:21.146 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:35:21.147 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:35:21.148 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:35:21.148 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:35:21.149 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:35:27.538 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:35:27.539 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:35:27.541 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:35:27.541 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:35:27.542 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:35:27.542 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:35:30.995 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:35:30.996 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:35:30.997 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:35:30.998 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:35:30.998 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:35:30.999 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:35:31.028 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 09:37:42.789 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:37:42.796 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:37:42.797 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:37:42.798 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:37:42.798 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:37:42.799 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:37:42.819 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 09:38:18.023 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:38:18.024 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:38:18.024 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:38:18.026 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:38:18.026 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:38:18.026 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:38:18.050 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 09:40:18.083 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-26 09:40:18.084 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-26 09:40:18.084 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-26 09:40:18.084 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-26 09:40:18.084 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-26 09:40:18.084 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-26 09:40:18.085 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-26 09:40:18.085 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-26 09:40:18.086 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-26 09:40:18.086 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-26 09:40:29.140 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=19, keyword=, startTime=null, endTime=null)
2025-06-26 09:40:29.140 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 19, keyword: , startTime: null, endTime: null
2025-06-26 09:40:29.156 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-26 09:40:29.156 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-26 09:40:40.300 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-26 09:40:40.300 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-26 09:40:40.300 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-26 09:40:40.301 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-26 09:40:40.301 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-26 09:40:40.301 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-26 09:40:40.302 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-26 09:40:40.302 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-26 09:40:40.303 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-26 09:40:40.303 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-26 09:40:41.473 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=19, keyword=, startTime=null, endTime=null)
2025-06-26 09:40:41.473 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 19, keyword: , startTime: null, endTime: null
2025-06-26 09:40:41.476 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-26 09:40:41.476 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-26 09:46:37.042 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:46:37.043 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:46:37.047 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:46:37.047 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:46:37.049 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:46:37.049 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:46:41.076 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:46:41.077 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:46:41.080 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:46:41.080 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:46:41.081 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:46:41.082 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:46:46.564 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:46:46.565 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:46:46.565 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:46:46.565 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:46:46.566 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:46:46.566 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:46:46.594 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 09:47:00.670 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 09:47:00.671 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 09:47:00.672 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 09:47:00.673 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 09:47:00.673 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 09:47:00.675 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 09:47:00.693 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 10:29:39.763 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 10:29:39.770 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 10:29:39.773 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 10:29:39.773 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 10:29:39.774 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 10:29:39.774 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 10:29:44.143 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 10:29:44.144 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 10:29:44.145 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 10:29:44.145 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 10:29:44.146 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 10:29:44.147 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 10:29:50.738 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 10:29:50.739 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 10:29:50.740 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 10:29:50.740 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 10:29:50.740 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 10:29:50.740 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 10:29:50.763 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 10:30:02.956 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 10:30:02.956 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 10:30:02.958 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 10:30:02.958 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 10:30:02.959 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 10:30:02.959 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 10:30:02.981 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 10:57:26.152 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 10:57:26.153 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 10:57:26.166 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 10:57:26.166 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 10:57:26.167 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 10:57:26.167 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 10:57:31.711 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 10:57:31.712 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 10:57:31.713 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 10:57:31.714 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 10:57:31.714 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 10:57:31.715 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:00:03.340 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:00:03.340 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:00:03.341 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:00:03.342 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:00:03.342 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:00:03.342 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:00:10.502 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:00:10.503 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:00:10.504 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:00:10.504 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:00:10.505 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:00:10.505 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:00:28.827 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:00:28.829 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:00:28.831 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:00:28.831 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:00:28.833 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:00:28.834 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:01:04.314 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:01:04.315 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:01:04.318 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:01:04.318 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:01:04.319 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:01:04.320 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:02:07.180 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:02:07.180 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:02:07.181 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:02:07.181 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:02:07.182 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:02:07.182 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:02:13.628 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:02:13.628 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:02:13.629 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:02:13.629 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:02:13.630 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:02:13.630 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:06:55.180 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:06:55.180 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:06:55.181 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:06:55.183 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:06:55.183 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:06:55.183 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:08:23.484 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:08:23.484 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:08:23.485 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:08:23.485 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:08:23.485 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:08:23.485 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:08:26.928 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:08:26.928 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:08:26.929 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:08:26.929 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:08:26.930 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:08:26.930 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:08:29.868 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:08:29.869 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:08:29.870 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:08:29.870 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:08:29.871 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:08:29.871 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:08:29.895 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:10:10.684 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:10:10.684 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:10:10.685 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:10:10.685 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:10:10.685 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:10:10.686 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:10:10.717 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:11:04.684 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:11:04.685 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:11:04.686 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:11:04.686 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:11:04.687 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:11:04.687 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:11:27.105 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:11:27.106 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:11:27.108 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:11:27.108 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:11:27.109 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:11:27.109 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:11:39.201 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:11:39.201 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:11:39.202 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:11:39.202 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:11:39.202 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:11:39.203 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:11:39.230 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:12:54.938 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:12:54.938 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:12:54.940 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:12:54.940 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:12:54.940 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:12:54.940 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:12:57.975 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:12:57.975 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:12:57.976 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:12:57.976 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:12:57.977 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:12:57.977 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:13:02.575 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:13:02.577 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:13:02.579 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:13:02.579 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:13:02.581 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:13:02.581 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:13:02.601 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:13:15.925 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:13:15.925 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:13:15.925 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:13:15.926 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:13:15.926 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:13:15.926 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:13:15.949 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:23:03.186 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:23:03.196 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:23:03.198 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:23:03.198 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:23:03.199 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:23:03.200 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:23:06.960 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:23:06.961 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:23:06.962 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:23:06.963 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:23:06.963 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:23:06.964 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:23:11.416 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:23:11.417 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:23:11.419 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:23:11.419 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:23:11.419 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:23:11.419 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:23:11.439 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:23:34.996 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:23:34.996 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:23:34.999 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:23:34.999 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:23:34.999 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:23:35.000 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:23:35.018 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:26:00.997 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:26:00.998 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:26:00.999 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:26:01.000 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:26:01.001 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:26:01.001 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:26:07.542 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:26:07.542 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:26:07.542 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:26:07.542 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:26:07.544 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:26:07.544 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:26:12.078 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:26:12.078 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:26:12.079 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:26:12.079 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:26:12.080 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:26:12.080 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:26:12.110 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:26:51.779 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:26:51.787 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:26:51.789 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:26:51.789 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:26:51.790 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:26:51.790 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:26:55.414 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:26:55.414 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:26:55.414 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:26:55.414 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:26:55.414 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:26:55.414 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:26:55.993 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:26:55.994 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:26:55.994 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:26:55.994 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:26:55.996 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:26:55.996 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:26:58.221 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:26:58.222 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:26:58.223 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:26:58.223 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:26:58.223 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:26:58.223 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:26:58.245 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:27:51.737 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:27:51.737 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:27:51.739 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:27:51.739 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:27:51.740 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:27:51.740 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:27:57.468 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:27:57.468 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:27:57.469 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:27:57.469 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:27:57.470 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:27:57.470 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:27:59.179 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:27:59.180 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:27:59.180 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:27:59.181 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:27:59.181 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:27:59.181 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:27:59.208 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:28:18.029 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:28:18.029 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:28:18.030 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:28:18.030 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:28:18.031 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:28:18.031 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:28:18.061 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:34:01.754 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:34:01.765 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:34:01.766 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:34:01.767 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:34:01.767 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:34:01.767 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:34:05.648 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:34:05.648 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:34:05.649 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:34:05.649 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:34:05.650 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:34:05.650 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:34:10.161 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:34:10.163 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:34:10.165 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:34:10.166 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:34:10.166 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:34:10.167 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:34:10.205 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:34:58.796 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:34:58.797 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:34:58.800 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:34:58.800 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:34:58.801 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:34:58.801 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:35:01.447 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:35:01.447 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:35:01.450 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:35:01.450 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:35:01.451 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:35:01.451 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:35:06.224 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:35:06.225 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:35:06.226 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:35:06.227 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:35:06.227 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:35:06.227 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:35:11.099 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:35:11.100 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:35:11.102 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:35:11.103 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:35:11.103 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:35:11.104 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:35:11.130 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:35:21.339 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:35:21.339 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:35:21.340 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:35:21.340 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:35:21.341 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:35:21.341 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:35:21.360 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:35:58.305 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:35:58.305 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:35:58.306 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:35:58.306 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:35:58.306 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:35:58.306 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:35:58.326 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:38:44.002 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:38:44.002 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:38:44.004 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:38:44.005 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:38:44.005 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:38:44.006 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:38:51.528 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:38:51.528 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:38:51.529 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:38:51.529 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:38:51.530 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:38:51.530 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:38:55.846 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:38:55.846 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:38:55.847 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:38:55.847 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:38:55.848 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:38:55.848 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:38:58.471 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:38:58.472 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:38:58.473 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:38:58.473 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:38:58.474 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:38:58.475 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:38:58.501 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:39:08.860 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:39:08.861 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:39:08.862 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:39:08.862 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:39:08.863 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:39:08.863 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:39:08.879 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:39:17.604 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:39:17.604 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:39:17.606 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:39:17.607 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:39:17.608 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:39:17.609 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:39:17.632 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:40:38.616 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:40:38.616 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:40:38.618 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:40:38.618 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:40:38.618 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:40:38.619 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:40:47.308 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:40:47.308 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:40:47.309 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:40:47.309 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:40:47.310 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:40:47.310 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:40:52.266 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:40:52.267 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:40:52.268 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:40:52.269 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:40:52.270 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:40:52.270 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:42:05.284 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:42:05.285 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:42:05.286 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:42:05.286 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:42:05.286 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:42:05.287 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:42:12.061 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:42:12.061 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:42:12.062 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:42:12.062 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:42:12.062 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:42:12.062 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:43:29.506 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:43:29.507 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:43:29.507 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:43:29.508 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:43:29.508 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:43:29.508 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:43:46.422 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:43:46.423 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:43:46.423 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:43:46.423 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:43:46.424 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:43:46.424 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:43:50.909 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:43:50.910 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:43:50.912 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:43:50.913 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:43:50.913 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:43:50.914 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:46:04.987 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:46:04.988 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:46:04.989 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:46:04.990 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:46:04.990 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:46:04.991 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:46:10.843 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:46:10.843 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:46:10.844 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:46:10.844 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:46:10.845 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:46:10.846 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:47:08.581 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:47:08.581 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:47:08.583 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:47:08.583 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:47:08.583 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:47:08.583 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:47:12.606 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:47:12.607 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:47:12.610 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:47:12.611 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:47:12.616 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:47:12.616 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:47:20.350 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:47:20.351 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:47:20.353 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:47:20.354 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:47:20.354 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:47:20.355 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:47:20.375 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:47:25.334 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:47:25.335 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:47:25.337 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:47:25.337 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:47:25.338 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:47:25.338 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:47:25.354 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:47:38.921 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:47:38.921 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:47:38.924 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:47:38.924 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:47:38.926 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:47:38.926 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:47:38.943 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:48:38.796 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:48:38.797 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:48:38.799 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:48:38.800 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:48:38.801 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:48:38.801 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:48:44.317 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:48:44.318 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:48:44.319 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:48:44.319 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:48:44.319 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:48:44.319 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:48:44.337 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:49:58.838 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:49:58.839 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:49:58.840 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:49:58.840 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:49:58.840 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:49:58.840 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:50:03.047 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:50:03.047 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:50:03.047 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:50:03.048 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:50:03.048 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:50:03.048 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:50:03.877 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:50:03.877 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:50:03.877 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:50:03.879 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:50:03.879 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:50:03.879 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:50:03.902 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:50:33.549 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:50:33.549 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:50:33.550 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:50:33.550 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:50:33.550 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:50:33.550 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:50:37.260 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:50:37.260 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:50:37.261 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:50:37.261 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:50:37.261 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:50:37.261 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:50:37.823 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:50:37.824 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:50:37.824 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:50:37.824 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:50:37.824 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:50:37.824 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:50:37.855 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:51:00.741 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:51:00.741 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:51:00.742 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:51:00.742 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:51:00.742 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:51:00.743 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:51:00.764 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:51:49.909 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:51:49.917 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:51:49.917 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:51:49.917 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:51:49.918 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:51:49.918 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:51:53.447 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:51:53.447 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:51:53.447 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:51:53.448 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:51:53.448 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:51:53.448 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:51:54.111 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:51:54.111 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:51:54.112 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:51:54.112 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:51:54.113 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:51:54.113 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:51:54.138 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 11:52:05.512 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 11:52:05.513 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 11:52:05.515 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 11:52:05.516 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 11:52:05.517 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 11:52:05.517 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 11:52:05.540 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 13:31:43.206 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 13:31:43.216 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 13:31:43.217 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 13:31:43.217 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 13:31:43.217 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 13:31:43.217 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 13:40:18.907 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 13:40:18.907 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 13:40:18.908 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 13:40:18.909 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 13:40:18.909 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 13:40:18.911 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 13:44:07.843 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 13:44:07.844 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 13:44:07.845 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 13:44:07.845 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 13:44:07.845 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 13:44:07.846 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 13:44:13.381 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 13:44:13.381 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 13:44:13.382 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 13:44:13.382 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 13:44:13.382 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 13:44:13.382 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 13:46:08.746 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 13:46:08.747 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 13:46:08.747 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 13:46:08.747 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 13:46:08.749 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 13:46:08.749 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 13:47:01.019 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 13:47:01.020 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 13:47:01.021 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 13:47:01.021 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 13:47:01.022 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 13:47:01.022 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 13:47:32.719 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 13:47:32.721 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 13:47:32.722 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 13:47:32.722 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 13:47:32.723 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 13:47:32.723 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 13:47:34.281 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 13:47:34.282 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 13:47:34.284 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 13:47:34.284 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 13:47:34.285 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 13:47:34.286 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 13:51:28.604 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 13:51:28.604 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 13:51:28.605 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 13:51:28.605 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 13:51:28.606 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 13:51:28.606 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 13:51:44.198 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 13:51:44.198 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 13:51:44.199 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 13:51:44.199 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 13:51:44.200 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 13:51:44.200 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 13:51:46.924 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 13:51:46.925 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 13:51:46.926 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 13:51:46.926 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 13:51:46.927 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 13:51:46.928 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 13:51:52.461 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 13:51:52.462 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 13:51:52.462 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 13:51:52.463 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 13:51:52.463 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 13:51:52.464 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 13:52:56.582 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 13:52:56.583 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 13:52:56.584 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 13:52:56.585 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 13:52:56.587 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 13:52:56.587 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 13:54:59.078 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 13:54:59.078 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 13:54:59.079 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 13:54:59.079 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 13:54:59.080 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 13:54:59.080 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 13:55:14.488 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 13:55:14.489 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 13:55:14.489 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 13:55:14.489 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 13:55:14.490 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 13:55:14.490 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 13:59:01.560 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 13:59:01.560 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 13:59:01.561 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 13:59:01.561 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 13:59:01.561 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 13:59:01.561 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 13:59:05.253 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 13:59:05.253 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 13:59:05.254 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 13:59:05.255 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 13:59:05.255 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 13:59:05.255 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 13:59:07.066 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 13:59:07.067 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 13:59:07.068 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 13:59:07.069 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 13:59:07.069 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 13:59:07.069 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 13:59:15.565 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 13:59:15.566 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 13:59:15.568 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 13:59:15.568 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 13:59:15.569 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 13:59:15.569 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 14:03:50.633 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 14:03:50.633 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 14:03:50.634 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 14:03:50.634 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 14:03:50.634 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 14:03:50.635 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 14:04:31.882 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-26 14:04:31.883 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-26 14:04:31.888 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-26 14:04:31.888 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-26 14:04:31.889 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-26 14:04:31.889 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-26 14:04:31.914 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-26 14:04:31.914 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-26 14:04:31.914 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-26 14:04:31.914 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-26 14:35:28.428 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 14:35:28.429 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 14:35:28.431 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 14:35:28.431 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 14:35:28.432 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 14:35:28.432 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 14:35:40.987 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 14:35:40.987 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 14:35:40.988 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 14:35:40.988 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 14:35:40.989 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 14:35:40.989 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 14:35:42.593 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 14:35:42.594 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 14:35:42.595 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 14:35:42.595 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 14:35:42.595 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 14:35:42.597 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 15:14:08.968 - INFO - [LineCodeController.java:97] : 接收到查询线路代码请求: lineCodeId=17
2025-06-26 15:14:08.993 - INFO - [LineCodeServiceImpl.java:112] : 查询线路代码成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-26 15:14:08.994 - INFO - [LineCodeController.java:101] : 线路代码查询成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-26 15:14:27.928 - INFO - [LineCodeController.java:97] : 接收到查询线路代码请求: lineCodeId=17
2025-06-26 15:14:27.928 - INFO - [LineCodeServiceImpl.java:112] : 查询线路代码成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-26 15:14:27.929 - INFO - [LineCodeController.java:101] : 线路代码查询成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-26 15:27:23.583 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 15:27:23.584 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 15:27:23.587 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 15:27:23.587 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 15:27:23.587 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 15:27:23.587 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 15:27:30.511 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 15:27:30.513 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 15:27:30.514 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 15:27:30.515 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 15:27:30.516 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 15:27:30.516 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 15:27:37.536 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 15:27:37.537 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 15:27:37.537 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 15:27:37.538 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 15:27:37.538 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 15:27:37.538 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 15:49:28.413 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 15:49:28.414 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 15:49:28.417 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 15:49:28.417 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 15:49:28.418 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 15:49:28.419 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 15:49:45.279 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 15:49:45.279 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 15:49:45.281 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 15:49:45.281 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 15:49:45.282 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 15:49:45.282 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 15:49:48.137 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 15:49:48.138 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 15:49:48.141 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 15:49:48.141 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 15:49:48.142 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 15:49:48.142 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 15:49:51.563 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 15:49:51.564 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 15:49:51.566 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 15:49:51.567 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 15:49:51.568 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 15:49:51.568 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 15:50:01.177 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 15:50:01.177 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 15:50:01.177 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 15:50:01.178 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 15:50:01.178 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 15:50:01.178 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 16:02:49.480 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 16:02:49.480 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 16:02:49.480 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 16:02:49.480 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 16:02:49.482 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 16:02:49.482 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 16:03:20.690 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 16:03:20.690 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 16:03:20.691 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 16:03:20.691 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 16:03:20.691 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 16:03:20.691 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 16:09:01.916 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 16:09:01.917 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 16:09:01.918 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 16:09:01.918 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 16:09:01.918 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 16:09:01.918 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 16:42:46.747 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 16:42:46.748 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 16:42:46.751 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 16:42:46.751 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 16:42:46.752 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 16:42:46.752 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 16:45:59.795 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-26 16:45:59.796 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-26 16:45:59.796 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-26 16:45:59.796 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-26 16:45:59.796 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-26 16:45:59.796 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-26 16:45:59.797 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-26 16:45:59.797 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-26 16:45:59.798 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-26 16:45:59.798 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-26 17:18:45.885 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-06-26 17:18:45.886 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-06-26 17:18:45.924 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-26 17:18:45.925 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-26 17:18:46.735 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=30
2025-06-26 17:18:46.736 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 30
2025-06-26 17:18:46.807 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/26/30/simulation_script
2025-06-26 17:18:46.808 - WARN - [CsvUtils.java:41] : CSV目录不存在或不是有效目录: D:\CETWorkSpace\ngap-server/data/26/30/simulation_script
2025-06-26 17:18:46.808 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 1 个分类
2025-06-26 17:18:46.808 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 30, 指标数量: 1
2025-06-26 17:18:46.831 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=30
2025-06-26 17:18:46.831 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 30
2025-06-26 17:18:46.832 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/26/30/simulation_script
2025-06-26 17:18:46.832 - WARN - [SimulationServiceImpl.java:1880] : 仿真输出目录不存在: D:\CETWorkSpace\ngap-server/data/26/30/simulation_script
2025-06-26 17:18:46.835 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 30, 总数据点: 0, 合格数据点: 0, 合格率: 0.00%
2025-06-26 17:18:54.710 - INFO - [SimulationController.java:130] : 接收到获取仿真任务详情请求: simulationId=30
2025-06-26 17:18:54.710 - INFO - [SimulationServiceImpl.java:589] : 获取仿真任务详情成功: ID=30
2025-06-26 17:18:54.710 - INFO - [SimulationController.java:134] : 获取仿真任务详情成功，ID: 30
2025-06-26 17:20:41.739 - INFO - [TaskSchedule.java:38] : 开始删除
2025-06-26 17:20:41.739 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-06-26 17:20:41.740 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-06-26 17:20:41.740 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-06-26 17:20:41.741 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-06-26 17:20:41.741 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-06-26 17:23:24.818 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 17:23:24.819 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 17:23:24.829 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 17:23:24.829 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 17:23:24.831 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 17:23:24.832 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 17:36:09.155 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 17:36:09.155 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 17:36:09.156 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 17:36:09.156 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 17:36:09.156 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 17:36:09.156 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 17:41:13.627 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 17:41:13.629 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 17:41:13.631 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 17:41:13.631 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 17:41:13.632 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 17:41:13.632 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 17:41:18.344 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 17:41:18.345 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 17:41:18.346 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 17:41:18.347 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 17:41:18.349 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 17:41:18.349 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 17:41:21.283 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 17:41:21.284 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 17:41:21.286 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 17:41:21.287 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 17:41:21.288 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 17:41:21.288 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 17:41:21.624 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 17:48:46.817 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 17:48:46.836 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 17:48:46.838 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 17:48:46.838 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 17:48:46.838 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 17:48:46.838 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 17:48:51.250 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 17:48:51.250 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 17:48:51.253 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 17:48:51.253 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 17:48:51.254 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 17:48:51.254 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 17:48:55.403 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 17:48:55.403 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 17:48:55.406 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 17:48:55.406 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 17:48:55.406 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 17:48:55.406 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 17:48:55.738 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 17:50:15.193 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 17:50:15.194 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 17:50:15.196 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 17:50:15.196 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 17:50:15.197 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 17:50:15.198 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 17:52:42.452 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 17:52:42.453 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 17:52:42.454 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 17:52:42.455 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 17:52:42.455 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 17:52:42.455 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 17:53:51.315 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 17:53:51.315 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 17:53:51.318 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 17:53:51.318 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 17:53:51.319 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 17:53:51.319 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 17:53:56.795 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 17:53:56.796 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 17:53:56.798 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 17:53:56.799 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 17:53:56.799 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 17:53:56.799 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 17:54:01.649 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 17:54:01.650 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 17:54:01.653 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 17:54:01.653 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 17:54:01.655 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 17:54:01.655 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 17:54:02.001 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 19:28:24.630 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:28:24.632 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:28:24.637 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:28:24.637 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:28:24.638 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:28:24.638 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:28:28.118 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:28:28.119 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:28:28.121 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:28:28.122 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:28:28.123 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:28:28.123 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:28:30.862 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:28:30.863 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:28:30.864 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:28:30.864 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:28:30.865 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:28:30.865 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:28:31.130 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 19:28:35.251 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:28:35.252 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:28:35.253 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:28:35.254 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:28:35.256 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:28:35.257 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:28:35.571 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 19:28:39.769 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:28:39.769 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:28:39.771 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:28:39.772 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:28:39.773 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:28:39.774 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:28:40.094 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 19:35:56.157 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:35:56.175 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:35:56.176 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:35:56.177 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:35:56.177 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:35:56.177 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:36:09.147 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:36:09.147 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:36:09.148 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:36:09.149 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:36:09.149 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:36:09.149 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:36:15.172 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:36:15.173 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:36:15.175 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:36:15.175 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:36:15.176 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:36:15.176 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:36:18.708 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:36:18.709 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:36:18.710 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:36:18.712 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:36:18.713 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:36:18.713 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:36:19.080 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 19:37:45.686 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:37:45.694 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:37:45.695 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:37:45.696 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:37:45.696 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:37:45.696 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:37:51.515 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:37:51.516 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:37:51.518 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:37:51.520 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:37:51.520 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:37:51.521 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:37:52.353 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:37:52.354 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:37:52.356 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:37:52.356 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:37:52.357 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:37:52.357 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:37:56.517 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:37:56.517 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:37:56.517 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:37:56.518 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:37:56.518 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:37:56.518 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:37:58.244 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:37:58.244 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:37:58.246 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:37:58.247 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:37:58.247 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:37:58.248 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:37:58.512 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 19:39:20.865 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:39:20.867 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:39:20.869 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:39:20.869 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:39:20.870 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:39:20.871 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:39:25.788 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:39:25.789 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:39:25.790 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:39:25.790 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:39:25.791 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:39:25.791 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:39:26.195 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:39:26.196 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:39:26.198 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:39:26.198 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:39:26.199 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:39:26.199 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:39:26.534 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 19:46:26.879 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:46:26.905 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:46:26.907 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:46:26.908 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:46:26.908 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:46:26.909 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:46:31.816 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:46:31.817 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:46:31.819 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:46:31.820 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:46:31.821 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:46:31.821 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:46:34.884 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:46:34.885 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:46:34.886 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:46:34.886 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:46:34.886 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:46:34.886 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:46:37.743 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:46:37.744 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:46:37.744 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:46:37.744 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:46:37.746 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:46:37.746 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:46:46.556 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:46:46.556 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:46:46.558 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:46:46.558 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:46:46.558 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:46:46.558 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:46:46.904 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 19:47:57.979 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:47:57.980 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:47:57.982 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:47:57.983 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:47:57.983 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:47:57.983 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:48:02.022 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:48:02.022 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:48:02.023 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:48:02.023 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:48:02.023 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:48:02.023 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:48:03.195 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 19:48:03.195 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 19:48:03.196 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 19:48:03.196 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 19:48:03.196 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 19:48:03.196 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-26 19:48:03.459 -ERROR - [GlobalExceptionHandler.java:22] : Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "null"
org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:133)
org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-26 20:18:50.439 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-26 20:18:50.440 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-26 20:18:50.504 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-26 20:18:50.504 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-26 20:18:50.504 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-26 20:18:50.504 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-26 20:18:50.506 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-26 20:18:50.506 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-26 20:18:50.507 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-26 20:18:50.507 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-26 20:23:50.573 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-26 20:23:50.573 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-26 20:23:50.574 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-26 20:23:50.575 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-26 20:23:50.575 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-26 20:23:50.575 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
