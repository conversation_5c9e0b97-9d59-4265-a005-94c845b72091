2025-06-21 10:59:01.717 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-21 10:59:01.718 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-21 10:59:01.719 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-21 10:59:01.719 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-21 10:59:01.719 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-21 10:59:01.723 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-21 10:59:01.850 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-21 10:59:01.851 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-21 10:59:01.851 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-21 10:59:01.852 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-21 10:59:08.322 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=11, keyword=, startTime=null, endTime=null)
2025-06-21 10:59:08.323 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 11, keyword: , startTime: null, endTime: null
2025-06-21 10:59:08.418 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-21 10:59:08.419 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-21 10:59:10.494 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=2
2025-06-21 10:59:10.497 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=2, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=1, pageSize=50)
2025-06-21 10:59:10.566 - INFO - [SimulationController.java:294] : 获取台区电压统计报表成功，台区ID: 2, 用户总数: 53, 合格率: 68.69565217391305
2025-06-21 10:59:10.572 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 2, 用户数量: 53
2025-06-21 10:59:23.316 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-21 10:59:23.316 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-21 10:59:23.316 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-21 10:59:23.316 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-21 10:59:23.317 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-21 10:59:23.317 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-21 10:59:23.318 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-21 10:59:23.318 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-21 10:59:23.318 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-21 10:59:23.318 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-21 10:59:25.123 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-06-21 10:59:25.123 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-06-21 10:59:25.124 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-21 10:59:25.125 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-21 10:59:37.675 - INFO - [SimulationController.java:130] : 接收到获取仿真任务详情请求: simulationId=30
2025-06-21 10:59:37.675 - INFO - [SimulationServiceImpl.java:589] : 获取仿真任务详情成功: ID=30
2025-06-21 10:59:37.676 - INFO - [SimulationController.java:134] : 获取仿真任务详情成功，ID: 30
2025-06-21 11:00:10.926 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-21 11:00:10.926 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-21 11:00:10.926 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-21 11:00:10.926 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-21 11:00:10.930 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-21 11:00:10.930 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-21 11:00:10.931 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-21 11:00:10.931 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-21 11:00:10.931 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-21 11:00:10.931 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-21 11:00:14.453 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=21, keyword=, startTime=null, endTime=null)
2025-06-21 11:00:14.453 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 21, keyword: , startTime: null, endTime: null
2025-06-21 11:00:14.584 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 6 条数据
2025-06-21 11:00:14.585 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 6
2025-06-21 11:00:17.604 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=24
2025-06-21 11:00:17.605 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=24, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=1, pageSize=50)
2025-06-21 11:00:17.681 - INFO - [SimulationController.java:294] : 获取台区电压统计报表成功，台区ID: 24, 用户总数: 214, 合格率: 85.25734986694737
2025-06-21 11:00:17.696 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 24, 用户数量: 214
2025-06-21 11:00:24.177 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=24
2025-06-21 11:00:24.178 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 24
2025-06-21 11:00:24.181 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script
2025-06-21 11:00:25.331 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script, 共 216 个子指标
2025-06-21 11:00:25.332 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/21/24/measured_data/工作簿1.xlsx
2025-06-21 11:00:25.332 - INFO - [ExcelUtils.java:50] : 检测到大文件(5.55MB)，使用优化处理器
2025-06-21 11:00:29.430 - INFO - [OptimizedExcelProcessor.java:94] : 指标提取完成: 文件=D:\CETWorkSpace\ngap-server/data/21/24/measured_data/工作簿1.xlsx, 耗时=4079ms, 指标数=956
2025-06-21 11:00:29.438 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 工作簿1.xlsx 的电压指标，共 1 个指标
2025-06-21 11:00:29.439 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-21 11:00:29.439 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 24, 指标数量: 2
2025-06-21 11:00:29.521 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=24
2025-06-21 11:00:29.521 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 24
2025-06-21 11:00:29.525 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script
2025-06-21 11:00:29.525 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script
2025-06-21 11:00:29.526 - INFO - [CsvUtils.java:375] : 找到 216 个CSV文件，开始处理
2025-06-21 11:00:29.611 - INFO - [CsvUtils.java:389] : 完成电压数据提取，处理文件数: 216/216, 总数据点: 23424, 合格数据点: 23161, 合格率: 98.88%
2025-06-21 11:00:29.611 - INFO - [SimulationServiceImpl.java:1914] : 仿真任务 24 电压合格率计算完成：总数据点=23424, 合格数据点=23161, 合格率=98.88%, 处理时间=86ms
2025-06-21 11:00:29.611 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 24, 总数据点: 23424, 合格数据点: 23161, 合格率: 98.88%
2025-06-21 11:00:37.541 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=24, queryDTO=BatchMetricQueryDTO(metricIds=[circuit_Mon_monitor_generator_10_pq_1.P1 (kW)], startTimestamp=null)
2025-06-21 11:00:37.541 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 24, metricIds: [circuit_Mon_monitor_generator_10_pq_1.P1 (kW)]
2025-06-21 11:00:37.542 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 24, metricId: circuit_Mon_monitor_generator_10_pq_1.P1 (kW)
2025-06-21 11:00:37.545 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_pq_1.csv, 指标ID: circuit_Mon_monitor_generator_10_pq_1.P1 (kW)
2025-06-21 11:00:37.558 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_pq_1.csv, 指标: circuit_Mon_monitor_generator_10_pq_1.P1 (kW), 数据点数: 96
2025-06-21 11:00:37.559 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 96
2025-06-21 11:00:37.559 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 24, 指标数量: 1
2025-06-21 11:00:48.199 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=24, queryDTO=BatchMetricQueryDTO(metricIds=[circuit_Mon_monitor_generator_10_pq_1.P1 (kW)], startTimestamp=null)
2025-06-21 11:00:48.199 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 24, metricIds: [circuit_Mon_monitor_generator_10_pq_1.P1 (kW)]
2025-06-21 11:00:48.199 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 24, metricId: circuit_Mon_monitor_generator_10_pq_1.P1 (kW)
2025-06-21 11:00:48.204 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_pq_1.csv, 指标ID: circuit_Mon_monitor_generator_10_pq_1.P1 (kW)
2025-06-21 11:00:48.205 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_pq_1.csv, 指标: circuit_Mon_monitor_generator_10_pq_1.P1 (kW), 数据点数: 96
2025-06-21 11:00:48.205 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 96
2025-06-21 11:00:48.205 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 24, 指标数量: 1
2025-06-21 11:01:09.287 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=24, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4206858070152.用户名称-胡元凯.电压等级-220.相别-1], startTimestamp=null)
2025-06-21 11:01:09.287 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 24, metricIds: [用户电压.用户编号-4206858070152.用户名称-胡元凯.电压等级-220.相别-1]
2025-06-21 11:01:09.287 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 24, metricId: 用户电压.用户编号-4206858070152.用户名称-胡元凯.电压等级-220.相别-1
2025-06-21 11:01:09.292 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4206858070152.用户名称-胡元凯.电压等级-220.相别-1
2025-06-21 11:01:09.292 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/用户电压.csv
2025-06-21 11:01:09.292 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 工作簿1.xlsx
2025-06-21 11:01:09.293 - INFO - [ExcelUtils.java:322] : 检测到大文件(5.55MB)，使用优化处理器获取指标数据
2025-06-21 11:01:09.293 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4206858070152.用户名称-胡元凯.电压等级-220.相别-1
2025-06-21 11:01:12.448 - INFO - [OptimizedExcelProcessor.java:167] : 指标数据获取完成: 指标ID=用户电压.用户编号-4206858070152.用户名称-胡元凯.电压等级-220.相别-1, 耗时=3155ms, 数据点数=2870
2025-06-21 11:01:12.448 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 2870 个数据点
2025-06-21 11:01:12.448 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 2870
2025-06-21 11:01:12.448 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 24, 指标数量: 1
2025-06-21 11:03:19.235 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=24, queryDTO=BatchMetricQueryDTO(metricIds=[circuit_Mon_monitor_generator_10_pq_1.P1 (kW), 用户电压.用户编号-4206858070152.用户名称-胡元凯.电压等级-220.相别-1], startTimestamp=null)
2025-06-21 11:03:19.235 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 24, metricIds: [circuit_Mon_monitor_generator_10_pq_1.P1 (kW), 用户电压.用户编号-4206858070152.用户名称-胡元凯.电压等级-220.相别-1]
2025-06-21 11:03:19.236 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 24, metricId: circuit_Mon_monitor_generator_10_pq_1.P1 (kW)
2025-06-21 11:03:19.240 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_pq_1.csv, 指标ID: circuit_Mon_monitor_generator_10_pq_1.P1 (kW)
2025-06-21 11:03:19.252 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_pq_1.csv, 指标: circuit_Mon_monitor_generator_10_pq_1.P1 (kW), 数据点数: 96
2025-06-21 11:03:19.253 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 24, metricId: 用户电压.用户编号-4206858070152.用户名称-胡元凯.电压等级-220.相别-1
2025-06-21 11:03:19.256 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4206858070152.用户名称-胡元凯.电压等级-220.相别-1
2025-06-21 11:03:19.256 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/用户电压.csv
2025-06-21 11:03:19.256 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 工作簿1.xlsx
2025-06-21 11:03:19.256 - INFO - [ExcelUtils.java:322] : 检测到大文件(5.55MB)，使用优化处理器获取指标数据
2025-06-21 11:03:19.256 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4206858070152.用户名称-胡元凯.电压等级-220.相别-1
2025-06-21 11:03:19.256 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 2870 个数据点
2025-06-21 11:03:19.257 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 2, 失败: 0, 总共获取数据点: 2966
2025-06-21 11:03:19.257 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 24, 指标数量: 2
2025-06-21 11:03:50.313 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=24
2025-06-21 11:03:50.313 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 24
2025-06-21 11:03:50.316 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script
2025-06-21 11:03:50.316 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script
2025-06-21 11:03:50.317 - INFO - [CsvUtils.java:375] : 找到 216 个CSV文件，开始处理
2025-06-21 11:03:51.087 - INFO - [CsvUtils.java:389] : 完成电压数据提取，处理文件数: 216/216, 总数据点: 23424, 合格数据点: 23161, 合格率: 98.88%
2025-06-21 11:03:51.087 - INFO - [SimulationServiceImpl.java:1914] : 仿真任务 24 电压合格率计算完成：总数据点=23424, 合格数据点=23161, 合格率=98.88%, 处理时间=771ms
2025-06-21 11:03:51.087 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 24, 总数据点: 23424, 合格数据点: 23161, 合格率: 98.88%
2025-06-21 11:06:48.027 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=24
2025-06-21 11:06:48.028 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=24, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=1, pageSize=50)
2025-06-21 11:06:48.080 - INFO - [SimulationController.java:294] : 获取台区电压统计报表成功，台区ID: 24, 用户总数: 214, 合格率: 85.25734986694737
2025-06-21 11:06:48.090 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 24, 用户数量: 214
2025-06-21 11:06:51.980 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=24, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=2, pageSize=50)
2025-06-21 11:06:51.996 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 24, 用户数量: 214
2025-06-21 11:06:52.625 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=24, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=3, pageSize=50)
2025-06-21 11:06:52.643 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 24, 用户数量: 214
2025-06-21 11:06:53.144 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=24, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=4, pageSize=50)
2025-06-21 11:06:53.159 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 24, 用户数量: 214
2025-06-21 11:06:53.746 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=24, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=5, pageSize=50)
2025-06-21 11:06:53.760 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 24, 用户数量: 214
2025-06-21 11:06:57.262 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=24, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=4, pageSize=50)
2025-06-21 11:06:57.277 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 24, 用户数量: 214
2025-06-21 11:06:58.616 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=24, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=1, pageSize=100)
2025-06-21 11:06:58.632 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 24, 用户数量: 214
2025-06-21 11:07:02.880 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=24, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=2, pageSize=100)
2025-06-21 11:07:02.895 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 24, 用户数量: 214
2025-06-21 11:07:10.859 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=24, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=3, pageSize=100)
2025-06-21 11:07:10.874 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 24, 用户数量: 214
2025-06-21 17:12:42.670 - INFO - [TaskSchedule.java:38] : 开始删除
2025-06-21 17:12:42.676 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-06-21 17:12:42.711 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-06-21 17:12:42.711 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-06-21 17:12:42.741 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-06-21 17:12:42.741 - INFO - [TaskSchedule.java:41] : 删除完毕
