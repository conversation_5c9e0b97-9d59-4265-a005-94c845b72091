2025-07-01 08:37:28.044 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-07-01 08:37:28.044 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-07-01 08:37:28.047 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-07-01 08:37:28.047 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-07-01 08:37:28.047 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-07-01 08:37:28.059 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-07-01 08:37:28.079 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 9 条数据
2025-07-01 08:37:28.079 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-07-01 08:37:28.080 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 9 个项目
2025-07-01 08:37:28.080 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 9
2025-07-01 08:37:31.070 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-07-01 08:37:31.071 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-07-01 08:37:31.101 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 2 条数据
2025-07-01 08:37:31.102 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 2
2025-07-01 08:37:38.050 - INFO - [SimulationController.java:130] : 接收到获取仿真任务详情请求: simulationId=30
2025-07-01 08:37:38.051 - INFO - [SimulationServiceImpl.java:589] : 获取仿真任务详情成功: ID=30
2025-07-01 08:37:38.051 - INFO - [SimulationController.java:134] : 获取仿真任务详情成功，ID: 30
2025-07-01 08:37:42.511 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-01 08:37:42.511 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-01 08:37:42.521 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-01 08:37:42.521 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-01 08:37:42.521 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-01 08:37:42.521 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-01 08:37:46.182 - INFO - [SimulationController.java:102] : 接收到更新仿真任务请求: simulationId=30, simulationModel=, simulationDraft={"x":0,"y":0,"scale":1,"pens":[{"hiddenText":true,"disableInput":true,"width":100,"height":100,"ratio":true,"name":"image","deviceType":"battery","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_163)'%3e%3crect%20x='3.5'%20y='0.5'%20width='17'%20height='23'%20stroke='black'/%3e%3cpath%20d='M1%208V16'%20stroke='black'/%3e%3cpath%20d='M23%208V16'%20stroke='black'/%3e%3cpath%20d='M10.3333%207H15.7879L12.4545%2011.2H17L9.72727%2019L11.2424%2013.3H7L10.3333%207Z'%20stroke='black'%20stroke-linejoin='round'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_163'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"56c33350","x":0.5,"y":1,"penId":"342bca"}],"simulationConfig":{"phases":3,"baseKV":0,"pu":1,"customScript":""},"id":"342bca","children":[],"x":318,"y":97,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"text":"342bca"},{"hiddenText":false,"disableInput":true,"width":100,"height":100,"ratio":true,"name":"name","deviceType":"name","image":"","anchors":[{"id":"56c33350","x":0.5,"y":1,"penId":"342bca"}],"simulationConfig":{"phases":3,"baseKV":0,"pu":1,"customScript":""},"id":"name_342bca","children":[],"x":318,"y":177,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"text":"342bca","locked":10}],"origin":{"x":0,"y":0},"center":{"x":0,"y":0},"paths":{},"template":"7d6fa740","component":false,"autoSizeinPc":true,"rule":true,"ruleColor":"#888888","gridColor":"#e2e2e2","gridSize":20,"color":"#222222","activeColor":"#278df8","toArrow":"line","simulationConfig":{"name":"","number":24,"stepSize":"1h","mode":"daily","hour":0,"sec":0,"voltageBases":[0.22,0.38,10],"defaultBaseFrequency":60},"version":"1.0.68","dataPoints":[]}, simulationScript=, nodes=, controlStrategy={}
2025-07-01 08:37:46.184 - INFO - [SimulationServiceImpl.java:336] : 仿真任务draft更新: 30
2025-07-01 08:37:46.370 - INFO - [SimulationServiceImpl.java:380] : 仿真任务更新成功: ID=30
2025-07-01 08:37:46.370 - INFO - [SimulationController.java:108] : 仿真任务更新成功，ID: 30, 更新后模型: {"x":176,"y":48,"scale":0.46999999999999953,"pens":[{"width":47.**************,"height":47.**************,"ratio":true,"name":"image","deviceType":"battery","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_163)'%3e%3crect%20x='3.5'%20y='0.5'%20width='17'%20height='23'%20stroke='black'/%3e%3cpath%20d='M1%208V16'%20stroke='black'/%3e%3cpath%20d='M23%208V16'%20stroke='black'/%3e%3cpath%20d='M10.3333%207H15.7879L12.4545%2011.2H17L9.72727%2019L11.2424%2013.3H7L10.3333%207Z'%20stroke='black'%20stroke-linejoin='round'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_163'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"1666a7d","x":0.5,"y":1,"penId":"5cef529"}],"simulationConfig":{"phases":3,"baseKV":0,"pu":1,"customScript":""},"id":"5cef529","children":[],"x":14.**************,"y":74.065035291926,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"49a81b","lineAnchor":"6a65a3dd","anchor":"1666a7d"}],"ex":61.**************,"ey":121.065035291926,"center":{"x":38.**************,"y":97.565035291926}},{"width":47.**************,"height":47.**************,"ratio":true,"name":"image","deviceType":"regulator","image":"data:image/svg+xml,%3csvg%20width='25'%20height='25'%20viewBox='0%200%2025%2025'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='12.1406'%20cy='15.1758'%20r='5.47461'%20stroke='black'/%3e%3ccircle%20cx='12.1406'%20cy='10.0381'%20r='5.47461'%20transform='rotate(-180%2012.1406%2010.0381)'%20stroke='black'/%3e%3cpath%20d='M12.1406%2023.7734V21.2559'%20stroke='black'%20stroke-linecap='square'/%3e%3cpath%20d='M12.1406%201.44043L12.1406%203.95801'%20stroke='black'%20stroke-linecap='square'/%3e%3cpath%20d='M1.00276%2019.1217L0.593417%2019.4098L1.15728%2020.2342L1.56662%2019.9461L1.00276%2019.1217ZM23.481%204.33965C23.5307%204.06614%2023.3522%203.80515%2023.0823%203.75671L18.683%202.9674C18.4131%202.91897%2018.1539%203.10143%2018.1041%203.37494C18.0544%203.64846%2018.2329%203.90945%2018.5029%203.95788L22.4133%204.65949L21.6926%208.62141C21.6428%208.89492%2021.8213%209.15591%2022.0913%209.20435C22.3612%209.25278%2022.6204%209.07032%2022.6702%208.79681L23.481%204.33965ZM1.28469%2019.5339L1.56662%2019.9461L23.2741%204.66415L22.9922%204.25195L22.7103%203.83975L1.00276%2019.1217L1.28469%2019.5339Z'%20fill='black'/%3e%3c/svg%3e","anchors":[{"id":"58e49c86","x":0.5,"y":0,"background":"blue","flag":"wind1","penId":"287182e7"},{"id":"210eb168","x":0.5,"y":1,"background":"red","flag":"wind2","penId":"287182e7"}],"simulationConfig":{"name":"","buses":[],"phases":3,"windings":2,"XHL":0.01,"customScript":"","bank":"","maxTap":1.1,"minTap":0.9,"numTaps":32,"percentLoadloss":0,"wdgs":[{"wdg":1,"buses":"","conn":"DELT","kv":0.4,"kva":100,"percentR":0.1,"tap":0,"node":{"name":"node_3"}},{"wdg":2,"buses":"","conn":"WYE","kv":0.4,"kva":100,"percentR":0.1,"tap":0,"node":{"name":"node_4"}}],"control":{"name":"","transformer":"","winding":1,"vreg":11,"band":0,"ptratio":20,"enabled":true}},"id":"287182e7","children":[],"x":247.**************,"y":209.*************,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"706488f1","lineAnchor":"a4c2d47","anchor":"58e49c86"},{"lineId":"63ebe3a0","lineAnchor":"9838094","anchor":"210eb168"}],"ex":294.************,"ey":256.*************,"center":{"x":270.************,"y":232.*************},"textTop":60,"text":"Regulator_1"},{"width":47.**************,"height":47.**************,"ratio":true,"name":"image","deviceType":"transformer","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='12'%20cy='8'%20r='5.5'%20stroke='black'/%3e%3ccircle%20cx='12'%20cy='16'%20r='5.5'%20stroke='black'/%3e%3cpath%20d='M12%200V2'%20stroke='black'/%3e%3cpath%20d='M12%2022V24'%20stroke='black'/%3e%3c/svg%3e","anchors":[{"id":"fc33b0e","x":0.5,"y":0,"background":"blue","flag":"wind1","penId":"890fa2a"},{"id":"d8a12ed","x":0.5,"y":1,"background":"red","flag":"wind2","penId":"890fa2a"}],"simulationConfig":{"name":"","buses":[],"phases":3,"windings":2,"XHL":0.01,"customScript":"","bank":"","maxTap":1.1,"minTap":0.9,"numTaps":32,"percentLoadloss":0.1,"wdgs":[{"wdg":1,"buses":"","conn":"DELT","kv":10,"kva":100,"percentR":0.1,"tap":0,"node":{"name":"node_1"}},{"wdg":2,"buses":"","conn":"WYE","kv":0.4,"kva":100,"percentR":0.1,"tap":0,"node":{"name":"node_2"}}]},"id":"890fa2a","children":[],"x":14.**************,"y":144.*************,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"49a81b","lineAnchor":"54d0bcd","anchor":"fc33b0e"},{"lineId":"7a57851","lineAnchor":"3bb5a106","anchor":"d8a12ed"}],"ex":61.**************,"ey":191.*************,"center":{"x":38.**************,"y":168.*************},"textTop":60,"text":"Transformer_2"},{"id":"49a81b","name":"line","lineName":"line","x":38.**************,"y":121.**************,"type":1,"toArrow":"line","lineWidth":1,"length":23.**************,"ex":38.**************,"ey":144.**************,"width":0,"height":23.**************,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"6a65a3dd","x":0,"y":0,"penId":"49a81b","connectTo":"5cef529","anchorId":"1666a7d","start":true,"lineLength":23.**************},{"x":0,"y":1,"id":"54d0bcd","penId":"49a81b","connectTo":"890fa2a","anchorId":"fc33b0e"}],"center":{"x":38.**************,"y":132.86299447559944},"rotate":0},{"anchors":[{"id":"31c10c2","x":0,"y":0.5,"background":"blue","penId":"259b0c3"},{"id":"46b83690","x":1,"y":0.5,"penId":"259b0c3"}],"width":117.5000000000001,"height":2.5849999999999986,"name":"rectangle","lineWidth":0,"background":"#222222","deviceType":"motherLine","simulationConfig":{"1":3,"phases":3,"lineCode":18},"id":"259b0c3","children":[],"x":73.14435295104545,"y":207.7800352919251,"fontSize":24,"lineHeight":1.5,"ex":190.64435295104556,"ey":210.3650352919251,"center":{"x":131.8943529510455,"y":209.07253529192508},"connectedLines":[{"lineId":"7a57851","lineAnchor":"792ffa8","anchor":"31c10c2"},{"lineId":"706488f1","lineAnchor":"65d27bc","anchor":"46b83690"}],"rotate":0},{"id":"7a57851","name":"line","lineName":"line","x":38.**************,"y":191.66095365927296,"type":1,"toArrow":"line","lineWidth":1,"length":39.1008639903021,"ex":73.14435295104579,"ey":209.07253529192587,"width":35.01020408163265,"height":17.41158163265291,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"3bb5a106","x":0,"y":0,"penId":"7a57851","connectTo":"890fa2a","anchorId":"d8a12ed","start":true,"lineLength":39.1008639903021},{"x":1,"y":1,"id":"792ffa8","penId":"7a57851","connectTo":"259b0c3","anchorId":"31c10c2"}],"center":{"x":55.639250910229464,"y":200.3667444755994},"rotate":0},{"id":"706488f1","name":"line","lineName":"line","x":190.64435295104516,"y":209.*************,"type":1,"toArrow":"line","lineWidth":1,"length":80.13979591836988,"ex":270.78414886941505,"ey":209.*************,"width":80.13979591836988,"height":0,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"65d27bc","x":0,"y":0,"penId":"706488f1","connectTo":"259b0c3","anchorId":"46b83690","start":true,"lineLength":80.13979591836988},{"x":1,"y":0,"id":"a4c2d47","penId":"706488f1","connectTo":"287182e7","anchorId":"58e49c86"}],"center":{"x":230.7142509102301,"y":209.*************},"rotate":0},{"anchors":[{"id":"6463915","x":0,"y":0.5,"background":"blue","penId":"19c450b2"},{"id":"2a06cc41","x":1,"y":0.5,"penId":"19c450b2"}],"width":281.9999999999997,"height":2.349999999999998,"name":"rectangle","lineWidth":0,"background":"#222222","deviceType":"motherLine","simulationConfig":{"1":1,"phases":3,"lineCode":18},"id":"19c450b2","children":[],"x":270.78414886941505,"y":303.6791666666667,"fontSize":24,"lineHeight":1.5,"rotate":0,"ex":552.7841488694148,"ey":306.0291666666667,"center":{"x":411.78414886941493,"y":304.8541666666667},"connectedLines":[{"lineId":"63ebe3a0","lineAnchor":"6919766","anchor":"6463915"},{"lineId":"65170652","lineAnchor":"57e60302","anchor":"2a06cc41"}]},{"id":"63ebe3a0","name":"line","lineName":"line","x":270.************,"y":256.*************,"type":1,"toArrow":"line","lineWidth":1,"length":48.781631374740925,"ex":270.78414886941505,"ey":304.85416666666663,"width":3.069544618483633e-12,"height":48.781631374740925,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"9838094","x":0,"y":0,"penId":"63ebe3a0","connectTo":"287182e7","anchorId":"210eb168","start":true,"lineLength":48.781631374740925},{"x":1,"y":1,"id":"6919766","penId":"63ebe3a0","connectTo":"19c450b2","anchorId":"6463915"}],"rotate":0,"center":{"x":270.7841488694135,"y":280.4633509792962}},{"width":46.99999999999996,"height":46.99999999999996,"ratio":true,"name":"image","deviceType":"load","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_157)'%3e%3ccircle%20cx='12'%20cy='11.5'%20r='11'%20stroke='black'/%3e%3cpath%20d='M4%204L20%2019'%20stroke='black'/%3e%3cpath%20d='M4%2019L20%204'%20stroke='black'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_157'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"96cf50","x":0.5,"y":0,"penId":"2e5595a"}],"simulationConfig":{"name":"","buses":[],"phases":3,"phasePositions":[],"kw":1,"kvar":0,"kv":12.47,"model":1,"customScript":"","outputMode":["VI","PQ"]},"loadShapeConfig":{"name":"","mult":[],"interval":1,"divisor":1},"id":"2e5595a","children":[],"x":526.75,"y":378.29166666666663,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"65170652","lineAnchor":"59de52d7","anchor":"96cf50"}],"textTop":60,"text":"load_1","ex":573.75,"ey":425.2916666666666,"center":{"x":550.25,"y":401.79166666666663}},{"id":"65170652","name":"line","lineName":"line","x":550.25,"y":304.85416666666663,"type":1,"toArrow":"line","lineWidth":1,"length":73.48121063743,"ex":552.7841488694147,"ey":378.29166666666663,"width":2.5341488694147074,"height":73.4375,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"57e60302","x":1,"y":0,"penId":"65170652","connectTo":"19c450b2","anchorId":"2a06cc41","start":true,"lineLength":73.48121063743},{"x":0,"y":1,"id":"59de52d7","penId":"65170652","connectTo":"2e5595a","anchorId":"96cf50"}],"rotate":0,"center":{"x":551.5170744347074,"y":341.57291666666663}}],"origin":{"x":1694.8414215966984,"y":173.47003529192597},"center":{"x":280,"y":204},"paths":{},"template":"454b508d","component":false,"autoSizeinPc":true,"rule":true,"ruleColor":"#888888","gridColor":"#e2e2e2","gridSize":20,"color":"#222222","activeColor":"#278df8","toArrow":"line","simulationConfig":{"name":"","number":1,"stepSize":"1h","mode":"snapshot","hour":0,"sec":0,"voltageBases":[0.22,0.38,10],"defaultBaseFrequency":50},"version":"1.0.68","dataPoints":[]}, 更新后脚本: ﻿clear

new Circuit.circuit phases=3 bus=node_1
~ basekv=0 pu=1


set defaultbasefrequency=50
set mode=snapshot stepsize=1h number=1 hour=0 sec=0
set voltagebases=[0.22,0.38,10] 


new Transformer.Regulator_1
~ phases=3 windings=2 bank=RegControl_4_Regulator_1
~ XHL=0.01 %loadloss=0
~ maxtap=1.1 mintap=0.9 numtaps=32
~ wdg=1 bus=node_3 kv=0.4 kva=100 conn=DELT %R=0.1 tap=0
~ wdg=2 bus=node_4 kv=0.4 kva=100 conn=WYE %R=0.1 tap=0

new RegControl.RegControl_4_Regulator_1 transformer=Regulator_1 winding=1
~ vreg=11 band=0 ptratio=20
~ enabled=true
new Transformer.Transformer_2
~ phases=3 windings=2 
~ XHL=0.01 %loadloss=0.1
~ maxtap=1.1 mintap=0.9 numtaps=32
~ wdg=1 bus=node_1 kv=10 kva=100 conn=DELT %R=0.1 tap=0
~ wdg=2 bus=node_2 kv=0.4 kva=100 conn=WYE %R=0.1 tap=0















new LineCode.LGJ25
~ nphases=3 basefreq=50 units=km R1=1.37 X1=0.38

new LineCode.JKLYJ35
~ nphases=3 basefreq=50 units=km R1=0.3238 X1=0.303

new LineCode.JKLYJ70
~ nphases=3 basefreq=50 units=km R1=0.46 X1=0.398




new Line.259b0c3__0 phases=3 bus1=node_2 bus2=node_3
~ lineCode=JKLYJ35 length=3
new Line.19c450b2__0 phases=3 bus1=node_4 bus2=node_5
~ lineCode=JKLYJ35 length=1


//负荷相关
new Load.load_1 phases=3 bus1=node_5
~ model=1 kv=12.47 kvar=0 kw=1



//光伏相关


//储能相关


//关键参数监控
New Monitor.monitor_load_1_VI  element=Load.load_1 terminal=1 mode=0 ppolar=0
New Monitor.monitor_load_1_PQ  element=Load.load_1 terminal=1 mode=1 ppolar=0


calcv
solve
export monitor monitor_load_1_VI
export monitor monitor_load_1_PQ

2025-07-01 08:39:34.274 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-07-01 08:39:34.275 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-07-01 08:39:34.275 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-07-01 08:39:34.275 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-07-01 08:39:34.275 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-07-01 08:39:34.275 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-07-01 08:39:34.275 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 9 条数据
2025-07-01 08:39:34.276 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-07-01 08:39:34.276 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 9 个项目
2025-07-01 08:39:34.276 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 9
2025-07-01 08:39:36.084 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-07-01 08:39:36.085 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-07-01 08:39:36.087 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 2 条数据
2025-07-01 08:39:36.087 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 2
2025-07-01 08:39:38.112 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=33
2025-07-01 08:39:38.113 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 33
2025-07-01 08:39:38.113 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 08:39:38.113 - WARN - [CsvUtils.java:41] : CSV目录不存在或不是有效目录: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 08:39:38.114 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋.xlsx
2025-07-01 08:39:38.114 - INFO - [ExcelUtils.java:50] : 检测到大文件(6.21MB)，使用优化处理器
2025-07-01 08:39:40.591 - INFO - [OptimizedExcelProcessor.java:94] : 指标提取完成: 文件=D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋.xlsx, 耗时=2476ms, 指标数=1528
2025-07-01 08:39:40.592 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 荆门沙洋.xlsx 的电压指标，共 1 个指标
2025-07-01 08:39:40.592 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-07-01 08:39:40.592 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 33, 指标数量: 2
2025-07-01 08:39:40.639 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=33
2025-07-01 08:39:40.639 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 33
2025-07-01 08:39:40.640 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 08:39:40.640 - WARN - [SimulationServiceImpl.java:1880] : 仿真输出目录不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 08:39:40.640 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 33, 总数据点: 0, 合格数据点: 0, 合格率: 0.00%
2025-07-01 08:40:16.585 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-07-01 08:40:16.586 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-07-01 08:40:16.585 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-07-01 08:40:16.586 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-07-01 08:40:16.586 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-07-01 08:40:16.587 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-07-01 08:40:16.587 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 9 条数据
2025-07-01 08:40:16.587 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-07-01 08:40:16.588 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 9 个项目
2025-07-01 08:40:16.588 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 9
2025-07-01 08:43:15.018 - INFO - [SimulationController.java:130] : 接收到获取仿真任务详情请求: simulationId=25
2025-07-01 08:43:15.034 - INFO - [SimulationServiceImpl.java:589] : 获取仿真任务详情成功: ID=25
2025-07-01 08:43:15.035 - INFO - [SimulationController.java:134] : 获取仿真任务详情成功，ID: 25
2025-07-01 08:43:18.832 - INFO - [SimulationController.java:102] : 接收到更新仿真任务请求: simulationId=25, simulationModel=, simulationDraft={"x":377,"y":52,"scale":0.60**************,"pens":[{"hiddenText":true,"disableInput":true,"width":60.**************,"height":60.**************,"ratio":true,"name":"image","deviceType":"battery","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_163)'%3e%3crect%20x='3.5'%20y='0.5'%20width='17'%20height='23'%20stroke='black'/%3e%3cpath%20d='M1%208V16'%20stroke='black'/%3e%3cpath%20d='M23%208V16'%20stroke='black'/%3e%3cpath%20d='M10.3333%207H15.7879L12.4545%2011.2H17L9.72727%2019L11.2424%2013.3H7L10.3333%207Z'%20stroke='black'%20stroke-linejoin='round'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_163'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"2e617a1d","x":0.5,"y":1,"penId":"e8c51f2"}],"simulationConfig":{"phases":3,"baseKV":0,"pu":1,"customScript":""},"id":"e8c51f2","children":[],"x":-57.**************,"y":43,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"text":"e8c51f2","connectedLines":[{"lineId":"535af70","lineAnchor":"00b16b3","anchor":"2e617a1d"}]},{"hiddenText":false,"disableInput":true,"width":60.**************,"height":60.**************,"ratio":true,"name":"name","deviceType":"name","image":"","anchors":[{"id":"2e617a1d","x":0.5,"y":1,"penId":"e8c51f2"}],"simulationConfig":{"phases":3,"baseKV":0,"pu":1,"customScript":""},"id":"name_e8c51f2","children":[],"x":-58,"y":91,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"text":"e8c51f2","locked":10},{"hiddenText":true,"disableInput":true,"width":60.**************,"height":60.**************,"ratio":true,"name":"image","deviceType":"regulator","image":"data:image/svg+xml,%3csvg%20width='25'%20height='25'%20viewBox='0%200%2025%2025'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='12.1406'%20cy='15.1758'%20r='5.47461'%20stroke='black'/%3e%3ccircle%20cx='12.1406'%20cy='10.0381'%20r='5.47461'%20transform='rotate(-180%2012.1406%2010.0381)'%20stroke='black'/%3e%3cpath%20d='M12.1406%2023.7734V21.2559'%20stroke='black'%20stroke-linecap='square'/%3e%3cpath%20d='M12.1406%201.44043L12.1406%203.95801'%20stroke='black'%20stroke-linecap='square'/%3e%3cpath%20d='M1.00276%2019.1217L0.593417%2019.4098L1.15728%2020.2342L1.56662%2019.9461L1.00276%2019.1217ZM23.481%204.33965C23.5307%204.06614%2023.3522%203.80515%2023.0823%203.75671L18.683%202.9674C18.4131%202.91897%2018.1539%203.10143%2018.1041%203.37494C18.0544%203.64846%2018.2329%203.90945%2018.5029%203.95788L22.4133%204.65949L21.6926%208.62141C21.6428%208.89492%2021.8213%209.15591%2022.0913%209.20435C22.3612%209.25278%2022.6204%209.07032%2022.6702%208.79681L23.481%204.33965ZM1.28469%2019.5339L1.56662%2019.9461L23.2741%204.66415L22.9922%204.25195L22.7103%203.83975L1.00276%2019.1217L1.28469%2019.5339Z'%20fill='black'/%3e%3c/svg%3e","anchors":[{"id":"a83d49b","x":0.5,"y":0,"background":"blue","flag":"wind1","penId":"3b3e6292"},{"id":"c98d1af","x":0.5,"y":1,"background":"red","flag":"wind2","penId":"3b3e6292"}],"simulationConfig":{"name":"","buses":[],"phases":3,"windings":2,"XHL":0,"customScript":"","bank":"","maxTap":1.1,"minTap":0.9,"numTaps":32,"percentLoadloss":0,"wdgs":[{"wdg":1,"buses":"","conn":"WYE","kv":12.47,"kva":0,"percentR":0,"tap":0,"node":{"name":"node_1"}},{"wdg":2,"buses":"","conn":"WYE","kv":12.47,"kva":0,"percentR":0,"tap":0,"node":{"name":"node_2"}}],"control":{"name":"","transformer":"","winding":1,"vreg":120,"band":3,"ptratio":60,"enabled":true}},"id":"3b3e6292","children":[],"x":-57.**************,"y":151,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"text":"3b3e6292","connectedLines":[{"lineId":"535af70","lineAnchor":"7e62466","anchor":"a83d49b"},{"lineId":"7bc76012","lineAnchor":"6535b0ca","anchor":"c98d1af"}]},{"hiddenText":false,"disableInput":true,"width":60.**************,"height":60.**************,"ratio":true,"name":"name","deviceType":"name","image":"","anchors":[{"id":"a83d49b","x":0.5,"y":0,"background":"blue","flag":"wind1","penId":"3b3e6292"},{"id":"c98d1af","x":0.5,"y":1,"background":"red","flag":"wind2","penId":"3b3e6292"}],"simulationConfig":{"name":"","buses":[],"phases":3,"windings":2,"XHL":0,"customScript":"","bank":"","maxTap":1.1,"minTap":0.9,"numTaps":32,"percentLoadloss":0,"wdgs":[{"wdg":1,"buses":"","conn":"WYE","kv":12.47,"kva":0,"percentR":0,"tap":0,"node":{"name":"node_1"}},{"wdg":2,"buses":"","conn":"WYE","kv":12.47,"kva":0,"percentR":0,"tap":0,"node":{"name":"node_2"}}],"control":{"name":"","transformer":"","winding":1,"vreg":120,"band":3,"ptratio":60,"enabled":true}},"id":"name_3b3e6292","children":[],"x":-58,"y":199,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"text":"3b3e6292","locked":10},{"id":"535af70","name":"line","lineName":"line","x":-27.**************,"y":103,"type":1,"toArrow":"line","lineWidth":1,"length":48,"ex":-27.**************,"ey":151,"width":0,"height":48,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"00b16b3","x":0,"y":0,"penId":"535af70","connectTo":"e8c51f2","anchorId":"2e617a1d","start":true,"lineLength":48},{"x":0,"y":1,"id":"7e62466","penId":"535af70","connectTo":"3b3e6292","anchorId":"a83d49b"}],"rotate":0},{"hiddenText":true,"disableInput":true,"anchors":[{"id":"ed25cde","x":0,"y":0.5,"background":"blue","penId":"17be75d2"},{"id":"cceaf82","x":1,"y":0.5,"penId":"17be75d2"}],"width":360.00000000000006,"height":3.0000000000000004,"name":"rectangle","lineWidth":0,"background":"#222222","deviceType":"motherLine","simulationConfig":{"phases":3},"id":"17be75d2","children":[],"x":-111.00000000000003,"y":277.5,"fontSize":24,"lineHeight":1.5,"rotate":0,"text":"17be75d2","connectedLines":[{"lineId":"7bc76012","lineAnchor":"ed900c0","anchor":"ed25cde"}]},{"hiddenText":false,"disableInput":true,"anchors":[{"id":"ed25cde","x":0,"y":0.5,"background":"blue","penId":"17be75d2"},{"id":"cceaf82","x":1,"y":0.5,"penId":"17be75d2"}],"width":360.00000000000006,"height":3.0000000000000004,"name":"name","lineWidth":0,"background":"#222222","deviceType":"name","simulationConfig":{"phases":3},"id":"name_17be75d2","children":[],"x":-111.00000000000003,"y":297,"fontSize":24,"lineHeight":1.5,"rotate":0,"text":"17be75d2","image":"","locked":10},{"id":"7bc76012","name":"line","lineName":"line","x":-111.00000000000003,"y":211,"type":1,"toArrow":"line","lineWidth":1,"length":107.29864864013902,"ex":-27.999999999999986,"ey":279,"width":83.00000000000004,"height":68,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"6535b0ca","x":1,"y":0,"penId":"7bc76012","connectTo":"3b3e6292","anchorId":"c98d1af","start":true,"lineLength":107.29864864013902},{"x":0,"y":1,"id":"ed900c0","penId":"7bc76012","connectTo":"17be75d2","anchorId":"ed25cde"}],"rotate":0}],"origin":{"x":32.14999999999998,"y":115.44999999999985},"center":{"x":608,"y":250},"paths":{},"template":"0f0305f","component":false,"autoSizeinPc":true,"rule":true,"ruleColor":"#888888","gridColor":"#e2e2e2","gridSize":20,"color":"#222222","activeColor":"#278df8","toArrow":"line","simulationConfig":{"name":"","number":24,"stepSize":"1h","mode":"daily","hour":0,"sec":0,"voltageBases":[0.22,0.38,10],"defaultBaseFrequency":60},"version":"1.0.68","dataPoints":[]}, simulationScript=, nodes=, controlStrategy={}
2025-07-01 08:43:18.833 - INFO - [SimulationServiceImpl.java:336] : 仿真任务draft更新: 25
2025-07-01 08:43:18.986 - INFO - [SimulationServiceImpl.java:380] : 仿真任务更新成功: ID=25
2025-07-01 08:43:18.987 - INFO - [SimulationController.java:108] : 仿真任务更新成功，ID: 25, 更新后模型: {"x":0,"y":0,"scale":0.8,"pens":[{"hiddenText":true,"disableInput":true,"width":80.**************,"height":80.**************,"ratio":true,"name":"image","deviceType":"battery","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_163)'%3e%3crect%20x='3.5'%20y='0.5'%20width='17'%20height='23'%20stroke='black'/%3e%3cpath%20d='M1%208V16'%20stroke='black'/%3e%3cpath%20d='M23%208V16'%20stroke='black'/%3e%3cpath%20d='M10.3333%207H15.7879L12.4545%2011.2H17L9.72727%2019L11.2424%2013.3H7L10.3333%207Z'%20stroke='black'%20stroke-linejoin='round'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_163'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"5c6b048","x":0.5,"y":1,"penId":"722e61f1"}],"simulationConfig":{"phases":3,"baseKV":0,"pu":1,"customScript":""},"id":"722e61f1","children":[],"x":278.*************,"y":-37.866666666666674,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"text":"722e61f1","connectedLines":[{"lineId":"1532693","lineAnchor":"3ae5176","anchor":"5c6b048"}],"ex":358.*************,"ey":42.13333333333334,"center":{"x":318.*************,"y":2.133333333333333}},{"hiddenText":false,"disableInput":true,"width":80.**************,"height":80.**************,"ratio":true,"name":"name","deviceType":"name","image":"","anchors":[{"id":"5c6b048","x":0.5,"y":1,"penId":"722e61f1"}],"simulationConfig":{"phases":3,"baseKV":0,"pu":1,"customScript":""},"id":"name_722e61f1","children":[],"x":278.*************,"y":26.133333333333383,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"text":"722e61f1","locked":10,"ex":358.*************,"ey":106.1333333333334,"center":{"x":318.*************,"y":66.13333333333338}},{"hiddenText":true,"disableInput":true,"width":80.**************,"height":80.**************,"ratio":true,"name":"image","deviceType":"transformer","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='12'%20cy='8'%20r='5.5'%20stroke='black'/%3e%3ccircle%20cx='12'%20cy='16'%20r='5.5'%20stroke='black'/%3e%3cpath%20d='M12%200V2'%20stroke='black'/%3e%3cpath%20d='M12%2022V24'%20stroke='black'/%3e%3c/svg%3e","anchors":[{"id":"95eb19b","x":0.5,"y":0,"background":"blue","flag":"wind1","penId":"b17228a"},{"id":"6212bfe3","x":0.5,"y":1,"background":"red","flag":"wind2","penId":"b17228a"}],"simulationConfig":{"name":"","buses":[],"phases":3,"windings":2,"XHL":0,"customScript":"","bank":"","maxTap":1.1,"minTap":0.9,"numTaps":32,"percentLoadloss":0,"wdgs":[{"wdg":1,"buses":"","conn":"WYE","kv":12.47,"kva":0,"percentR":0,"tap":0,"node":{"name":"node_1"}},{"wdg":2,"buses":"","conn":"WYE","kv":12.47,"kva":0,"percentR":0,"tap":0,"node":{"name":"node_2"}}]},"id":"b17228a","children":[],"x":477,"y":40.**************,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"text":"b17228a","connectedLines":[{"lineId":"1532693","lineAnchor":"7831cea","anchor":"95eb19b"},{"lineId":"a5cda6","lineAnchor":"bc163cd","anchor":"6212bfe3"}],"ex":557,"ey":120.**************,"center":{"x":517,"y":80.**************}},{"hiddenText":false,"disableInput":true,"width":80.**************,"height":80.**************,"ratio":true,"name":"name","deviceType":"name","image":"","anchors":[{"id":"95eb19b","x":0.5,"y":0,"background":"blue","flag":"wind1","penId":"b17228a"},{"id":"6212bfe3","x":0.5,"y":1,"background":"red","flag":"wind2","penId":"b17228a"}],"simulationConfig":{"name":"","buses":[],"phases":3,"windings":2,"XHL":0,"customScript":"","bank":"","maxTap":1.1,"minTap":0.9,"numTaps":32,"percentLoadloss":0,"wdgs":[{"wdg":1,"buses":"","conn":"WYE","kv":12.47,"kva":0,"percentR":0,"tap":0,"node":{"name":"node_1"}},{"wdg":2,"buses":"","conn":"WYE","kv":12.47,"kva":0,"percentR":0,"tap":0,"node":{"name":"node_2"}}]},"id":"name_b17228a","children":[],"x":477,"y":104.*************,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"text":"b17228a","locked":10,"ex":557,"ey":184.*************,"center":{"x":517,"y":144.*************}},{"id":"1532693","name":"line","lineName":"line","x":318.*************,"y":40.**************,"type":1,"toArrow":"line","lineWidth":1,"length":198.**************,"ex":517,"ey":42.**************,"width":198.*************,"height":1.****************,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"3ae5176","x":0,"y":1,"penId":"1532693","connectTo":"722e61f1","anchorId":"5c6b048","start":true,"lineLength":198.**************},{"x":1,"y":0,"id":"7831cea","penId":"1532693","connectTo":"b17228a","anchorId":"95eb19b"}],"center":{"x":417.79999999999995,"y":41.33333333333343}},{"hiddenText":true,"disableInput":true,"anchors":[{"id":"1b1cb288","x":0,"y":0.5,"background":"blue","penId":"3e9e9102"},{"id":"55fcf84e","x":1,"y":0.5,"penId":"3e9e9102"}],"width":480.0000000000001,"height":4.0**************,"name":"rectangle","lineWidth":0,"background":"#222222","deviceType":"motherLine","simulationConfig":{"phases":3},"id":"3e9e9102","children":[],"x":285.79999999999995,"y":207.33333333333348,"fontSize":24,"lineHeight":1.5,"text":"3e9e9102","connectedLines":[{"lineId":"a5cda6","lineAnchor":"409b0ac","anchor":"1b1cb288"}],"ex":765.8000000000001,"ey":211.33333333333348,"center":{"x":525.8,"y":209.33333333333348}},{"hiddenText":false,"disableInput":true,"anchors":[{"id":"1b1cb288","x":0,"y":0.5,"background":"blue","penId":"3e9e9102"},{"id":"55fcf84e","x":1,"y":0.5,"penId":"3e9e9102"}],"width":480.0000000000001,"height":4.0**************,"name":"name","lineWidth":0,"background":"#222222","deviceType":"name","simulationConfig":{"phases":3},"id":"name_3e9e9102","children":[],"x":285.79999999999995,"y":233.33333333333348,"fontSize":24,"lineHeight":1.5,"rotate":0,"text":"3e9e9102","image":"","locked":10,"ex":765.8000000000001,"ey":237.33333333333348,"center":{"x":525.8,"y":235.33333333333348}},{"id":"a5cda6","name":"line","lineName":"line","x":285.79999999999995,"y":120.*************,"type":1,"toArrow":"line","lineWidth":1,"length":247.66687303715057,"ex":517,"ey":209.33333333333348,"width":231.20000000000005,"height":88.80000000000018,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"bc163cd","x":1,"y":0,"penId":"a5cda6","connectTo":"b17228a","anchorId":"6212bfe3","start":true,"lineLength":247.66687303715057},{"x":0,"y":1,"id":"409b0ac","penId":"a5cda6","connectTo":"3e9e9102","anchorId":"1b1cb288"}],"center":{"x":401.4,"y":164.9333333333334}}],"origin":{"x":1.0000000000001137,"y":-75.46666666666658},"center":{"x":450,"y":300},"paths":{},"template":"ae1d1c3","component":false,"autoSizeinPc":true,"rule":true,"ruleColor":"#888888","gridColor":"#e2e2e2","gridSize":20,"color":"#222222","activeColor":"#278df8","toArrow":"line","version":"1.0.68","dataPoints":[]}, 更新后脚本: ﻿clear

new Circuit.circuit phases=3 bus=node_1
~ basekv=0 pu=1


set defaultbasefrequency=60
set mode=daily stepsize=1h number=24 hour=0 sec=0
set voltagebases=[0.22,0.38,10] 


new Transformer.b17228a
~ phases=3 windings=2 
~ XHL=0 %loadloss=0
~ maxtap=1.1 mintap=0.9 numtaps=32
~ wdg=1 bus=node_1 kv=12.47 kva=0 conn=WYE %R=0 tap=0
~ wdg=2 bus=node_2 kv=12.47 kva=0 conn=WYE %R=0 tap=0















new LineCode.LGJ25
~ nphases=3 basefreq=50 units=km R1=1.37 X1=0.38

new LineCode.JKLYJ35
~ nphases=3 basefreq=50 units=km R1=0.3238 X1=0.303

new LineCode.JKLYJ70
~ nphases=3 basefreq=50 units=km R1=0.46 X1=0.398




new Line.3e9e9102__0 phases=3 bus1=node_2 bus2=node_3
~ length=0.000001 


//负荷相关


//光伏相关


//储能相关


//关键参数监控


calcv
solve

2025-07-01 08:47:16.344 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-01 08:47:16.359 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-01 08:47:16.360 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-01 08:47:16.360 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-01 08:47:16.360 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-01 08:47:16.361 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-01 08:49:26.831 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-07-01 08:49:26.832 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-07-01 08:49:26.832 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-07-01 08:49:26.833 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-07-01 08:49:26.833 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-07-01 08:49:26.833 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-07-01 08:49:26.835 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 9 条数据
2025-07-01 08:49:26.835 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-07-01 08:49:26.836 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 9 个项目
2025-07-01 08:49:26.836 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 9
2025-07-01 08:49:28.107 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-07-01 08:49:28.108 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-07-01 08:49:28.109 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 2 条数据
2025-07-01 08:49:28.109 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 2
2025-07-01 09:17:29.488 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-01 09:17:29.489 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-01 09:17:29.491 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-01 09:17:29.491 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-01 09:17:29.492 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-01 09:17:29.493 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-01 09:17:38.581 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-01 09:17:38.581 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-01 09:17:38.582 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-01 09:17:38.582 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-01 09:17:38.582 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-01 09:17:38.582 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-01 09:20:38.053 - INFO - [SimulationController.java:130] : 接收到获取仿真任务详情请求: simulationId=30
2025-07-01 09:20:38.054 - INFO - [SimulationServiceImpl.java:589] : 获取仿真任务详情成功: ID=30
2025-07-01 09:20:38.055 - INFO - [SimulationController.java:134] : 获取仿真任务详情成功，ID: 30
2025-07-01 09:20:45.504 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-01 09:20:45.505 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-01 09:20:45.506 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-01 09:20:45.507 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-01 09:20:45.507 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-01 09:20:45.507 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-01 09:24:18.917 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-01 09:24:18.919 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-01 09:24:18.920 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-01 09:24:18.920 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-01 09:24:18.921 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-01 09:24:18.922 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-01 09:25:04.465 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-01 09:25:04.465 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-01 09:25:04.466 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-01 09:25:04.466 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-01 09:25:04.467 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-01 09:25:04.467 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-01 09:25:21.191 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-01 09:25:21.192 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-01 09:25:21.193 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-01 09:25:21.194 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-01 09:25:21.195 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-01 09:25:21.195 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-01 09:25:31.192 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-07-01 09:25:31.192 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-07-01 09:25:31.193 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-07-01 09:25:31.194 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-07-01 09:25:31.194 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-07-01 09:25:31.194 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-07-01 09:26:27.900 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-07-01 09:26:27.900 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-07-01 09:26:27.901 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-07-01 09:26:27.901 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-07-01 09:26:27.901 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-07-01 09:26:27.901 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-07-01 09:26:27.902 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 9 条数据
2025-07-01 09:26:27.904 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-07-01 09:26:27.904 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 9 个项目
2025-07-01 09:26:27.904 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 9
2025-07-01 09:26:29.027 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-07-01 09:26:29.028 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-07-01 09:26:29.030 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 2 条数据
2025-07-01 09:26:29.030 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 2
2025-07-01 09:26:31.888 - INFO - [SimulationController.java:130] : 接收到获取仿真任务详情请求: simulationId=30
2025-07-01 09:26:31.889 - INFO - [SimulationServiceImpl.java:589] : 获取仿真任务详情成功: ID=30
2025-07-01 09:26:31.891 - INFO - [SimulationController.java:134] : 获取仿真任务详情成功，ID: 30
2025-07-01 10:40:33.833 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-07-01 10:40:33.835 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-07-01 10:40:33.836 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-07-01 10:40:33.836 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-07-01 10:40:33.836 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-07-01 10:40:33.836 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-07-01 10:40:33.840 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 9 条数据
2025-07-01 10:40:33.840 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-07-01 10:40:33.841 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 9 个项目
2025-07-01 10:40:33.841 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 9
2025-07-01 10:40:35.248 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-07-01 10:40:35.249 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-07-01 10:40:35.251 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 2 条数据
2025-07-01 10:40:35.252 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 2
2025-07-01 10:42:23.708 - INFO - [SimulationController.java:156] : 接收到上传文件请求，文件名：荆门沙洋2025.xlsx, 文件类型：measured_data, 文件大小：6546582
2025-07-01 10:42:23.722 - INFO - [SimulationServiceImpl.java:794] : 成功上传文件到仿真任务，仿真ID: 33, 文件类型: measured_data, 文件路径: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 10:42:23.882 - INFO - [SimulationController.java:162] : 文件上传成功，保存路径：D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 10:42:23.904 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-07-01 10:42:23.904 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-07-01 10:42:23.907 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 2 条数据
2025-07-01 10:42:23.907 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 2
2025-07-01 10:42:26.217 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=33, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=1, pageSize=50)
2025-07-01 10:42:26.217 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=33
2025-07-01 10:42:26.217 - INFO - [SimulationServiceImpl.java:1769] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/33/measured_data/电压合格率报表.xlsx
2025-07-01 10:42:26.218 - INFO - [SimulationServiceImpl.java:1713] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/33/measured_data/电压合格率报表.xlsx
2025-07-01 10:42:28.360 -ERROR - [SimulationServiceImpl.java:1788] : 获取用户电压报表数据时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processRowsInBatches(VoltageDataProcessor.java:291)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:228)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getUserVoltageReport(SimulationServiceImpl.java:1775)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getUserVoltageReport(SimulationController.java:309)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-01 10:42:28.362 - INFO - [SimulationServiceImpl.java:1822] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/33/measured_data/电压合格率报表.xlsx
2025-07-01 10:42:28.406 -ERROR - [SimulationServiceImpl.java:1732] : 获取电压报表数据时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processRowsInBatches(VoltageDataProcessor.java:291)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:228)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getCourtsVoltageReport(SimulationServiceImpl.java:1719)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getCourtsVoltageReport(SimulationController.java:293)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-01 10:42:28.409 -ERROR - [GlobalExceptionHandler.java:22] : Cannot invoke "com.cet.electric.ngapserver.entity.CourtsStatistics.getTotalUsers()" because "voltageReport" is null
com.cet.electric.ngapserver.web.controller.SimulationController.getCourtsVoltageReport(SimulationController.java:295)
java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
java.base/java.lang.reflect.Method.invoke(Method.java:580)
org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-01 10:42:30.044 -ERROR - [SimulationServiceImpl.java:1841] : 获取用户电压报表记录数时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processRowsInBatches(VoltageDataProcessor.java:291)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:228)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.countUserVoltageReport(SimulationServiceImpl.java:1828)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getUserVoltageReport(SimulationController.java:315)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-01 10:42:30.045 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 33, 用户数量: 0
2025-07-01 10:42:34.348 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=33
2025-07-01 10:42:34.348 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=33, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=1, pageSize=50)
2025-07-01 10:42:34.348 - INFO - [SimulationServiceImpl.java:1713] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/33/measured_data/电压合格率报表.xlsx
2025-07-01 10:42:34.348 - INFO - [SimulationServiceImpl.java:1769] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/33/measured_data/电压合格率报表.xlsx
2025-07-01 10:42:36.172 -ERROR - [SimulationServiceImpl.java:1732] : 获取电压报表数据时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processRowsInBatches(VoltageDataProcessor.java:291)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:228)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getCourtsVoltageReport(SimulationServiceImpl.java:1719)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getCourtsVoltageReport(SimulationController.java:293)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-01 10:42:36.173 -ERROR - [GlobalExceptionHandler.java:22] : Cannot invoke "com.cet.electric.ngapserver.entity.CourtsStatistics.getTotalUsers()" because "voltageReport" is null
com.cet.electric.ngapserver.web.controller.SimulationController.getCourtsVoltageReport(SimulationController.java:295)
java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
java.base/java.lang.reflect.Method.invoke(Method.java:580)
org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-01 10:42:36.195 -ERROR - [SimulationServiceImpl.java:1788] : 获取用户电压报表数据时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processRowsInBatches(VoltageDataProcessor.java:291)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:228)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getUserVoltageReport(SimulationServiceImpl.java:1775)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getUserVoltageReport(SimulationController.java:309)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-01 10:42:36.196 - INFO - [SimulationServiceImpl.java:1822] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/33/measured_data/电压合格率报表.xlsx
2025-07-01 10:42:37.931 -ERROR - [SimulationServiceImpl.java:1841] : 获取用户电压报表记录数时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processRowsInBatches(VoltageDataProcessor.java:291)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:228)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.countUserVoltageReport(SimulationServiceImpl.java:1828)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getUserVoltageReport(SimulationController.java:315)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-01 10:42:37.931 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 33, 用户数量: 0
2025-07-01 10:49:41.073 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-07-01 10:49:41.076 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-01 10:49:41.079 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-07-01 10:49:41.083 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-07-01 10:49:47.291 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 30296 (D:\CETWorkSpace\ngap-server\ngap-server.jar started by dell in D:\CETWorkSpace\ngap-server)
2025-07-01 10:49:47.293 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-07-01 10:49:49.125 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-07-01 10:49:49.133 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-07-01 10:49:49.134 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-07-01 10:49:49.134 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-01 10:49:49.186 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-07-01 10:49:49.186 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 1844 ms
2025-07-01 10:49:49.718 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-01 10:49:50.150 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-01 10:49:50.214 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-01 10:49:50.273 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-07-01 10:49:50.450 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-07-01 10:49:50.467 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-07-01 10:49:50.526 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-07-01 10:49:50.527 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-07-01 10:49:50.554 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-07-01 10:49:50.602 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-07-01 10:49:50.790 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-07-01 10:49:50.817 - INFO - [TaskSchedule.java:38] : 开始删除
2025-07-01 10:49:50.817 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-07-01 10:49:50.819 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 4.219 seconds (JVM running for 5.318)
2025-07-01 10:49:50.831 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-07-01 10:49:50.893 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-01 10:49:50.893 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-01 10:49:50.894 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-07-01 10:49:50.894 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-01 10:49:51.157 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-01 10:49:51.157 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-01 10:49:51.158 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-07-01 10:49:51.175 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-07-01 10:49:51.175 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-07-01 10:49:51.176 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-07-01 10:49:51.176 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-07-01 10:50:19.532 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 13036 (D:\CETWorkSpace\ngap-server\ngap-server.jar started by dell in D:\CETWorkSpace\ngap-server)
2025-07-01 10:50:19.533 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-07-01 10:50:20.798 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-07-01 10:50:20.804 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-07-01 10:50:20.804 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-07-01 10:50:20.804 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-01 10:50:20.846 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-07-01 10:50:20.846 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 1281 ms
2025-07-01 10:50:21.333 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-01 10:50:21.645 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-01 10:50:21.704 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-01 10:50:21.800 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-07-01 10:50:21.958 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-07-01 10:50:21.979 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-07-01 10:50:22.016 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-07-01 10:50:22.017 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-07-01 10:50:22.033 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-07-01 10:50:22.063 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-07-01 10:50:22.264 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-07-01 10:50:22.293 - INFO - [TaskSchedule.java:38] : 开始删除
2025-07-01 10:50:22.293 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-07-01 10:50:22.295 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 3.424 seconds (JVM running for 3.753)
2025-07-01 10:50:22.304 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-07-01 10:50:22.354 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-01 10:50:22.355 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-01 10:50:22.356 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-07-01 10:50:22.357 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-01 10:50:22.407 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-07-01 10:50:22.414 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-01 10:50:22.414 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-01 10:50:22.428 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-07-01 10:50:22.428 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-07-01 10:50:22.430 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-07-01 10:50:22.430 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-07-01 10:50:32.053 - INFO - [DirectJDKLog.java:173] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 10:50:32.053 - INFO - [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-07-01 10:50:32.057 - INFO - [FrameworkServlet.java:547] : Completed initialization in 4 ms
2025-07-01 10:50:32.077 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-07-01 10:50:32.077 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-07-01 10:50:32.077 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-07-01 10:50:32.078 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-07-01 10:50:32.124 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-07-01 10:50:32.125 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-07-01 10:50:32.150 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 9 条数据
2025-07-01 10:50:32.150 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-07-01 10:50:32.152 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 9 个项目
2025-07-01 10:50:32.153 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 9
2025-07-01 10:50:32.896 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-07-01 10:50:32.896 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-07-01 10:50:32.899 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 2 条数据
2025-07-01 10:50:32.900 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 2
2025-07-01 10:50:34.787 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=33
2025-07-01 10:50:34.788 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 33
2025-07-01 10:50:34.794 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 10:50:34.795 - WARN - [CsvUtils.java:41] : CSV目录不存在或不是有效目录: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 10:50:34.796 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 10:50:34.820 - INFO - [ExcelCacheManager.java:51] : Excel缓存管理器初始化完成，过期时间: 1800000ms
2025-07-01 10:50:34.822 - INFO - [VoltageObjectPool.java:46] : 电压对象池初始化完成
2025-07-01 10:50:34.823 - INFO - [VoltagePerformanceMonitor.java:28] : 电压性能监控器初始化完成
2025-07-01 10:50:34.824 - INFO - [ExcelUtils.java:50] : 检测到大文件(6.24MB)，使用优化处理器
2025-07-01 10:50:38.022 - INFO - [OptimizedExcelProcessor.java:94] : 指标提取完成: 文件=D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx, 耗时=3192ms, 指标数=1530
2025-07-01 10:50:38.029 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 荆门沙洋2025.xlsx 的电压指标，共 1 个指标
2025-07-01 10:50:38.029 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-07-01 10:50:38.030 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 33, 指标数量: 2
2025-07-01 10:50:38.152 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=33
2025-07-01 10:50:38.152 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 33
2025-07-01 10:50:38.154 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 10:50:38.154 - WARN - [SimulationServiceImpl.java:1880] : 仿真输出目录不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 10:50:38.154 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 33, 总数据点: 0, 合格数据点: 0, 合格率: 0.00%
2025-07-01 10:50:44.923 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=33, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3], startTimestamp=null)
2025-07-01 10:50:44.924 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 33, metricIds: [用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3]
2025-07-01 10:50:44.924 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 33, metricId: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 10:50:44.927 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 10:50:44.927 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv
2025-07-01 10:50:44.928 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 荆门沙洋2025.xlsx
2025-07-01 10:50:44.928 - INFO - [ExcelUtils.java:322] : 检测到大文件(6.24MB)，使用优化处理器获取指标数据
2025-07-01 10:50:44.928 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 10:50:46.772 - INFO - [OptimizedExcelProcessor.java:167] : 指标数据获取完成: 指标ID=用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3, 耗时=1844ms, 数据点数=0
2025-07-01 10:50:46.773 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 0 个数据点
2025-07-01 10:50:46.774 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 0
2025-07-01 10:50:46.774 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 33, 指标数量: 1
2025-07-01 10:51:04.688 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=33
2025-07-01 10:51:04.691 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=33, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=1, pageSize=50)
2025-07-01 10:51:04.691 - INFO - [SimulationServiceImpl.java:1713] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/33/measured_data/电压合格率报表.xlsx
2025-07-01 10:51:04.692 - INFO - [SimulationServiceImpl.java:1769] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/33/measured_data/电压合格率报表.xlsx
2025-07-01 10:51:06.831 -ERROR - [VoltageDataProcessor.java:235] : 读取小文件失败: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
java.lang.ClassCastException: class com.cet.electric.ngapserver.util.excel.OptimizedExcelProcessor$ColumnAnalysisResult cannot be cast to class java.util.Map (com.cet.electric.ngapserver.util.excel.OptimizedExcelProcessor$ColumnAnalysisResult is in unnamed module of loader org.springframework.boot.loader.LaunchedURLClassLoader @7506e922; java.util.Map is in module java.base of loader 'bootstrap')
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.getColumnIndexCached(VoltageDataProcessor.java:249)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:225)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getCourtsVoltageReport(SimulationServiceImpl.java:1719)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getCourtsVoltageReport(SimulationController.java:293)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-01 10:51:06.832 - INFO - [VoltageDataProcessor.java:237] : 传统方式失败，尝试流式读取: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 10:51:06.832 - INFO - [VoltageDataProcessor.java:93] : 尝试使用增强内存限制读取大文件: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 10:51:06.901 -ERROR - [VoltageDataProcessor.java:235] : 读取小文件失败: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
java.lang.ClassCastException: class com.cet.electric.ngapserver.util.excel.OptimizedExcelProcessor$ColumnAnalysisResult cannot be cast to class java.util.Map (com.cet.electric.ngapserver.util.excel.OptimizedExcelProcessor$ColumnAnalysisResult is in unnamed module of loader org.springframework.boot.loader.LaunchedURLClassLoader @7506e922; java.util.Map is in module java.base of loader 'bootstrap')
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.getColumnIndexCached(VoltageDataProcessor.java:249)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:225)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getUserVoltageReport(SimulationServiceImpl.java:1775)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getUserVoltageReport(SimulationController.java:309)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-01 10:51:06.902 - INFO - [VoltageDataProcessor.java:237] : 传统方式失败，尝试流式读取: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 10:51:06.902 - INFO - [VoltageDataProcessor.java:93] : 尝试使用增强内存限制读取大文件: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 10:51:08.874 - INFO - [VoltageDataProcessor.java:104] : 成功打开工作表，开始处理数据...
2025-07-01 10:51:08.875 -ERROR - [SimulationServiceImpl.java:1788] : 获取用户电压报表数据时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processLargeFileInSmallBatches(VoltageDataProcessor.java:178)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readLargeFileWithStreaming(VoltageDataProcessor.java:110)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:238)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getUserVoltageReport(SimulationServiceImpl.java:1775)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getUserVoltageReport(SimulationController.java:309)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-01 10:51:08.876 - INFO - [VoltageDataProcessor.java:104] : 成功打开工作表，开始处理数据...
2025-07-01 10:51:08.877 - INFO - [SimulationServiceImpl.java:1822] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/33/measured_data/电压合格率报表.xlsx
2025-07-01 10:51:08.877 -ERROR - [SimulationServiceImpl.java:1732] : 获取电压报表数据时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processLargeFileInSmallBatches(VoltageDataProcessor.java:178)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readLargeFileWithStreaming(VoltageDataProcessor.java:110)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:238)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getCourtsVoltageReport(SimulationServiceImpl.java:1719)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getCourtsVoltageReport(SimulationController.java:293)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-01 10:51:08.880 -ERROR - [GlobalExceptionHandler.java:22] : Cannot invoke "com.cet.electric.ngapserver.entity.CourtsStatistics.getTotalUsers()" because "voltageReport" is null
com.cet.electric.ngapserver.web.controller.SimulationController.getCourtsVoltageReport(SimulationController.java:295)
java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
java.base/java.lang.reflect.Method.invoke(Method.java:580)
org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-01 10:51:10.638 -ERROR - [VoltageDataProcessor.java:235] : 读取小文件失败: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
java.lang.ClassCastException: class com.cet.electric.ngapserver.util.excel.OptimizedExcelProcessor$ColumnAnalysisResult cannot be cast to class java.util.Map (com.cet.electric.ngapserver.util.excel.OptimizedExcelProcessor$ColumnAnalysisResult is in unnamed module of loader org.springframework.boot.loader.LaunchedURLClassLoader @7506e922; java.util.Map is in module java.base of loader 'bootstrap')
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.getColumnIndexCached(VoltageDataProcessor.java:249)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:225)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.countUserVoltageReport(SimulationServiceImpl.java:1828)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getUserVoltageReport(SimulationController.java:315)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-01 10:51:10.639 - INFO - [VoltageDataProcessor.java:237] : 传统方式失败，尝试流式读取: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 10:51:10.639 - INFO - [VoltageDataProcessor.java:93] : 尝试使用增强内存限制读取大文件: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 10:51:12.304 - INFO - [VoltageDataProcessor.java:104] : 成功打开工作表，开始处理数据...
2025-07-01 10:51:12.305 -ERROR - [SimulationServiceImpl.java:1841] : 获取用户电压报表记录数时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processLargeFileInSmallBatches(VoltageDataProcessor.java:178)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readLargeFileWithStreaming(VoltageDataProcessor.java:110)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:238)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.countUserVoltageReport(SimulationServiceImpl.java:1828)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getUserVoltageReport(SimulationController.java:315)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-01 10:51:12.305 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 33, 用户数量: 0
2025-07-01 10:52:08.993 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=33
2025-07-01 10:52:08.994 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 33
2025-07-01 10:52:08.996 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 10:52:08.996 - WARN - [CsvUtils.java:41] : CSV目录不存在或不是有效目录: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 10:52:08.997 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 10:52:08.997 - INFO - [ExcelUtils.java:50] : 检测到大文件(6.24MB)，使用优化处理器
2025-07-01 10:52:08.998 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 荆门沙洋2025.xlsx 的电压指标，共 1 个指标
2025-07-01 10:52:08.998 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-07-01 10:52:08.998 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 33, 指标数量: 2
2025-07-01 10:52:09.071 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=33
2025-07-01 10:52:09.072 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 33
2025-07-01 10:52:09.074 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 10:52:09.074 - WARN - [SimulationServiceImpl.java:1880] : 仿真输出目录不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 10:52:09.074 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 33, 总数据点: 0, 合格数据点: 0, 合格率: 0.00%
2025-07-01 10:52:16.689 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=33, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3], startTimestamp=null)
2025-07-01 10:52:16.689 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 33, metricIds: [用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3]
2025-07-01 10:52:16.690 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 33, metricId: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 10:52:16.691 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 10:52:16.691 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv
2025-07-01 10:52:16.691 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 荆门沙洋2025.xlsx
2025-07-01 10:52:16.691 - INFO - [ExcelUtils.java:322] : 检测到大文件(6.24MB)，使用优化处理器获取指标数据
2025-07-01 10:52:16.692 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 10:52:16.692 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 0 个数据点
2025-07-01 10:52:16.692 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 0
2025-07-01 10:52:16.692 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 33, 指标数量: 1
2025-07-01 10:53:07.198 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=21, keyword=, startTime=null, endTime=null)
2025-07-01 10:53:07.199 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 21, keyword: , startTime: null, endTime: null
2025-07-01 10:53:08.430 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 6 条数据
2025-07-01 10:53:08.431 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 6
2025-07-01 10:53:10.212 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=24
2025-07-01 10:53:10.212 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 24
2025-07-01 10:53:10.216 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script
2025-07-01 10:53:13.010 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script, 共 216 个子指标
2025-07-01 10:53:13.011 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/21/24/measured_data/工作簿1.xlsx
2025-07-01 10:53:13.011 - INFO - [ExcelUtils.java:50] : 检测到大文件(5.55MB)，使用优化处理器
2025-07-01 10:53:14.711 - INFO - [OptimizedExcelProcessor.java:94] : 指标提取完成: 文件=D:\CETWorkSpace\ngap-server/data/21/24/measured_data/工作簿1.xlsx, 耗时=1700ms, 指标数=956
2025-07-01 10:53:14.712 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 工作簿1.xlsx 的电压指标，共 1 个指标
2025-07-01 10:53:14.712 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-07-01 10:53:14.712 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 24, 指标数量: 2
2025-07-01 10:53:14.796 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=24
2025-07-01 10:53:14.797 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 24
2025-07-01 10:53:14.801 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script
2025-07-01 10:53:14.803 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script
2025-07-01 10:53:14.804 - INFO - [CsvUtils.java:375] : 找到 216 个CSV文件，开始处理
2025-07-01 10:53:14.939 - INFO - [CsvUtils.java:389] : 完成电压数据提取，处理文件数: 216/216, 总数据点: 23424, 合格数据点: 23161, 合格率: 98.88%
2025-07-01 10:53:14.940 - INFO - [SimulationServiceImpl.java:1914] : 仿真任务 24 电压合格率计算完成：总数据点=23424, 合格数据点=23161, 合格率=98.88%, 处理时间=137ms
2025-07-01 10:53:14.940 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 24, 总数据点: 23424, 合格数据点: 23161, 合格率: 98.88%
2025-07-01 10:53:21.105 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=24, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4206858070152.用户名称-胡元凯.电压等级-220.相别-1], startTimestamp=null)
2025-07-01 10:53:21.106 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 24, metricIds: [用户电压.用户编号-4206858070152.用户名称-胡元凯.电压等级-220.相别-1]
2025-07-01 10:53:21.107 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 24, metricId: 用户电压.用户编号-4206858070152.用户名称-胡元凯.电压等级-220.相别-1
2025-07-01 10:53:21.108 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4206858070152.用户名称-胡元凯.电压等级-220.相别-1
2025-07-01 10:53:21.108 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/用户电压.csv
2025-07-01 10:53:21.108 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 工作簿1.xlsx
2025-07-01 10:53:21.109 - INFO - [ExcelUtils.java:322] : 检测到大文件(5.55MB)，使用优化处理器获取指标数据
2025-07-01 10:53:21.109 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4206858070152.用户名称-胡元凯.电压等级-220.相别-1
2025-07-01 10:53:22.490 - INFO - [OptimizedExcelProcessor.java:167] : 指标数据获取完成: 指标ID=用户电压.用户编号-4206858070152.用户名称-胡元凯.电压等级-220.相别-1, 耗时=1381ms, 数据点数=2870
2025-07-01 10:53:22.491 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 2870 个数据点
2025-07-01 10:53:22.491 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 2870
2025-07-01 10:53:22.491 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 24, 指标数量: 1
2025-07-01 10:54:24.258 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 32276 (D:\CETWorkSpace\ngap-server\target\classes started by dell in D:\CETWorkSpace\ngap-server)
2025-07-01 10:54:24.260 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-07-01 10:54:24.282 - INFO - [DeferredLog.java:225] : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-01 10:54:24.282 - INFO - [DeferredLog.java:225] : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-01 10:54:26.658 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-07-01 10:54:26.663 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-07-01 10:54:26.664 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-07-01 10:54:26.664 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-01 10:54:26.720 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-07-01 10:54:26.720 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 2437 ms
2025-07-01 10:54:27.147 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-01 10:54:27.353 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-01 10:54:27.416 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-01 10:54:27.461 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-07-01 10:54:27.485 - INFO - [OptionalLiveReloadServer.java:58] : LiveReload server is running on port 35729
2025-07-01 10:54:27.613 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-07-01 10:54:27.635 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-07-01 10:54:27.653 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-07-01 10:54:27.654 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-07-01 10:54:27.665 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-07-01 10:54:27.685 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-07-01 10:54:27.802 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-07-01 10:54:27.817 - INFO - [TaskSchedule.java:38] : 开始删除
2025-07-01 10:54:27.817 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-07-01 10:54:27.820 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 4.057 seconds (JVM running for 7.547)
2025-07-01 10:54:27.824 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-01 10:54:27.824 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-01 10:54:27.824 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-07-01 10:54:27.825 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-01 10:54:27.844 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-07-01 10:54:27.919 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-01 10:54:27.920 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-01 10:54:27.931 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-07-01 10:54:27.943 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-07-01 10:54:27.943 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-07-01 10:54:27.944 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-07-01 10:54:27.944 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-07-01 10:54:28.541 - INFO - [DirectJDKLog.java:173] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 10:54:28.542 - INFO - [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-07-01 10:54:28.545 - INFO - [FrameworkServlet.java:547] : Completed initialization in 3 ms
2025-07-01 10:54:37.526 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-07-01 10:54:37.527 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-07-01 10:54:37.548 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 2 条数据
2025-07-01 10:54:37.549 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 2
2025-07-01 10:54:43.207 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=33
2025-07-01 10:54:43.208 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 33
2025-07-01 10:54:43.212 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 10:54:43.213 - WARN - [CsvUtils.java:41] : CSV目录不存在或不是有效目录: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 10:54:43.213 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 10:54:43.216 - INFO - [ExcelCacheManager.java:51] : Excel缓存管理器初始化完成，过期时间: 1800000ms
2025-07-01 10:54:43.218 - INFO - [VoltageObjectPool.java:46] : 电压对象池初始化完成
2025-07-01 10:54:43.219 - INFO - [VoltagePerformanceMonitor.java:28] : 电压性能监控器初始化完成
2025-07-01 10:54:43.221 - INFO - [ExcelUtils.java:50] : 检测到大文件(6.24MB)，使用优化处理器
2025-07-01 10:54:47.210 - INFO - [OptimizedExcelProcessor.java:94] : 指标提取完成: 文件=D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx, 耗时=3984ms, 指标数=1530
2025-07-01 10:54:47.211 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 荆门沙洋2025.xlsx 的电压指标，共 1 个指标
2025-07-01 10:54:47.211 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-07-01 10:54:47.211 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 33, 指标数量: 2
2025-07-01 10:54:47.274 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=33
2025-07-01 10:54:47.274 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 33
2025-07-01 10:54:47.275 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 10:54:47.275 - WARN - [SimulationServiceImpl.java:1880] : 仿真输出目录不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 10:54:47.275 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 33, 总数据点: 0, 合格数据点: 0, 合格率: 0.00%
2025-07-01 10:54:52.652 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=33, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3], startTimestamp=null)
2025-07-01 10:54:52.652 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 33, metricIds: [用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3]
2025-07-01 10:54:52.652 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 33, metricId: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 10:54:52.653 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 10:54:52.653 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv
2025-07-01 10:54:52.653 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 荆门沙洋2025.xlsx
2025-07-01 10:54:52.653 - INFO - [ExcelUtils.java:322] : 检测到大文件(6.24MB)，使用优化处理器获取指标数据
2025-07-01 10:54:52.653 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 10:54:55.167 - INFO - [OptimizedExcelProcessor.java:167] : 指标数据获取完成: 指标ID=用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3, 耗时=2514ms, 数据点数=0
2025-07-01 10:54:55.167 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 0 个数据点
2025-07-01 10:54:55.167 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 0
2025-07-01 10:54:55.168 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 33, 指标数量: 1
2025-07-01 10:57:14.102 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-07-01 10:57:14.105 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-01 10:57:14.108 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-07-01 10:57:14.110 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-07-01 10:57:19.702 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 28520 (D:\CETWorkSpace\ngap-server\target\classes started by dell in D:\CETWorkSpace\ngap-server)
2025-07-01 10:57:19.703 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-07-01 10:57:19.730 - INFO - [DeferredLog.java:225] : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-01 10:57:19.730 - INFO - [DeferredLog.java:225] : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-01 10:57:20.843 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-07-01 10:57:20.861 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-07-01 10:57:20.862 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-07-01 10:57:20.862 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-01 10:57:20.942 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-07-01 10:57:20.942 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 1212 ms
2025-07-01 10:57:21.407 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-01 10:57:21.687 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-01 10:57:21.742 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-01 10:57:21.813 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-07-01 10:57:21.847 - INFO - [OptionalLiveReloadServer.java:58] : LiveReload server is running on port 35729
2025-07-01 10:57:21.971 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-07-01 10:57:22.000 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-07-01 10:57:22.033 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-07-01 10:57:22.034 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-07-01 10:57:22.059 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-07-01 10:57:22.489 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-07-01 10:57:22.706 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-07-01 10:57:22.729 - INFO - [TaskSchedule.java:38] : 开始删除
2025-07-01 10:57:22.730 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-07-01 10:57:22.735 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 3.438 seconds (JVM running for 4.967)
2025-07-01 10:57:22.746 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-01 10:57:22.748 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-01 10:57:22.749 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-07-01 10:57:22.749 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-01 10:57:22.765 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-07-01 10:57:22.877 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-01 10:57:22.878 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-01 10:57:22.881 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-07-01 10:57:22.942 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-07-01 10:57:22.942 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-07-01 10:57:22.943 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-07-01 10:57:22.943 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-07-01 10:57:23.264 - INFO - [DirectJDKLog.java:173] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 10:57:23.265 - INFO - [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-07-01 10:57:23.279 - INFO - [FrameworkServlet.java:547] : Completed initialization in 14 ms
2025-07-01 10:57:33.590 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=33, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3], startTimestamp=null)
2025-07-01 10:57:33.590 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 33, metricIds: [用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3]
2025-07-01 10:57:33.590 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 33, metricId: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 10:57:33.595 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 10:57:33.596 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv
2025-07-01 10:57:33.596 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 荆门沙洋2025.xlsx
2025-07-01 10:57:33.618 - INFO - [ExcelCacheManager.java:51] : Excel缓存管理器初始化完成，过期时间: 1800000ms
2025-07-01 10:57:33.619 - INFO - [VoltageObjectPool.java:46] : 电压对象池初始化完成
2025-07-01 10:57:33.619 - INFO - [VoltagePerformanceMonitor.java:28] : 电压性能监控器初始化完成
2025-07-01 10:57:33.620 - INFO - [ExcelUtils.java:322] : 检测到大文件(6.24MB)，使用优化处理器获取指标数据
2025-07-01 10:57:33.621 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 10:59:25.585 - WARN - [HikariPool.java:787] : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=50s820ms186µs600ns).
2025-07-01 11:02:41.892 - WARN - [HikariPool.java:787] : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m16s300ms331µs).
2025-07-01 11:02:44.984 - INFO - [OptimizedExcelProcessor.java:167] : 指标数据获取完成: 指标ID=用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3, 耗时=311363ms, 数据点数=0
2025-07-01 11:02:44.993 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 0 个数据点
2025-07-01 11:02:44.998 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 0
2025-07-01 11:02:44.998 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 33, 指标数量: 1
2025-07-01 11:04:08.569 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-07-01 11:04:08.571 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-01 11:04:08.573 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-07-01 11:04:08.574 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-07-01 11:04:08.897 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 28520 (D:\CETWorkSpace\ngap-server\target\classes started by dell in D:\CETWorkSpace\ngap-server)
2025-07-01 11:04:08.897 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-07-01 11:04:09.216 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-07-01 11:04:09.217 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-07-01 11:04:09.217 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-07-01 11:04:09.217 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-01 11:04:09.237 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-07-01 11:04:09.237 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 338 ms
2025-07-01 11:04:09.350 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-01 11:04:09.437 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-01 11:04:09.456 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-01 11:04:09.477 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-07-01 11:04:09.485 - INFO - [OptionalLiveReloadServer.java:58] : LiveReload server is running on port 35729
2025-07-01 11:04:09.530 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-07-01 11:04:09.557 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-07-01 11:04:09.559 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-07-01 11:04:09.560 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-07-01 11:04:09.561 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-07-01 11:04:09.569 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-07-01 11:04:09.627 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-07-01 11:04:09.638 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 0.774 seconds (JVM running for 411.856)
2025-07-01 11:04:09.638 - INFO - [TaskSchedule.java:38] : 开始删除
2025-07-01 11:04:09.639 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-07-01 11:04:09.639 - INFO - [HikariDataSource.java:110] : HikariPool-2 - Starting...
2025-07-01 11:04:09.640 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-01 11:04:09.641 - INFO - [HikariDataSource.java:123] : HikariPool-2 - Start completed.
2025-07-01 11:04:09.641 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-01 11:04:09.641 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-07-01 11:04:09.642 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-07-01 11:04:09.642 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-07-01 11:04:09.642 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-01 11:04:09.642 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-07-01 11:04:09.643 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-07-01 11:04:09.643 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-01 11:04:09.643 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-01 11:04:09.644 - INFO - [ConditionEvaluationDeltaLoggingListener.java:63] : Condition evaluation unchanged
2025-07-01 11:04:11.057 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-07-01 11:04:11.057 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-01 11:04:11.058 - INFO - [HikariDataSource.java:350] : HikariPool-2 - Shutdown initiated...
2025-07-01 11:04:11.060 - INFO - [HikariDataSource.java:352] : HikariPool-2 - Shutdown completed.
2025-07-01 11:04:13.037 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 28520 (D:\CETWorkSpace\ngap-server\target\classes started by dell in D:\CETWorkSpace\ngap-server)
2025-07-01 11:04:13.037 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-07-01 11:04:13.295 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-07-01 11:04:13.295 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-07-01 11:04:13.295 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-07-01 11:04:13.296 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-01 11:04:13.334 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-07-01 11:04:13.335 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 296 ms
2025-07-01 11:04:13.438 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-01 11:04:13.519 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-01 11:04:13.533 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-01 11:04:13.549 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-07-01 11:04:13.554 - INFO - [OptionalLiveReloadServer.java:58] : LiveReload server is running on port 35729
2025-07-01 11:04:13.584 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-07-01 11:04:13.598 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-07-01 11:04:13.600 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-07-01 11:04:13.600 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-07-01 11:04:13.601 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-07-01 11:04:13.605 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-07-01 11:04:13.641 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-07-01 11:04:13.649 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 0.638 seconds (JVM running for 415.867)
2025-07-01 11:04:13.649 - INFO - [TaskSchedule.java:38] : 开始删除
2025-07-01 11:04:13.650 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-07-01 11:04:13.650 - INFO - [HikariDataSource.java:110] : HikariPool-3 - Starting...
2025-07-01 11:04:13.650 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-01 11:04:13.651 - INFO - [HikariDataSource.java:123] : HikariPool-3 - Start completed.
2025-07-01 11:04:13.651 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-01 11:04:13.651 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-07-01 11:04:13.652 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-07-01 11:04:13.652 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-07-01 11:04:13.652 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-07-01 11:04:13.652 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-07-01 11:04:13.653 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-01 11:04:13.653 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-01 11:04:13.653 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-01 11:04:13.654 - INFO - [ConditionEvaluationDeltaLoggingListener.java:63] : Condition evaluation unchanged
2025-07-01 11:04:59.602 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-07-01 11:04:59.603 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-01 11:04:59.604 - INFO - [HikariDataSource.java:350] : HikariPool-3 - Shutdown initiated...
2025-07-01 11:04:59.605 - INFO - [HikariDataSource.java:352] : HikariPool-3 - Shutdown completed.
2025-07-01 11:05:03.372 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 21380 (D:\CETWorkSpace\ngap-server\target\classes started by dell in D:\CETWorkSpace\ngap-server)
2025-07-01 11:05:03.373 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-07-01 11:05:03.399 - INFO - [DeferredLog.java:225] : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-01 11:05:03.400 - INFO - [DeferredLog.java:225] : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-01 11:05:04.456 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-07-01 11:05:04.473 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-07-01 11:05:04.474 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-07-01 11:05:04.475 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-01 11:05:04.560 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-07-01 11:05:04.560 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 1160 ms
2025-07-01 11:05:04.996 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-01 11:05:05.312 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-01 11:05:05.363 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-01 11:05:05.425 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-07-01 11:05:05.460 - INFO - [OptionalLiveReloadServer.java:58] : LiveReload server is running on port 35729
2025-07-01 11:05:05.574 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-07-01 11:05:05.604 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-07-01 11:05:05.874 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-07-01 11:05:05.877 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-07-01 11:05:06.041 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-07-01 11:05:06.088 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-07-01 11:05:06.275 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-07-01 11:05:06.298 - INFO - [TaskSchedule.java:38] : 开始删除
2025-07-01 11:05:06.299 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-07-01 11:05:06.303 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 3.301 seconds (JVM running for 3.796)
2025-07-01 11:05:06.310 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-01 11:05:06.310 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-01 11:05:06.311 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-07-01 11:05:06.311 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-01 11:05:06.329 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-07-01 11:05:06.460 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-01 11:05:06.461 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-01 11:05:06.464 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-07-01 11:05:06.520 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-07-01 11:05:06.520 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-07-01 11:05:06.520 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-07-01 11:05:06.520 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-07-01 11:05:06.760 - INFO - [DirectJDKLog.java:173] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 11:05:06.761 - INFO - [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-07-01 11:05:06.769 - INFO - [FrameworkServlet.java:547] : Completed initialization in 8 ms
2025-07-01 11:05:11.079 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=33, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3], startTimestamp=null)
2025-07-01 11:05:11.079 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 33, metricIds: [用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3]
2025-07-01 11:05:11.079 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 33, metricId: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:05:11.086 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:05:11.086 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv
2025-07-01 11:05:11.086 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 荆门沙洋2025.xlsx
2025-07-01 11:05:11.089 - INFO - [ExcelCacheManager.java:51] : Excel缓存管理器初始化完成，过期时间: 1800000ms
2025-07-01 11:05:11.090 - INFO - [VoltageObjectPool.java:46] : 电压对象池初始化完成
2025-07-01 11:05:11.091 - INFO - [VoltagePerformanceMonitor.java:28] : 电压性能监控器初始化完成
2025-07-01 11:05:11.092 - INFO - [ExcelUtils.java:322] : 检测到大文件(6.24MB)，使用优化处理器获取指标数据
2025-07-01 11:05:11.092 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:05:14.540 - INFO - [OptimizedExcelProcessor.java:167] : 指标数据获取完成: 指标ID=用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3, 耗时=3448ms, 数据点数=0
2025-07-01 11:05:14.541 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 0 个数据点
2025-07-01 11:05:14.543 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 0
2025-07-01 11:05:14.543 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 33, 指标数量: 1
2025-07-01 11:05:33.099 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=33, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3], startTimestamp=null)
2025-07-01 11:05:33.100 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 33, metricIds: [用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3]
2025-07-01 11:05:33.100 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 33, metricId: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:05:33.100 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:05:33.101 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv
2025-07-01 11:05:33.101 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 荆门沙洋2025.xlsx
2025-07-01 11:05:33.101 - INFO - [ExcelUtils.java:322] : 检测到大文件(6.24MB)，使用优化处理器获取指标数据
2025-07-01 11:05:33.101 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:05:33.101 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 0 个数据点
2025-07-01 11:05:33.101 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 0
2025-07-01 11:05:33.101 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 33, 指标数量: 1
2025-07-01 11:05:36.861 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=33, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2], startTimestamp=null)
2025-07-01 11:05:36.862 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 33, metricIds: [用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2]
2025-07-01 11:05:36.862 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 33, metricId: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2
2025-07-01 11:05:36.863 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2
2025-07-01 11:05:36.863 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv
2025-07-01 11:05:36.863 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 荆门沙洋2025.xlsx
2025-07-01 11:05:36.863 - INFO - [ExcelUtils.java:322] : 检测到大文件(6.24MB)，使用优化处理器获取指标数据
2025-07-01 11:05:36.863 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2
2025-07-01 11:06:30.380 - WARN - [HikariPool.java:787] : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=53s790ms739µs700ns).
2025-07-01 11:09:01.734 - WARN - [HikariPool.java:787] : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m56s879ms305µs200ns).
2025-07-01 11:09:01.801 - INFO - [OptimizedExcelProcessor.java:167] : 指标数据获取完成: 指标ID=用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2, 耗时=204938ms, 数据点数=0
2025-07-01 11:09:01.802 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 0 个数据点
2025-07-01 11:09:01.802 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 0
2025-07-01 11:09:01.802 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 33, 指标数量: 1
2025-07-01 11:09:02.404 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-07-01 11:09:02.405 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-01 11:09:02.407 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-07-01 11:09:02.408 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-07-01 11:09:06.424 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 34008 (D:\CETWorkSpace\ngap-server\target\classes started by dell in D:\CETWorkSpace\ngap-server)
2025-07-01 11:09:06.426 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-07-01 11:09:06.446 - INFO - [DeferredLog.java:225] : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-01 11:09:06.447 - INFO - [DeferredLog.java:225] : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-01 11:09:07.215 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-07-01 11:09:07.220 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-07-01 11:09:07.221 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-07-01 11:09:07.221 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-01 11:09:07.287 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-07-01 11:09:07.288 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 841 ms
2025-07-01 11:09:07.548 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-01 11:09:07.737 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-01 11:09:07.770 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-01 11:09:07.811 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-07-01 11:09:07.831 - INFO - [OptionalLiveReloadServer.java:58] : LiveReload server is running on port 35729
2025-07-01 11:09:07.899 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-07-01 11:09:07.922 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-07-01 11:09:07.932 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-07-01 11:09:07.934 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-07-01 11:09:07.944 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-07-01 11:09:07.962 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-07-01 11:09:08.081 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-07-01 11:09:08.097 - INFO - [TaskSchedule.java:38] : 开始删除
2025-07-01 11:09:08.097 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-07-01 11:09:08.099 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 1.913 seconds (JVM running for 2.227)
2025-07-01 11:09:08.101 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-01 11:09:08.102 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-01 11:09:08.102 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-07-01 11:09:08.102 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-01 11:09:08.115 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-07-01 11:09:08.185 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-01 11:09:08.186 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-01 11:09:08.187 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-07-01 11:09:08.199 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-07-01 11:09:08.199 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-07-01 11:09:08.199 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-07-01 11:09:08.200 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-07-01 11:09:08.467 - INFO - [DirectJDKLog.java:173] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 11:09:08.468 - INFO - [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-07-01 11:09:08.471 - INFO - [FrameworkServlet.java:547] : Completed initialization in 3 ms
2025-07-01 11:09:12.875 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=33
2025-07-01 11:09:12.875 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 33
2025-07-01 11:09:12.880 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 11:09:12.880 - WARN - [CsvUtils.java:41] : CSV目录不存在或不是有效目录: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 11:09:12.880 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 11:09:12.882 - INFO - [ExcelCacheManager.java:51] : Excel缓存管理器初始化完成，过期时间: 1800000ms
2025-07-01 11:09:12.884 - INFO - [VoltageObjectPool.java:46] : 电压对象池初始化完成
2025-07-01 11:09:12.885 - INFO - [VoltagePerformanceMonitor.java:28] : 电压性能监控器初始化完成
2025-07-01 11:09:12.885 - INFO - [ExcelUtils.java:50] : 检测到大文件(6.24MB)，使用优化处理器
2025-07-01 11:09:16.360 - INFO - [OptimizedExcelProcessor.java:94] : 指标提取完成: 文件=D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx, 耗时=3472ms, 指标数=1530
2025-07-01 11:09:16.361 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 荆门沙洋2025.xlsx 的电压指标，共 1 个指标
2025-07-01 11:09:16.361 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-07-01 11:09:16.362 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 33, 指标数量: 2
2025-07-01 11:09:16.425 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=33
2025-07-01 11:09:16.425 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 33
2025-07-01 11:09:16.426 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 11:09:16.426 - WARN - [SimulationServiceImpl.java:1880] : 仿真输出目录不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 11:09:16.427 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 33, 总数据点: 0, 合格数据点: 0, 合格率: 0.00%
2025-07-01 11:09:21.751 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=33, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3], startTimestamp=null)
2025-07-01 11:09:21.753 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 33, metricIds: [用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3]
2025-07-01 11:09:21.753 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 33, metricId: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:09:21.754 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:09:21.755 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv
2025-07-01 11:09:21.755 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 荆门沙洋2025.xlsx
2025-07-01 11:09:21.755 - INFO - [ExcelUtils.java:335] : 检测到大文件(6.24MB)，使用优化处理器获取指标数据
2025-07-01 11:09:21.755 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:09:25.170 - INFO - [OptimizedExcelProcessor.java:167] : 指标数据获取完成: 指标ID=用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3, 耗时=3415ms, 数据点数=2784
2025-07-01 11:09:25.170 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 2784 个数据点
2025-07-01 11:09:25.171 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 2784
2025-07-01 11:09:25.171 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 33, 指标数量: 1
2025-07-01 11:09:54.046 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=33, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3, 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2], startTimestamp=null)
2025-07-01 11:09:54.046 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 33, metricIds: [用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3, 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2]
2025-07-01 11:09:54.046 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 33, metricId: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:09:54.048 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:09:54.048 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv
2025-07-01 11:09:54.049 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 荆门沙洋2025.xlsx
2025-07-01 11:09:54.049 - INFO - [ExcelUtils.java:335] : 检测到大文件(6.24MB)，使用优化处理器获取指标数据
2025-07-01 11:09:54.049 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:09:54.050 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 2784 个数据点
2025-07-01 11:09:54.050 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 33, metricId: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2
2025-07-01 11:09:54.051 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2
2025-07-01 11:09:54.051 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv
2025-07-01 11:09:54.051 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 荆门沙洋2025.xlsx
2025-07-01 11:09:54.052 - INFO - [ExcelUtils.java:335] : 检测到大文件(6.24MB)，使用优化处理器获取指标数据
2025-07-01 11:09:54.052 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2
2025-07-01 11:09:56.935 - INFO - [OptimizedExcelProcessor.java:167] : 指标数据获取完成: 指标ID=用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2, 耗时=2883ms, 数据点数=2784
2025-07-01 11:09:56.936 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 2784 个数据点
2025-07-01 11:09:56.936 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 2, 失败: 0, 总共获取数据点: 5568
2025-07-01 11:09:56.936 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 33, 指标数量: 2
2025-07-01 11:09:58.888 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=33, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2], startTimestamp=null)
2025-07-01 11:09:58.888 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 33, metricIds: [用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2]
2025-07-01 11:09:58.888 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 33, metricId: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2
2025-07-01 11:09:58.889 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2
2025-07-01 11:09:58.890 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv
2025-07-01 11:09:58.890 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 荆门沙洋2025.xlsx
2025-07-01 11:09:58.890 - INFO - [ExcelUtils.java:335] : 检测到大文件(6.24MB)，使用优化处理器获取指标数据
2025-07-01 11:09:58.891 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2
2025-07-01 11:09:58.891 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 2784 个数据点
2025-07-01 11:09:58.892 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 2784
2025-07-01 11:09:58.892 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 33, 指标数量: 1
2025-07-01 11:10:02.446 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=33, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2, 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-1], startTimestamp=null)
2025-07-01 11:10:02.446 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 33, metricIds: [用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2, 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-1]
2025-07-01 11:10:02.446 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 33, metricId: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2
2025-07-01 11:10:02.448 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2
2025-07-01 11:10:02.448 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv
2025-07-01 11:10:02.449 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 荆门沙洋2025.xlsx
2025-07-01 11:10:02.449 - INFO - [ExcelUtils.java:335] : 检测到大文件(6.24MB)，使用优化处理器获取指标数据
2025-07-01 11:10:02.449 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-2
2025-07-01 11:10:02.449 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 2784 个数据点
2025-07-01 11:10:02.449 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 33, metricId: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-1
2025-07-01 11:10:02.450 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-1
2025-07-01 11:10:02.450 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv
2025-07-01 11:10:02.450 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 荆门沙洋2025.xlsx
2025-07-01 11:10:02.450 - INFO - [ExcelUtils.java:335] : 检测到大文件(6.24MB)，使用优化处理器获取指标数据
2025-07-01 11:10:02.451 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-1
2025-07-01 11:10:05.509 - INFO - [OptimizedExcelProcessor.java:167] : 指标数据获取完成: 指标ID=用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-1, 耗时=3058ms, 数据点数=2784
2025-07-01 11:10:05.509 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 2784 个数据点
2025-07-01 11:10:05.509 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 2, 失败: 0, 总共获取数据点: 5568
2025-07-01 11:10:05.509 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 33, 指标数量: 2
2025-07-01 11:10:06.194 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=33, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-1], startTimestamp=null)
2025-07-01 11:10:06.195 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 33, metricIds: [用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-1]
2025-07-01 11:10:06.195 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 33, metricId: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-1
2025-07-01 11:10:06.197 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-1
2025-07-01 11:10:06.197 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv
2025-07-01 11:10:06.197 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 荆门沙洋2025.xlsx
2025-07-01 11:10:06.198 - INFO - [ExcelUtils.java:335] : 检测到大文件(6.24MB)，使用优化处理器获取指标数据
2025-07-01 11:10:06.198 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4219925933088.用户-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-1
2025-07-01 11:10:06.198 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 2784 个数据点
2025-07-01 11:10:06.199 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 2784
2025-07-01 11:10:06.199 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 33, 指标数量: 1
2025-07-01 11:10:21.972 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=33
2025-07-01 11:10:21.973 - INFO - [SimulationServiceImpl.java:1713] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/33/measured_data/电压合格率报表.xlsx
2025-07-01 11:10:21.975 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=33, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=1, pageSize=50)
2025-07-01 11:10:21.976 - INFO - [SimulationServiceImpl.java:1769] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/33/measured_data/电压合格率报表.xlsx
2025-07-01 11:10:25.618 -ERROR - [VoltageDataProcessor.java:235] : 读取小文件失败: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
java.lang.ClassCastException: com.cet.electric.ngapserver.util.excel.OptimizedExcelProcessor$ColumnAnalysisResult cannot be cast to java.util.Map
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.getColumnIndexCached(VoltageDataProcessor.java:249)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:225)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getCourtsVoltageReport(SimulationServiceImpl.java:1719)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getCourtsVoltageReport(SimulationController.java:293)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-01 11:10:25.618 - INFO - [VoltageDataProcessor.java:237] : 传统方式失败，尝试流式读取: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 11:10:25.618 - INFO - [VoltageDataProcessor.java:93] : 尝试使用增强内存限制读取大文件: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 11:10:25.666 -ERROR - [VoltageDataProcessor.java:235] : 读取小文件失败: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
java.lang.ClassCastException: com.cet.electric.ngapserver.util.excel.OptimizedExcelProcessor$ColumnAnalysisResult cannot be cast to java.util.Map
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.getColumnIndexCached(VoltageDataProcessor.java:249)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:225)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getUserVoltageReport(SimulationServiceImpl.java:1775)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getUserVoltageReport(SimulationController.java:309)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-01 11:10:25.667 - INFO - [VoltageDataProcessor.java:237] : 传统方式失败，尝试流式读取: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 11:10:25.667 - INFO - [VoltageDataProcessor.java:93] : 尝试使用增强内存限制读取大文件: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 11:10:28.975 - INFO - [VoltageDataProcessor.java:104] : 成功打开工作表，开始处理数据...
2025-07-01 11:10:28.976 -ERROR - [SimulationServiceImpl.java:1732] : 获取电压报表数据时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processLargeFileInSmallBatches(VoltageDataProcessor.java:178)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readLargeFileWithStreaming(VoltageDataProcessor.java:110)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:238)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getCourtsVoltageReport(SimulationServiceImpl.java:1719)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getCourtsVoltageReport(SimulationController.java:293)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-01 11:10:28.978 -ERROR - [GlobalExceptionHandler.java:22] : null
com.cet.electric.ngapserver.web.controller.SimulationController.getCourtsVoltageReport(SimulationController.java:295)
sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
java.lang.reflect.Method.invoke(Method.java:498)
org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.lang.Thread.run(Thread.java:748)
2025-07-01 11:10:28.979 - WARN - [AbstractHandlerExceptionResolver.java:199] : Resolved [java.lang.NullPointerException]
2025-07-01 11:10:29.017 - INFO - [VoltageDataProcessor.java:104] : 成功打开工作表，开始处理数据...
2025-07-01 11:10:29.017 -ERROR - [SimulationServiceImpl.java:1788] : 获取用户电压报表数据时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processLargeFileInSmallBatches(VoltageDataProcessor.java:178)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readLargeFileWithStreaming(VoltageDataProcessor.java:110)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:238)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getUserVoltageReport(SimulationServiceImpl.java:1775)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getUserVoltageReport(SimulationController.java:309)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-01 11:10:29.018 - INFO - [SimulationServiceImpl.java:1822] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/33/measured_data/电压合格率报表.xlsx
2025-07-01 11:10:31.951 -ERROR - [VoltageDataProcessor.java:235] : 读取小文件失败: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
java.lang.ClassCastException: com.cet.electric.ngapserver.util.excel.OptimizedExcelProcessor$ColumnAnalysisResult cannot be cast to java.util.Map
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.getColumnIndexCached(VoltageDataProcessor.java:249)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:225)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.countUserVoltageReport(SimulationServiceImpl.java:1828)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getUserVoltageReport(SimulationController.java:315)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-01 11:10:31.951 - INFO - [VoltageDataProcessor.java:237] : 传统方式失败，尝试流式读取: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 11:10:31.951 - INFO - [VoltageDataProcessor.java:93] : 尝试使用增强内存限制读取大文件: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 11:10:34.900 - INFO - [VoltageDataProcessor.java:104] : 成功打开工作表，开始处理数据...
2025-07-01 11:10:34.900 -ERROR - [SimulationServiceImpl.java:1841] : 获取用户电压报表记录数时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processLargeFileInSmallBatches(VoltageDataProcessor.java:178)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readLargeFileWithStreaming(VoltageDataProcessor.java:110)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:238)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.countUserVoltageReport(SimulationServiceImpl.java:1828)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getUserVoltageReport(SimulationController.java:315)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-01 11:10:34.900 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 33, 用户数量: 0
2025-07-01 11:11:21.989 - INFO - [SimulationController.java:156] : 接收到上传文件请求，文件名：荆门沙洋2025.xlsx, 文件类型：measured_data, 文件大小：7221851
2025-07-01 11:11:22.001 - INFO - [SimulationServiceImpl.java:794] : 成功上传文件到仿真任务，仿真ID: 33, 文件类型: measured_data, 文件路径: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 11:11:22.109 - INFO - [SimulationController.java:162] : 文件上传成功，保存路径：D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 11:11:22.119 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-07-01 11:11:22.119 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-07-01 11:11:22.189 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 2 条数据
2025-07-01 11:11:22.191 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 2
2025-07-01 11:11:25.085 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=33, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=1, pageSize=50)
2025-07-01 11:11:25.087 - INFO - [SimulationServiceImpl.java:1769] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/33/measured_data/电压合格率报表.xlsx
2025-07-01 11:11:25.092 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=33
2025-07-01 11:11:25.093 - INFO - [SimulationServiceImpl.java:1713] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/33/measured_data/电压合格率报表.xlsx
2025-07-01 11:11:27.606 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx -> D:\CETWorkSpace\ngap-server\data\26\33\measured_data\电压合格率报表.xlsx, 处理记录数: 13241, 耗时: 5605ms
2025-07-01 11:11:30.724 -ERROR - [SimulationServiceImpl.java:1732] : 获取电压报表数据时发生错误
java.lang.IllegalArgumentException: This Style does not belong to the supplied Workbook Styles Source. Are you trying to assign a style from one workbook to the cell of a different workbook?
	at org.apache.poi.xssf.usermodel.XSSFCellStyle.verifyBelongsToStylesSource(XSSFCellStyle.java:123)
	at org.apache.poi.xssf.usermodel.XSSFCell.setCellStyle(XSSFCell.java:626)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.createReportContent(VoltageQualityReport.java:675)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateReportOptimized(VoltageQualityReport.java:268)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$4(VoltageQualityReport.java:93)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:236)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:92)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getCourtsVoltageReport(SimulationServiceImpl.java:1719)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getCourtsVoltageReport(SimulationController.java:293)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-01 11:11:30.724 -ERROR - [GlobalExceptionHandler.java:22] : null
com.cet.electric.ngapserver.web.controller.SimulationController.getCourtsVoltageReport(SimulationController.java:295)
sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
java.lang.reflect.Method.invoke(Method.java:498)
org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.lang.Thread.run(Thread.java:748)
2025-07-01 11:11:30.725 - WARN - [AbstractHandlerExceptionResolver.java:199] : Resolved [java.lang.NullPointerException]
2025-07-01 11:11:30.824 -ERROR - [SimulationServiceImpl.java:1788] : 获取用户电压报表数据时发生错误
java.lang.IllegalArgumentException: This Style does not belong to the supplied Workbook Styles Source. Are you trying to assign a style from one workbook to the cell of a different workbook?
	at org.apache.poi.xssf.usermodel.XSSFCellStyle.verifyBelongsToStylesSource(XSSFCellStyle.java:123)
	at org.apache.poi.xssf.usermodel.XSSFCell.setCellStyle(XSSFCell.java:626)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.createReportContent(VoltageQualityReport.java:675)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateReportOptimized(VoltageQualityReport.java:268)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$4(VoltageQualityReport.java:93)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:236)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:92)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getUserVoltageReport(SimulationServiceImpl.java:1775)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getUserVoltageReport(SimulationController.java:309)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-01 11:11:30.847 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 33, 用户数量: 312
2025-07-01 11:13:06.339 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-07-01 11:13:06.341 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-01 11:13:06.342 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-07-01 11:13:06.343 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-07-01 11:13:10.914 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 29544 (D:\CETWorkSpace\ngap-server\target\classes started by dell in D:\CETWorkSpace\ngap-server)
2025-07-01 11:13:10.915 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-07-01 11:13:10.933 - INFO - [DeferredLog.java:225] : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-01 11:13:10.933 - INFO - [DeferredLog.java:225] : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-01 11:13:12.336 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-07-01 11:13:12.342 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-07-01 11:13:12.343 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-07-01 11:13:12.343 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-01 11:13:12.441 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-07-01 11:13:12.441 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 1507 ms
2025-07-01 11:13:12.691 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-01 11:13:12.886 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-01 11:13:12.927 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-01 11:13:12.970 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-07-01 11:13:12.989 - INFO - [OptionalLiveReloadServer.java:58] : LiveReload server is running on port 35729
2025-07-01 11:13:13.054 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-07-01 11:13:13.074 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-07-01 11:13:13.086 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-07-01 11:13:13.087 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-07-01 11:13:13.098 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-07-01 11:13:13.118 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-07-01 11:13:13.237 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-07-01 11:13:13.252 - INFO - [TaskSchedule.java:38] : 开始删除
2025-07-01 11:13:13.252 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-07-01 11:13:13.254 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 2.618 seconds (JVM running for 2.949)
2025-07-01 11:13:13.257 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-01 11:13:13.257 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-01 11:13:13.257 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-07-01 11:13:13.257 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-01 11:13:13.270 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-07-01 11:13:13.335 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-01 11:13:13.335 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-01 11:13:13.336 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-07-01 11:13:13.346 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-07-01 11:13:13.346 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-07-01 11:13:13.346 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-07-01 11:13:13.346 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-07-01 11:13:13.425 - INFO - [DirectJDKLog.java:173] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 11:13:13.425 - INFO - [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-07-01 11:13:13.428 - INFO - [FrameworkServlet.java:547] : Completed initialization in 3 ms
2025-07-01 11:13:18.961 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=33
2025-07-01 11:13:18.967 - INFO - [ExcelCacheManager.java:51] : Excel缓存管理器初始化完成，过期时间: 1800000ms
2025-07-01 11:13:18.968 - INFO - [VoltageObjectPool.java:46] : 电压对象池初始化完成
2025-07-01 11:13:18.975 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=33, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=1, pageSize=50)
2025-07-01 11:13:19.396 - INFO - [SimulationController.java:294] : 获取台区电压统计报表成功，台区ID: 33, 用户总数: 312, 合格率: 99.11161219734159
2025-07-01 11:13:19.415 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 33, 用户数量: 312
2025-07-01 11:13:26.121 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=33
2025-07-01 11:13:26.121 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 33
2025-07-01 11:13:26.124 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 11:13:26.124 - WARN - [CsvUtils.java:41] : CSV目录不存在或不是有效目录: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 11:13:26.124 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx
2025-07-01 11:13:26.127 - INFO - [VoltagePerformanceMonitor.java:28] : 电压性能监控器初始化完成
2025-07-01 11:13:26.128 - INFO - [ExcelUtils.java:50] : 检测到大文件(6.89MB)，使用优化处理器
2025-07-01 11:13:30.365 - INFO - [OptimizedExcelProcessor.java:94] : 指标提取完成: 文件=D:\CETWorkSpace\ngap-server/data/26/33/measured_data/荆门沙洋2025.xlsx, 耗时=4235ms, 指标数=1530
2025-07-01 11:13:30.366 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 荆门沙洋2025.xlsx 的电压指标，共 1 个指标
2025-07-01 11:13:30.367 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-07-01 11:13:30.367 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 33, 指标数量: 2
2025-07-01 11:13:30.431 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=33
2025-07-01 11:13:30.431 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 33
2025-07-01 11:13:30.434 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 11:13:30.434 - WARN - [SimulationServiceImpl.java:1880] : 仿真输出目录不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script
2025-07-01 11:13:30.434 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 33, 总数据点: 0, 合格数据点: 0, 合格率: 0.00%
2025-07-01 11:13:35.403 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=33, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4219925933088.用户名称-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3], startTimestamp=null)
2025-07-01 11:13:35.403 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 33, metricIds: [用户电压.用户编号-4219925933088.用户名称-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3]
2025-07-01 11:13:35.404 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 33, metricId: 用户电压.用户编号-4219925933088.用户名称-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:13:35.405 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4219925933088.用户名称-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:13:35.406 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv
2025-07-01 11:13:35.406 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 荆门沙洋2025.xlsx
2025-07-01 11:13:35.407 - INFO - [ExcelUtils.java:335] : 检测到大文件(6.89MB)，使用优化处理器获取指标数据
2025-07-01 11:13:35.407 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4219925933088.用户名称-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:13:38.400 - INFO - [OptimizedExcelProcessor.java:167] : 指标数据获取完成: 指标ID=用户电压.用户编号-4219925933088.用户名称-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3, 耗时=2993ms, 数据点数=2784
2025-07-01 11:13:38.400 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 2784 个数据点
2025-07-01 11:13:38.400 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 2784
2025-07-01 11:13:38.401 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 33, 指标数量: 1
2025-07-01 11:14:19.532 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-07-01 11:14:19.532 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-07-01 11:14:19.533 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-07-01 11:14:19.534 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-07-01 11:14:19.539 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-07-01 11:14:19.540 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-07-01 11:14:19.558 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 9 条数据
2025-07-01 11:14:19.558 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-07-01 11:14:19.559 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 9 个项目
2025-07-01 11:14:19.560 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 9
2025-07-01 11:14:20.998 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-07-01 11:14:20.998 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-07-01 11:14:21.004 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 2 条数据
2025-07-01 11:14:21.005 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 2
2025-07-01 11:14:36.695 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=33
2025-07-01 11:14:36.695 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=33, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=1, pageSize=50)
2025-07-01 11:14:36.707 - INFO - [SimulationController.java:294] : 获取台区电压统计报表成功，台区ID: 33, 用户总数: 312, 合格率: 99.11161219734159
2025-07-01 11:14:36.723 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 33, 用户数量: 312
2025-07-01 11:15:29.844 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=33, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4219925933088.用户名称-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3], startTimestamp=null)
2025-07-01 11:15:29.845 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 33, metricIds: [用户电压.用户编号-4219925933088.用户名称-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3]
2025-07-01 11:15:29.845 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 33, metricId: 用户电压.用户编号-4219925933088.用户名称-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:15:29.846 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4219925933088.用户名称-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:15:29.846 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv
2025-07-01 11:15:29.846 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 荆门沙洋2025.xlsx
2025-07-01 11:15:29.847 - INFO - [ExcelUtils.java:335] : 检测到大文件(6.89MB)，使用优化处理器获取指标数据
2025-07-01 11:15:29.847 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4219925933088.用户名称-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:15:29.848 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 2784 个数据点
2025-07-01 11:15:29.848 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 2784
2025-07-01 11:15:29.848 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 33, 指标数量: 1
2025-07-01 11:17:58.958 - INFO - [SimulationController.java:60] : 接收到创建仿真任务请求: projectId=26, simulationName=对比
2025-07-01 11:17:59.208 - INFO - [SimulationServiceImpl.java:218] : 仿真任务创建成功: Simulation(simulationId=34, projectId=26, projectName=安徽六安低电压台区, simulationName=对比, simulationModel=null, simulationDraft=null, inputData=null, outputData=null, measuredData=null, simulationScript=null, nodes=null, controlStrategy=null, runStatus=READY, createdAt=1751339878959, updatedAt=1751339878959, isDeleted=0)
2025-07-01 11:17:59.209 - INFO - [SimulationServiceImpl.java:228] : 文件夹创建成功: D:\CETWorkSpace\ngap-server/data/26/34
2025-07-01 11:17:59.209 - INFO - [SimulationController.java:73] : 仿真任务创建成功，ID: 34
2025-07-01 11:17:59.225 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-07-01 11:17:59.225 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-07-01 11:17:59.227 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 3 条数据
2025-07-01 11:17:59.228 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 3
2025-07-01 11:18:09.886 - INFO - [SimulationController.java:156] : 接收到上传文件请求，文件名：荆门沙洋.xlsx, 文件类型：measured_data, 文件大小：6510539
2025-07-01 11:18:09.891 - INFO - [SimulationServiceImpl.java:794] : 成功上传文件到仿真任务，仿真ID: 34, 文件类型: measured_data, 文件路径: D:\CETWorkSpace\ngap-server/data/26/34/measured_data/荆门沙洋.xlsx
2025-07-01 11:18:10.335 - INFO - [SimulationController.java:162] : 文件上传成功，保存路径：D:\CETWorkSpace\ngap-server/data/26/34/measured_data/荆门沙洋.xlsx
2025-07-01 11:18:10.347 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-07-01 11:18:10.348 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-07-01 11:18:10.349 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 3 条数据
2025-07-01 11:18:10.350 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 3
2025-07-01 11:18:12.377 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=34
2025-07-01 11:18:12.377 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=34, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=1, pageSize=50)
2025-07-01 11:18:12.378 - INFO - [SimulationServiceImpl.java:1769] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/34/measured_data/电压合格率报表.xlsx
2025-07-01 11:18:12.390 - INFO - [SimulationServiceImpl.java:1713] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/34/measured_data/电压合格率报表.xlsx
2025-07-01 11:18:15.453 -ERROR - [SimulationServiceImpl.java:1732] : 获取电压报表数据时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processRowsInBatches(VoltageDataProcessor.java:291)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:228)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getCourtsVoltageReport(SimulationServiceImpl.java:1719)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getCourtsVoltageReport(SimulationController.java:293)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-01 11:18:15.468 -ERROR - [GlobalExceptionHandler.java:22] : null
com.cet.electric.ngapserver.web.controller.SimulationController.getCourtsVoltageReport(SimulationController.java:295)
sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
java.lang.reflect.Method.invoke(Method.java:498)
org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.lang.Thread.run(Thread.java:748)
2025-07-01 11:18:15.469 - WARN - [AbstractHandlerExceptionResolver.java:199] : Resolved [java.lang.NullPointerException]
2025-07-01 11:18:15.472 -ERROR - [SimulationServiceImpl.java:1788] : 获取用户电压报表数据时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processRowsInBatches(VoltageDataProcessor.java:291)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:228)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getUserVoltageReport(SimulationServiceImpl.java:1775)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getUserVoltageReport(SimulationController.java:309)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-01 11:18:15.473 - INFO - [SimulationServiceImpl.java:1822] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/34/measured_data/电压合格率报表.xlsx
2025-07-01 11:18:17.789 -ERROR - [SimulationServiceImpl.java:1841] : 获取用户电压报表记录数时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processRowsInBatches(VoltageDataProcessor.java:291)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:228)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.countUserVoltageReport(SimulationServiceImpl.java:1828)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getUserVoltageReport(SimulationController.java:315)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-01 11:18:17.790 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 34, 用户数量: 0
2025-07-01 11:18:20.556 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-07-01 11:18:20.556 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-07-01 11:18:20.557 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-07-01 11:18:20.557 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-07-01 11:18:20.557 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-07-01 11:18:20.557 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-07-01 11:18:20.559 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 9 条数据
2025-07-01 11:18:20.559 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-07-01 11:18:20.559 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 9 个项目
2025-07-01 11:18:20.560 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 9
2025-07-01 11:18:21.819 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-07-01 11:18:21.819 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-07-01 11:18:21.821 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 3 条数据
2025-07-01 11:18:21.822 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 3
2025-07-01 11:18:25.026 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=34
2025-07-01 11:18:25.027 - INFO - [SimulationServiceImpl.java:1713] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/34/measured_data/电压合格率报表.xlsx
2025-07-01 11:18:25.029 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=34, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=1, pageSize=50)
2025-07-01 11:18:25.030 - INFO - [SimulationServiceImpl.java:1769] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/34/measured_data/电压合格率报表.xlsx
2025-07-01 11:18:27.995 -ERROR - [SimulationServiceImpl.java:1788] : 获取用户电压报表数据时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processRowsInBatches(VoltageDataProcessor.java:291)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:228)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getUserVoltageReport(SimulationServiceImpl.java:1775)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getUserVoltageReport(SimulationController.java:309)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-01 11:18:27.995 - INFO - [SimulationServiceImpl.java:1822] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/34/measured_data/电压合格率报表.xlsx
2025-07-01 11:18:27.996 -ERROR - [SimulationServiceImpl.java:1732] : 获取电压报表数据时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processRowsInBatches(VoltageDataProcessor.java:291)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:228)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getCourtsVoltageReport(SimulationServiceImpl.java:1719)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getCourtsVoltageReport(SimulationController.java:293)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-01 11:18:27.997 -ERROR - [GlobalExceptionHandler.java:22] : null
com.cet.electric.ngapserver.web.controller.SimulationController.getCourtsVoltageReport(SimulationController.java:295)
sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
java.lang.reflect.Method.invoke(Method.java:498)
org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.lang.Thread.run(Thread.java:748)
2025-07-01 11:18:27.997 - WARN - [AbstractHandlerExceptionResolver.java:199] : Resolved [java.lang.NullPointerException]
2025-07-01 11:18:30.263 -ERROR - [SimulationServiceImpl.java:1841] : 获取用户电压报表记录数时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processRowsInBatches(VoltageDataProcessor.java:291)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:228)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.countUserVoltageReport(SimulationServiceImpl.java:1828)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getUserVoltageReport(SimulationController.java:315)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-01 11:18:30.264 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 34, 用户数量: 0
2025-07-01 11:18:35.927 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=34
2025-07-01 11:18:35.927 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 34
2025-07-01 11:18:35.928 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/26/34/simulation_script
2025-07-01 11:18:35.929 - WARN - [CsvUtils.java:41] : CSV目录不存在或不是有效目录: D:\CETWorkSpace\ngap-server/data/26/34/simulation_script
2025-07-01 11:18:35.929 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/26/34/measured_data/荆门沙洋.xlsx
2025-07-01 11:18:35.929 - INFO - [ExcelUtils.java:50] : 检测到大文件(6.21MB)，使用优化处理器
2025-07-01 11:18:38.500 - INFO - [OptimizedExcelProcessor.java:94] : 指标提取完成: 文件=D:\CETWorkSpace\ngap-server/data/26/34/measured_data/荆门沙洋.xlsx, 耗时=2571ms, 指标数=1528
2025-07-01 11:18:38.500 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 荆门沙洋.xlsx 的电压指标，共 1 个指标
2025-07-01 11:18:38.500 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-07-01 11:18:38.500 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 34, 指标数量: 2
2025-07-01 11:18:38.572 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=34
2025-07-01 11:18:38.573 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 34
2025-07-01 11:18:38.573 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/26/34/simulation_script
2025-07-01 11:18:38.573 - WARN - [SimulationServiceImpl.java:1880] : 仿真输出目录不存在: D:\CETWorkSpace\ngap-server/data/26/34/simulation_script
2025-07-01 11:18:38.573 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 34, 总数据点: 0, 合格数据点: 0, 合格率: 0.00%
2025-07-01 11:18:42.952 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=34, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4202030272980.用户-董加荣.电压等级-220.相别-1], startTimestamp=null)
2025-07-01 11:18:42.952 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 34, metricIds: [用户电压.用户编号-4202030272980.用户-董加荣.电压等级-220.相别-1]
2025-07-01 11:18:42.952 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 34, metricId: 用户电压.用户编号-4202030272980.用户-董加荣.电压等级-220.相别-1
2025-07-01 11:18:42.954 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/26/34/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4202030272980.用户-董加荣.电压等级-220.相别-1
2025-07-01 11:18:42.954 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/26/34/simulation_script/用户电压.csv
2025-07-01 11:18:42.954 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 荆门沙洋.xlsx
2025-07-01 11:18:42.954 - INFO - [ExcelUtils.java:335] : 检测到大文件(6.21MB)，使用优化处理器获取指标数据
2025-07-01 11:18:42.954 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4202030272980.用户-董加荣.电压等级-220.相别-1
2025-07-01 11:18:45.237 -ERROR - [OptimizedExcelProcessor.java:173] : 获取指标数据失败: java.util.HashMap cannot be cast to com.cet.electric.ngapserver.util.excel.OptimizedExcelProcessor$ColumnAnalysisResult
java.lang.ClassCastException: java.util.HashMap cannot be cast to com.cet.electric.ngapserver.util.excel.OptimizedExcelProcessor$ColumnAnalysisResult
	at com.cet.electric.ngapserver.util.excel.OptimizedExcelProcessor.analyzeColumnsWithCache(OptimizedExcelProcessor.java:440)
	at com.cet.electric.ngapserver.util.excel.OptimizedExcelProcessor.getMetricDataOptimized(OptimizedExcelProcessor.java:143)
	at com.cet.electric.ngapserver.util.ExcelUtils.getMetricData(ExcelUtils.java:336)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getExcelMetricData(SimulationServiceImpl.java:1453)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getSimulationMetricData(SimulationServiceImpl.java:1375)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getMultipleSimulationMetricData(SimulationServiceImpl.java:1510)
	at com.cet.electric.ngapserver.web.controller.SimulationController.batchGetSimulationMetricData(SimulationController.java:263)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-01 11:18:45.238 - WARN - [SimulationServiceImpl.java:1461] : 未找到Excel电压指标数据: metricId=用户电压.用户编号-4202030272980.用户-董加荣.电压等级-220.相别-1
2025-07-01 11:18:45.238 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 0
2025-07-01 11:18:45.238 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 34, 指标数量: 0
2025-07-01 11:18:52.371 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-07-01 11:18:52.371 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-07-01 11:18:52.372 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 3 条数据
2025-07-01 11:18:52.372 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 3
2025-07-01 11:18:53.353 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-07-01 11:18:53.354 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-07-01 11:18:53.354 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 3 条数据
2025-07-01 11:18:53.355 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 3
2025-07-01 11:18:55.187 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=34
2025-07-01 11:18:55.187 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=34, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=1, pageSize=50)
2025-07-01 11:18:55.189 - INFO - [SimulationServiceImpl.java:1713] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/34/measured_data/电压合格率报表.xlsx
2025-07-01 11:18:55.190 - INFO - [SimulationServiceImpl.java:1769] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/34/measured_data/电压合格率报表.xlsx
2025-07-01 11:18:58.210 -ERROR - [SimulationServiceImpl.java:1788] : 获取用户电压报表数据时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processRowsInBatches(VoltageDataProcessor.java:291)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:228)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getUserVoltageReport(SimulationServiceImpl.java:1775)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getUserVoltageReport(SimulationController.java:309)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-01 11:18:58.212 - INFO - [SimulationServiceImpl.java:1822] : 电压合格率报表文件不存在，开始生成报表：D:\CETWorkSpace\ngap-server/data/26/34/measured_data/电压合格率报表.xlsx
2025-07-01 11:18:58.219 -ERROR - [SimulationServiceImpl.java:1732] : 获取电压报表数据时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processRowsInBatches(VoltageDataProcessor.java:291)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:228)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.getCourtsVoltageReport(SimulationServiceImpl.java:1719)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getCourtsVoltageReport(SimulationController.java:293)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-01 11:18:58.220 -ERROR - [GlobalExceptionHandler.java:22] : null
com.cet.electric.ngapserver.web.controller.SimulationController.getCourtsVoltageReport(SimulationController.java:295)
sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
java.lang.reflect.Method.invoke(Method.java:498)
org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.lang.Thread.run(Thread.java:748)
2025-07-01 11:18:58.220 - WARN - [AbstractHandlerExceptionResolver.java:199] : Resolved [java.lang.NullPointerException]
2025-07-01 11:19:00.422 -ERROR - [SimulationServiceImpl.java:1841] : 获取用户电压报表记录数时发生错误
java.lang.IllegalArgumentException: Column with name '用户名称' not found.
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.processRowsInBatches(VoltageDataProcessor.java:291)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readSmallFileWithWorkbook(VoltageDataProcessor.java:228)
	at com.cet.electric.ngapserver.util.voltage.VoltageDataProcessor.readVoltageDataOptimized(VoltageDataProcessor.java:71)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.lambda$generateVoltageReport$0(VoltageQualityReport.java:70)
	at com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor.timeOperation(VoltagePerformanceMonitor.java:226)
	at com.cet.electric.ngapserver.util.VoltageQualityReport.generateVoltageReport(VoltageQualityReport.java:69)
	at com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.countUserVoltageReport(SimulationServiceImpl.java:1828)
	at com.cet.electric.ngapserver.web.controller.SimulationController.getUserVoltageReport(SimulationController.java:315)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-01 11:19:00.422 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 34, 用户数量: 0
2025-07-01 11:19:02.494 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-07-01 11:19:02.494 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-07-01 11:19:02.494 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-07-01 11:19:02.494 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-07-01 11:19:02.494 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-07-01 11:19:02.494 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-07-01 11:19:02.496 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 9 条数据
2025-07-01 11:19:02.496 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-07-01 11:19:02.497 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 9 个项目
2025-07-01 11:19:02.497 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 9
2025-07-01 11:19:03.775 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-07-01 11:19:03.775 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-07-01 11:19:03.777 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 3 条数据
2025-07-01 11:19:03.779 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 3
2025-07-01 11:20:36.516 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-07-01 11:20:36.518 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-01 11:20:36.519 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-07-01 11:20:36.520 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-07-01 11:21:26.229 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 24192 (D:\CETWorkSpace\ngap-server\target\classes started by dell in D:\CETWorkSpace\ngap-server)
2025-07-01 11:21:26.230 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-07-01 11:21:26.248 - INFO - [DeferredLog.java:225] : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-01 11:21:26.248 - INFO - [DeferredLog.java:225] : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-01 11:21:27.608 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-07-01 11:21:27.614 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-07-01 11:21:27.615 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-07-01 11:21:27.615 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-01 11:21:27.687 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-07-01 11:21:27.687 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 1439 ms
2025-07-01 11:21:28.348 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-01 11:21:29.003 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-01 11:21:29.042 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-01 11:21:29.100 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-07-01 11:21:29.132 - INFO - [OptionalLiveReloadServer.java:58] : LiveReload server is running on port 35729
2025-07-01 11:21:29.233 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-07-01 11:21:29.278 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-07-01 11:21:29.298 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-07-01 11:21:29.299 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-07-01 11:21:29.309 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-07-01 11:21:29.367 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-07-01 11:21:29.568 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-07-01 11:21:29.618 - INFO - [TaskSchedule.java:38] : 开始删除
2025-07-01 11:21:29.618 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-07-01 11:21:29.623 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 3.657 seconds (JVM running for 4.077)
2025-07-01 11:21:29.627 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-01 11:21:29.627 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-01 11:21:29.627 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-07-01 11:21:29.628 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-01 11:21:29.641 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-07-01 11:21:29.910 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-01 11:21:29.912 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-07-01 11:21:29.913 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-01 11:21:29.925 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-07-01 11:21:29.925 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-07-01 11:21:30.008 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-07-01 11:21:30.009 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-07-01 11:21:30.240 - INFO - [DirectJDKLog.java:173] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 11:21:30.240 - INFO - [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-07-01 11:21:30.245 - INFO - [FrameworkServlet.java:547] : Completed initialization in 5 ms
2025-07-01 11:21:36.896 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=33, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4219925933088.用户名称-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3], startTimestamp=null)
2025-07-01 11:21:36.896 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 33, metricIds: [用户电压.用户编号-4219925933088.用户名称-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3]
2025-07-01 11:21:36.896 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 33, metricId: 用户电压.用户编号-4219925933088.用户名称-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:21:36.920 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4219925933088.用户名称-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:21:36.921 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/26/33/simulation_script/用户电压.csv
2025-07-01 11:21:36.921 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 荆门沙洋2025.xlsx
2025-07-01 11:21:36.956 - INFO - [ExcelCacheManager.java:51] : Excel缓存管理器初始化完成，过期时间: 1800000ms
2025-07-01 11:21:36.980 - INFO - [VoltageObjectPool.java:46] : 电压对象池初始化完成
2025-07-01 11:21:36.984 - INFO - [VoltagePerformanceMonitor.java:28] : 电压性能监控器初始化完成
2025-07-01 11:21:36.986 - INFO - [ExcelUtils.java:335] : 检测到大文件(6.89MB)，使用优化处理器获取指标数据
2025-07-01 11:21:36.993 - INFO - [OptimizedExcelProcessor.java:113] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4219925933088.用户名称-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3
2025-07-01 11:21:41.218 - INFO - [OptimizedExcelProcessor.java:167] : 指标数据获取完成: 指标ID=用户电压.用户编号-4219925933088.用户名称-十堰堰疆新能源有限公司荆门分公司.电压等级-380.相别-3, 耗时=4225ms, 数据点数=2784
2025-07-01 11:21:41.235 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 2784 个数据点
2025-07-01 11:21:41.239 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 2784
2025-07-01 11:21:41.239 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 33, 指标数量: 1
2025-07-01 11:21:55.901 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-07-01 11:21:55.902 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-01 11:21:55.904 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-07-01 11:21:55.907 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-07-01 11:22:00.940 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplicationTests on DESKTOP-9LFKQ75 with PID 24620 (started by dell in D:\CETWorkSpace\ngap-server)
2025-07-01 11:22:00.942 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-07-01 11:22:02.567 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-01 11:22:03.120 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-01 11:22:03.234 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-01 11:22:03.466 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-07-01 11:22:03.674 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-07-01 11:22:03.700 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-07-01 11:22:03.724 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-07-01 11:22:03.748 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-07-01 11:22:03.946 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-07-01 11:22:03.965 - INFO - [TaskSchedule.java:38] : 开始删除
2025-07-01 11:22:03.966 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-07-01 11:22:03.969 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplicationTests in 3.584 seconds (JVM running for 10.483)
2025-07-01 11:22:03.988 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-07-01 11:22:03.992 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-01 11:22:03.992 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-01 11:22:03.992 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-07-01 11:22:03.992 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-01 11:22:04.126 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-01 11:22:04.126 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-01 11:22:04.177 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-07-01 11:22:04.197 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-07-01 11:22:04.197 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-07-01 11:22:04.199 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-07-01 11:22:04.199 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-07-01 11:22:05.748 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:05.749 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:05.749 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:05.779 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=1
2025-07-01 11:22:05.780 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 1, 指标数量: 2
2025-07-01 11:22:05.831 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:05.831 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:05.831 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:05.851 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=1, queryDTO=UserVoltageReportQueryDTO(keyword=用户, pageNum=1, pageSize=10)
2025-07-01 11:22:05.851 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 1, 用户数量: 2
2025-07-01 11:22:05.860 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:05.861 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:05.861 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:05.863 - INFO - [SimulationController.java:60] : 接收到创建仿真任务请求: projectId=100, simulationName=测试仿真
2025-07-01 11:22:05.863 - INFO - [SimulationController.java:73] : 仿真任务创建成功，ID: 1
2025-07-01 11:22:05.871 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:05.871 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:05.871 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:05.872 - INFO - [SimulationController.java:183] : 接收到获取仿真策略列表请求
2025-07-01 11:22:05.872 - INFO - [SimulationController.java:186] : 仿真策略列表获取成功，总数量: 2
2025-07-01 11:22:05.878 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:05.879 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:05.879 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:05.880 - INFO - [SimulationController.java:118] : 接收到删除仿真任务请求: simulationId=1
2025-07-01 11:22:05.880 - INFO - [SimulationController.java:122] : 仿真任务删除成功，ID: 1
2025-07-01 11:22:05.886 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:05.886 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:05.886 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:05.886 - INFO - [SimulationController.java:244] : 接收到获取仿真任务指标数据请求: simulationId=1, metricName=voltage
2025-07-01 11:22:05.886 - INFO - [SimulationController.java:249] : 获取仿真任务指标数据成功，任务ID: 1, 指标名称: voltage, 数据点数量: 3
2025-07-01 11:22:05.895 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:05.895 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:05.896 - INFO - [FrameworkServlet.java:547] : Completed initialization in 1 ms
2025-07-01 11:22:05.899 - INFO - [SimulationController.java:156] : 接收到上传文件请求，文件名：test.dss, 文件类型：dss, 文件大小：17
2025-07-01 11:22:05.899 - INFO - [SimulationController.java:162] : 文件上传成功，保存路径：/path/to/uploaded/file
2025-07-01 11:22:05.905 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:05.905 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:05.905 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:05.909 - INFO - [SimulationController.java:276] : 接收到导出仿真任务指标数据请求: simulationId=1, queryDTO=BatchMetricExportDTO(pic=chart, metricIds=[voltage, current], startTimestamp=1000)
2025-07-01 11:22:05.914 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:05.914 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:05.914 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:05.915 - INFO - [SimulationController.java:206] : 接收到导入仿真任务请求，文件名：simulation.json, 文件大小：36
2025-07-01 11:22:05.915 - INFO - [SimulationController.java:210] : 仿真任务导入成功，仿真任务ID：1
2025-07-01 11:22:05.921 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:05.921 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:05.921 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:05.924 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=1, queryDTO=BatchMetricQueryDTO(metricIds=[voltage, current], startTimestamp=1000)
2025-07-01 11:22:05.924 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 1, 指标数量: 2
2025-07-01 11:22:05.931 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:05.931 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:05.931 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:05.932 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=4
2025-07-01 11:22:05.933 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 4, 总数据点: 1000, 合格数据点: 985, 合格率: 98.50%
2025-07-01 11:22:05.944 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:05.944 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:05.944 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:05.945 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=1
2025-07-01 11:22:05.945 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 1, 总数据点: 10000, 合格数据点: 8550, 合格率: 85.50%
2025-07-01 11:22:05.951 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:05.951 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:05.951 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:05.952 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=3
2025-07-01 11:22:05.953 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 3, 总数据点: 100, 合格数据点: 75, 合格率: 75.00%
2025-07-01 11:22:05.960 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:05.960 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:05.960 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:05.961 - INFO - [SimulationController.java:130] : 接收到获取仿真任务详情请求: simulationId=1
2025-07-01 11:22:05.961 - INFO - [SimulationController.java:134] : 获取仿真任务详情成功，ID: 1
2025-07-01 11:22:05.967 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:05.967 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:05.967 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:05.968 - INFO - [SimulationController.java:82] : 接收到重命名仿真任务请求: simulationId=1, newName=新仿真名称
2025-07-01 11:22:05.968 - INFO - [SimulationController.java:86] : 仿真任务重命名成功，ID: 1, 新名称: 新仿真名称
2025-07-01 11:22:05.974 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:05.974 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:05.974 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:05.975 - INFO - [SimulationController.java:60] : 接收到创建仿真任务请求: projectId=100, simulationName=  测试仿真  
2025-07-01 11:22:05.975 - INFO - [SimulationController.java:73] : 仿真任务创建成功，ID: 1
2025-07-01 11:22:05.982 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:05.982 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:05.982 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:05.983 - INFO - [SimulationController.java:195] : 接收到导出仿真任务请求: simulationId=1
2025-07-01 11:22:05.988 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:05.988 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:05.988 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:05.989 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=10, sortBy=created_at, sortOrder=desc, projectId=1, keyword=null, startTime=null, endTime=null)
2025-07-01 11:22:05.990 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 0
2025-07-01 11:22:05.996 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:05.996 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:05.996 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:05.998 - INFO - [SimulationController.java:170] : 接收到运行仿真任务请求: simulationId=1
2025-07-01 11:22:05.998 - INFO - [SimulationController.java:176] : 仿真任务运行成功，任务ID: 1
2025-07-01 11:22:06.003 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:06.003 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:06.003 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:06.004 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=1
2025-07-01 11:22:06.004 - INFO - [SimulationController.java:294] : 获取台区电压统计报表成功，台区ID: 1, 用户总数: 100, 合格率: 95.5
2025-07-01 11:22:06.010 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:06.010 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:06.010 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:06.012 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=0
2025-07-01 11:22:06.012 -ERROR - [SimulationController.java:340] : 计算仿真电压合格率失败: 仿真ID不能为空或无效
2025-07-01 11:22:06.018 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:06.018 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:06.019 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:06.022 - INFO - [SimulationController.java:102] : 接收到更新仿真任务请求: simulationId=1, simulationModel=新模型, simulationDraft=新草稿, simulationScript=新脚本, nodes=新节点, controlStrategy=新控制策略
2025-07-01 11:22:06.022 - INFO - [SimulationController.java:108] : 仿真任务更新成功，ID: 1, 更新后模型: 新模型, 更新后脚本: 新脚本
2025-07-01 11:22:06.027 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:06.027 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:06.028 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:06.028 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=2
2025-07-01 11:22:06.029 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 2, 总数据点: 0, 合格数据点: 0, 合格率: 0.00%
2025-07-01 11:22:06.034 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:06.034 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:06.034 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:06.035 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=999
2025-07-01 11:22:06.035 -ERROR - [SimulationController.java:340] : 计算仿真电压合格率失败: 仿真任务不存在
2025-07-01 11:22:06.040 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:06.040 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:06.040 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:06.040 - INFO - [SimulationController.java:142] : 接收到复制仿真任务请求: simulationId=1
2025-07-01 11:22:06.041 - INFO - [SimulationController.java:146] : 仿真任务复制成功，新ID: 2
2025-07-01 11:22:06.045 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:06.046 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:06.046 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:06.047 - INFO - [SimulationController.java:218] : 接收到获取仿真任务输出路径请求: simulationId=1
2025-07-01 11:22:06.047 - INFO - [SimulationController.java:223] : 获取仿真任务输出路径成功，任务ID: 1, 输出路径: /output/path/simulation_1
2025-07-01 11:22:06.059 -ERROR - [DeviceServiceImpl.java:141] : 设备更新失败：参数代码已被其他设备使用: CONFLICT_PARAM
2025-07-01 11:22:06.060 -ERROR - [DeviceServiceImpl.java:168] : 设备删除失败：指定ID的设备不存在: 999
2025-07-01 11:22:06.061 - INFO - [DeviceServiceImpl.java:152] : 设备更新成功: Device(parameterId=1, deviceType=TRANSFORMER, parameterCode=UPDATED_PARAM, version=2.0)
2025-07-01 11:22:06.061 -ERROR - [DeviceServiceImpl.java:105] : 查询设备失败：ID不能为空
2025-07-01 11:22:06.062 - INFO - [DeviceServiceImpl.java:60] : 开始查询设备列表, page: 1, size: 10, sortBy: invalidField, sortOrder: desc, keyword: null
2025-07-01 11:22:06.062 - WARN - [DeviceServiceImpl.java:74] : 无效的排序字段: invalidField，使用默认排序字段 parameterId
2025-07-01 11:22:06.062 - INFO - [DeviceServiceImpl.java:90] : 查询设备列表成功，查询到 1 条数据
2025-07-01 11:22:06.062 - INFO - [DeviceServiceImpl.java:53] : 设备创建成功: Device(parameterId=null, deviceType=LINE, parameterCode=NEW_PARAM_001, version=1.0)
2025-07-01 11:22:06.062 - INFO - [DeviceServiceImpl.java:60] : 开始查询设备列表, page: 1, size: 10, sortBy: parameterId, sortOrder: desc, keyword: null
2025-07-01 11:22:06.063 - INFO - [DeviceServiceImpl.java:90] : 查询设备列表成功，查询到 1 条数据
2025-07-01 11:22:06.063 - INFO - [DeviceServiceImpl.java:178] : 设备删除成功: 1
2025-07-01 11:22:06.064 -ERROR - [DeviceServiceImpl.java:133] : 设备更新失败：指定ID的设备不存在: 999
2025-07-01 11:22:06.064 -ERROR - [DeviceServiceImpl.java:161] : 设备删除失败：ID不能为空
2025-07-01 11:22:06.065 -ERROR - [DeviceServiceImpl.java:42] : 设备创建失败：参数代码已存在: EXISTING_PARAM
2025-07-01 11:22:06.065 -ERROR - [DeviceServiceImpl.java:111] : 查询设备失败：指定ID的设备不存在: 999
2025-07-01 11:22:06.066 - INFO - [DeviceServiceImpl.java:96] : 开始查询设备总数, keyword: null
2025-07-01 11:22:06.066 - INFO - [DeviceServiceImpl.java:98] : 查询设备总数完成，共 5 条记录
2025-07-01 11:22:06.066 - INFO - [DeviceServiceImpl.java:115] : 查询设备成功: Device(parameterId=1, deviceType=LINE, parameterCode=TEST_PARAM_001, version=1.0)
2025-07-01 11:22:06.079 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:06.080 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:06.080 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:06.082 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 2
2025-07-01 11:22:06.082 - INFO - [DeviceController.java:60] : 设备更新成功: Device(parameterId=1, deviceType=LINE, parameterCode=UPDATED_PARAM001, version=1.1)
2025-07-01 11:22:06.082 - INFO - [DeviceController.java:60] : 设备更新成功: Device(parameterId=2, deviceType=TRANSFORMER, parameterCode=UPDATED_PARAM002, version=2.1)
2025-07-01 11:22:06.087 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:06.087 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:06.087 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:06.088 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 2
2025-07-01 11:22:06.088 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 1
2025-07-01 11:22:06.088 -ERROR - [DeviceController.java:69] : 设备操作失败: 设备类型不能为空.
2025-07-01 11:22:06.093 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:06.093 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:06.093 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:06.094 - INFO - [DeviceController.java:81] : 接收到分页查询设备请求: DeviceQueryDTO [page=1, size=10, sortBy=parameterId, sortOrder=desc, keyword=test]
2025-07-01 11:22:06.095 - INFO - [DeviceController.java:93] : 设备查询成功，总数量: 2
2025-07-01 11:22:06.100 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:06.100 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:06.100 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:06.100 - INFO - [DeviceController.java:111] : 接收到删除设备请求: parameterId=1
2025-07-01 11:22:06.100 - INFO - [DeviceController.java:115] : 设备删除结果: true
2025-07-01 11:22:06.104 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:06.104 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:06.105 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:06.105 - INFO - [DeviceController.java:111] : 接收到删除设备请求: parameterId=1
2025-07-01 11:22:06.105 - INFO - [DeviceController.java:115] : 设备删除结果: false
2025-07-01 11:22:06.109 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:06.109 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:06.109 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:06.111 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 1
2025-07-01 11:22:06.111 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 1
2025-07-01 11:22:06.116 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:06.116 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:06.116 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:06.118 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 1
2025-07-01 11:22:06.118 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 1
2025-07-01 11:22:06.122 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:06.122 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:06.122 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:06.123 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 2
2025-07-01 11:22:06.124 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 1
2025-07-01 11:22:06.124 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 2
2025-07-01 11:22:06.129 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:06.129 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:06.129 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:06.129 - INFO - [DeviceController.java:100] : 接收到查询设备请求: parameterId=1
2025-07-01 11:22:06.129 - INFO - [DeviceController.java:104] : 设备查询成功: Device(parameterId=1, deviceType=LINE, parameterCode=PARAM001, version=1.0)
2025-07-01 11:22:14.968 - INFO - [DatabaseUtils.java:81] : 创建数据库目录: D:\CETWorkSpace\ngap-server\.\test_data - 成功: true
2025-07-01 11:22:15.165 - INFO - [DatabaseUtils.java:97] : 开始执行SQL脚本: db/non_existent.sql
2025-07-01 11:22:15.170 - INFO - [DatabaseUtils.java:97] : 开始执行SQL脚本: db/init.sql
2025-07-01 11:22:16.726 - INFO - [DatabaseUtils.java:132] : SQL脚本执行完成: db/init.sql
2025-07-01 11:22:16.735 - WARN - [DatabaseUtils.java:146] : 数据库连接测试失败: jdbc:invalid:url - No suitable driver found for jdbc:invalid:url
2025-07-01 11:22:16.819 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-07-01 11:22:16.993 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-07-01 11:22:18.171 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-07-01 11:22:18.171 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-07-01 11:22:18.171 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-01 11:22:18.171 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-07-01 11:22:18.171 - INFO - [VoltageQualityReportPerformanceTest.java:96] : === 中等文件性能测试 ===
2025-07-01 11:22:18.338 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit5819149282912495537\medium_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit5819149282912495537\电压合格率报表.xlsx, 处理记录数: 100, 耗时: 167ms
2025-07-01 11:22:18.338 - INFO - [VoltageQualityReportPerformanceTest.java:107] : 中等文件处理时间: 167ms
2025-07-01 11:22:18.338 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 1
  总记录数: 100
  总处理时间: 167ms
  平均处理时间: 167ms
  处理速度: 598.80 记录/秒
  文件处理速度: 5.99 文件/秒
  操作统计:
    calculateOverallStats: OperationStats{count=1, total=3ms, avg=3ms, min=3ms, max=3ms}
    generateReport: OperationStats{count=1, total=48ms, avg=48ms, min=48ms, max=48ms}
    generateVoltageReport: OperationStats{count=1, total=167ms, avg=167ms, min=167ms, max=167ms}
    readVoltageData: OperationStats{count=1, total=112ms, avg=112ms, min=112ms, max=112ms}
    calculateUserStats: OperationStats{count=1, total=2ms, avg=2ms, min=2ms, max=2ms}
    groupByUser: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
}
2025-07-01 11:22:18.338 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-07-01 11:22:18.338 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-01 11:22:18.361 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-07-01 11:22:18.487 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-07-01 11:22:19.612 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1248KB
2025-07-01 11:22:19.613 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-07-01 11:22:19.613 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-01 11:22:19.613 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-07-01 11:22:19.613 - INFO - [VoltageQualityReportPerformanceTest.java:66] : === 小文件性能测试 ===
2025-07-01 11:22:19.636 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit1827972916387374363\small_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit1827972916387374363\电压合格率报表.xlsx, 处理记录数: 10, 耗时: 23ms
2025-07-01 11:22:19.659 - INFO - [VoltageQualityReportPerformanceTest.java:82] : 小文件处理时间 - 第一次: 23ms, 第二次: 23ms, 提升: 0.0%
2025-07-01 11:22:19.659 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 1
  总记录数: 10
  总处理时间: 23ms
  平均处理时间: 23ms
  处理速度: 434.78 记录/秒
  文件处理速度: 43.48 文件/秒
  操作统计:
    calculateOverallStats: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
    generateReport: OperationStats{count=1, total=8ms, avg=8ms, min=8ms, max=8ms}
    generateVoltageReport: OperationStats{count=2, total=46ms, avg=23ms, min=23ms, max=23ms}
    readVoltageData: OperationStats{count=1, total=13ms, avg=13ms, min=13ms, max=13ms}
    calculateUserStats: OperationStats{count=1, total=2ms, avg=2ms, min=2ms, max=2ms}
    groupByUser: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
}
2025-07-01 11:22:19.659 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-07-01 11:22:19.659 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-01 11:22:19.680 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-07-01 11:22:19.798 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-07-01 11:22:20.936 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-07-01 11:22:20.936 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-07-01 11:22:20.936 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-01 11:22:20.936 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-07-01 11:22:20.936 - INFO - [VoltageQualityReportPerformanceTest.java:141] : === 缓存效率测试 ===
2025-07-01 11:22:20.990 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit4848445327682267767\medium_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit4848445327682267767\电压合格率报表.xlsx, 处理记录数: 100, 耗时: 54ms
2025-07-01 11:22:20.991 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-01 11:22:21.036 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第1次处理时间: 45ms
2025-07-01 11:22:21.064 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第2次处理时间: 28ms
2025-07-01 11:22:21.090 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第3次处理时间: 26ms
2025-07-01 11:22:21.114 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第4次处理时间: 24ms
2025-07-01 11:22:21.136 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第5次处理时间: 22ms
2025-07-01 11:22:21.136 - INFO - [VoltageQualityReportPerformanceTest.java:162] : 缓存统计: CacheStats{size=5, hits=12, misses=15, hitRate=44.44%}
2025-07-01 11:22:21.138 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 0
  总记录数: 0
  总处理时间: 0ms
  平均处理时间: 0ms
  处理速度: 0.00 记录/秒
  文件处理速度: 0.00 文件/秒
  操作统计:
    generateVoltageReport: OperationStats{count=5, total=145ms, avg=29ms, min=22ms, max=45ms}
}
2025-07-01 11:22:21.138 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-07-01 11:22:21.138 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-01 11:22:21.158 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-07-01 11:22:21.273 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-07-01 11:22:22.379 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-07-01 11:22:22.379 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-07-01 11:22:22.379 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-01 11:22:22.380 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-07-01 11:22:22.380 - INFO - [VoltageQualityReportPerformanceTest.java:115] : === 大文件性能测试 ===
2025-07-01 11:22:22.753 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit2189018184128167423\large_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit2189018184128167423\电压合格率报表.xlsx, 处理记录数: 1000, 耗时: 373ms
2025-07-01 11:22:22.754 - INFO - [VoltageQualityReportPerformanceTest.java:126] : 大文件处理时间: 373ms
2025-07-01 11:22:22.754 - INFO - [VoltageQualityReportPerformanceTest.java:136] : 处理速度: 2680.97 记录/秒
2025-07-01 11:22:22.754 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 1
  总记录数: 1000
  总处理时间: 373ms
  平均处理时间: 373ms
  处理速度: 2680.97 记录/秒
  文件处理速度: 2.68 文件/秒
  操作统计:
    calculateOverallStats: OperationStats{count=1, total=6ms, avg=6ms, min=6ms, max=6ms}
    generateReport: OperationStats{count=1, total=86ms, avg=86ms, min=86ms, max=86ms}
    generateVoltageReport: OperationStats{count=1, total=373ms, avg=373ms, min=373ms, max=373ms}
    readVoltageData: OperationStats{count=1, total=267ms, avg=267ms, min=267ms, max=267ms}
    calculateUserStats: OperationStats{count=1, total=13ms, avg=13ms, min=13ms, max=13ms}
    groupByUser: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
}
2025-07-01 11:22:22.754 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-07-01 11:22:22.754 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-01 11:22:22.772 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-07-01 11:22:22.888 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-07-01 11:22:24.030 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-07-01 11:22:24.031 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-07-01 11:22:24.031 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-01 11:22:24.031 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-07-01 11:22:24.031 - INFO - [VoltageQualityReportPerformanceTest.java:175] : === 内存效率测试 ===
2025-07-01 11:22:24.749 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit8475323002061610529\large_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit8475323002061610529\电压合格率报表.xlsx, 处理记录数: 1000, 耗时: 356ms
2025-07-01 11:22:24.830 - INFO - [VoltageQualityReportPerformanceTest.java:190] : 内存使用: 初始=113MB, 最终=113MB, 增加=0MB
2025-07-01 11:22:24.830 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 1
  总记录数: 1000
  总处理时间: 356ms
  平均处理时间: 356ms
  处理速度: 2808.99 记录/秒
  文件处理速度: 2.81 文件/秒
  操作统计:
    calculateOverallStats: OperationStats{count=1, total=2ms, avg=2ms, min=2ms, max=2ms}
    generateReport: OperationStats{count=1, total=88ms, avg=88ms, min=88ms, max=88ms}
    generateVoltageReport: OperationStats{count=1, total=356ms, avg=356ms, min=356ms, max=356ms}
    readVoltageData: OperationStats{count=1, total=258ms, avg=258ms, min=258ms, max=258ms}
    calculateUserStats: OperationStats{count=1, total=7ms, avg=7ms, min=7ms, max=7ms}
    groupByUser: OperationStats{count=1, total=1ms, avg=1ms, min=1ms, max=1ms}
}
2025-07-01 11:22:24.831 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-07-01 11:22:24.831 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-07-01 11:22:25.964 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-07-01 11:22:25.970 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-07-01 11:22:25.980 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-07-01 11:22:25.981 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-07-01 11:22:25.991 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-07-01 11:22:26.002 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit5433800533579235224\test_voltage_data.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit5433800533579235224\电压合格率报表.xlsx, 处理记录数: 3, 耗时: 10ms
2025-07-01 11:22:26.002 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-07-01 11:22:26.014 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-07-01 11:22:26.020 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-07-01 11:22:26.030 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-07-01 11:22:26.030 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-07-01 11:22:27.628 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-01 11:22:27.628 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-01 11:22:27.628 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *******************************
2025-07-01 11:22:27.629 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-01 11:22:27.630 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-01 11:22:27.631 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-01 11:22:27.632 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-01 11:22:27.633 -ERROR - [DatabaseInitializer.java:54] : 数据库初始化失败
java.lang.ClassNotFoundException: invalid.driver.Class
	at java.net.URLClassLoader.findClass(URLClassLoader.java:381)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:335)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:264)
	at com.cet.electric.ngapserver.config.DatabaseInitializer.run(DatabaseInitializer.java:40)
	at com.cet.electric.ngapserver.config.DatabaseInitializerTest.testRun_WhenDriverLoadFails_ShouldThrowException(DatabaseInitializerTest.java:137)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.internal.runners.statements.ExpectException.evaluate(ExpectException.java:19)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.mockito.internal.runners.DefaultInternalRunner$1.run(DefaultInternalRunner.java:78)
	at org.mockito.internal.runners.DefaultInternalRunner.run(DefaultInternalRunner.java:84)
	at org.mockito.internal.runners.StrictRunner.run(StrictRunner.java:39)
	at org.mockito.junit.MockitoJUnitRunner.run(MockitoJUnitRunner.java:161)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
	at org.junit.vintage.engine.execution.RunnerExecutor.execute(RunnerExecutor.java:43)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.Iterator.forEachRemaining(Iterator.java:116)
	at java.util.Spliterators$IteratorSpliterator.forEachRemaining(Spliterators.java:1801)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:481)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:471)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.junit.vintage.engine.VintageTestEngine.executeAllChildren(VintageTestEngine.java:82)
	at org.junit.vintage.engine.VintageTestEngine.execute(VintageTestEngine.java:73)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:248)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$5(DefaultLauncher.java:211)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:226)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:199)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:132)
	at com.intellij.junit5.JUnit5IdeaTestRunner.startRunnerWithArgs(JUnit5IdeaTestRunner.java:57)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
2025-07-01 11:22:27.633 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-01 11:22:27.633 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-01 11:22:27.634 - INFO - [DatabaseInitializer.java:45] : 数据库文件不存在，开始创建数据库: *******************************
2025-07-01 11:22:27.634 - INFO - [DatabaseInitializer.java:66] : 开始初始化数据库...
2025-07-01 11:22:27.634 - INFO - [DatabaseUtils.java:81] : 创建数据库目录: D:\CETWorkSpace\ngap-server\.\test_data - 成功: true
2025-07-01 11:22:27.634 - INFO - [DatabaseInitializer.java:75] : 数据库连接创建成功，开始执行建表脚本...
2025-07-01 11:22:27.634 - INFO - [DatabaseUtils.java:97] : 开始执行SQL脚本: db/init.sql
2025-07-01 11:22:28.640 - INFO - [DatabaseUtils.java:132] : SQL脚本执行完成: db/init.sql
2025-07-01 11:22:28.640 - INFO - [DatabaseInitializer.java:80] : 数据库初始化完成
2025-07-01 11:22:28.640 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-01 11:22:28.789 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-01 11:22:28.789 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-01 11:22:28.789 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *******************************
2025-07-01 11:22:28.789 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-01 11:22:28.790 - WARN - [DatabaseInitializer.java:103] : 缺少必要的表: simulation
2025-07-01 11:22:28.791 - WARN - [DatabaseInitializer.java:103] : 缺少必要的表: device
2025-07-01 11:22:28.791 - WARN - [DatabaseInitializer.java:109] : 数据库表结构不完整，重新执行初始化脚本...
2025-07-01 11:22:28.791 - INFO - [DatabaseUtils.java:97] : 开始执行SQL脚本: db/init.sql
2025-07-01 11:22:29.865 - INFO - [DatabaseUtils.java:132] : SQL脚本执行完成: db/init.sql
2025-07-01 11:22:29.865 - INFO - [DatabaseInitializer.java:111] : 数据库表结构修复完成
2025-07-01 11:22:29.867 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-01 11:22:29.881 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:29.881 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:29.881 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:29.882 - INFO - [ProjectController.java:38] : 接收到创建项目请求: projectName=测试项目, projectType=
2025-07-01 11:22:29.882 - INFO - [ProjectController.java:51] : 项目创建成功，ID: 1
2025-07-01 11:22:29.887 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:29.887 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:29.887 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:29.887 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-07-01 11:22:29.889 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-07-01 11:22:29.894 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:29.894 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:29.894 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:29.896 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=10, sortBy=created_at, sortOrder=desc, projectType=null, keyword=null, startTime=null, endTime=null)
2025-07-01 11:22:29.896 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 0
2025-07-01 11:22:29.901 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:29.901 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:29.901 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:29.902 - INFO - [ProjectController.java:134] : 接收到导出项目配置文件请求: projectId=1
2025-07-01 11:22:29.906 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:29.906 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:29.906 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:29.907 - INFO - [ProjectController.java:38] : 接收到创建项目请求: projectName=  测试项目  , projectType=  General_scenario  
2025-07-01 11:22:29.907 - INFO - [ProjectController.java:51] : 项目创建成功，ID: 1
2025-07-01 11:22:29.913 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:29.913 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:29.913 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:29.914 - INFO - [ProjectController.java:142] : 接收到导入项目配置文件请求，文件名：project.json, 文件大小：66
2025-07-01 11:22:29.914 - INFO - [ProjectController.java:146] : 项目配置文件导入成功，项目ID：1
2025-07-01 11:22:29.918 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:29.918 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:29.919 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:29.919 - INFO - [ProjectController.java:119] : 接收到删除项目请求: projectId=1
2025-07-01 11:22:29.919 - INFO - [ProjectController.java:124] : 项目删除成功，项目ID: 1
2025-07-01 11:22:29.926 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:29.926 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:29.926 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:29.927 - INFO - [ProjectController.java:38] : 接收到创建项目请求: projectName=测试项目, projectType=General_scenario
2025-07-01 11:22:29.927 - INFO - [ProjectController.java:51] : 项目创建成功，ID: 1
2025-07-01 11:22:29.931 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:29.931 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:29.931 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:29.932 - INFO - [ProjectController.java:106] : 接收到重命名项目请求: projectId=1, newName=新项目名称
2025-07-01 11:22:29.932 - INFO - [ProjectController.java:111] : 项目重命名成功，项目ID: 1, 新名称: 新项目名称
2025-07-01 11:22:29.936 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-07-01 11:22:29.936 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-07-01 11:22:29.936 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-07-01 11:22:29.937 - INFO - [ProjectController.java:91] : 接收到复制项目请求: projectId=1
2025-07-01 11:22:29.937 - INFO - [ProjectController.java:96] : 项目复制成功，原项目ID: 1, 新项目ID: 2
2025-07-01 11:22:30.521 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-01 11:22:30.521 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-01 11:22:30.522 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:8080, DSS文件路径: /test/path/test.dss
2025-07-01 11:22:30.522 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 200 OK
2025-07-01 11:22:30.523 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-01 11:22:30.523 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:8080, DSS文件路径: /test/path/test.dss
2025-07-01 11:22:30.523 - WARN - [DssUtils.java:74] : DSS服务返回空的错误响应，已创建默认错误信息
2025-07-01 11:22:30.523 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 500 INTERNAL_SERVER_ERROR
2025-07-01 11:22:30.523 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-01 11:22:30.523 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:8080, DSS文件路径: /test/path/test.dss
2025-07-01 11:22:30.523 - INFO - [DssUtils.java:88] : 为DSS错误响应添加output_data字段
2025-07-01 11:22:30.523 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 500 INTERNAL_SERVER_ERROR
2025-07-01 11:22:30.524 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-01 11:22:30.524 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:8080, DSS文件路径: /test/path/test.dss
2025-07-01 11:22:30.524 - INFO - [DssUtils.java:88] : 为DSS错误响应添加output_data字段
2025-07-01 11:22:30.524 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 400 BAD_REQUEST
2025-07-01 11:22:32.724 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-07-01 11:22:32.725 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-01 11:22:32.728 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-07-01 11:22:32.730 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-07-01 11:23:06.484 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 23044 (D:\CETWorkSpace\ngap-server\target\classes started by dell in D:\CETWorkSpace\ngap-server)
2025-07-01 11:23:06.485 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-07-01 11:23:06.505 - INFO - [DeferredLog.java:225] : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-01 11:23:06.505 - INFO - [DeferredLog.java:225] : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-01 11:23:07.296 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-07-01 11:23:07.306 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-07-01 11:23:07.307 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-07-01 11:23:07.307 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-01 11:23:07.382 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-07-01 11:23:07.382 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 877 ms
2025-07-01 11:23:07.607 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-01 11:23:07.769 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-01 11:23:07.799 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-01 11:23:07.839 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-07-01 11:23:07.856 - INFO - [OptionalLiveReloadServer.java:58] : LiveReload server is running on port 35729
2025-07-01 11:23:07.929 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-07-01 11:23:07.952 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-07-01 11:23:07.962 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-07-01 11:23:07.963 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-07-01 11:23:07.970 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-07-01 11:23:07.987 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-07-01 11:23:08.091 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-07-01 11:23:08.107 - INFO - [TaskSchedule.java:38] : 开始删除
2025-07-01 11:23:08.108 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-07-01 11:23:08.110 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 1.859 seconds (JVM running for 2.181)
2025-07-01 11:23:08.112 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-01 11:23:08.113 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-01 11:23:08.113 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-07-01 11:23:08.113 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-01 11:23:08.115 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-07-01 11:23:08.173 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-01 11:23:08.173 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-01 11:23:08.175 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-07-01 11:23:08.185 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-07-01 11:23:08.185 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-07-01 11:23:08.186 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-07-01 11:23:08.186 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-07-01 11:23:08.512 - INFO - [DirectJDKLog.java:173] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 11:23:08.512 - INFO - [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-07-01 11:23:08.515 - INFO - [FrameworkServlet.java:547] : Completed initialization in 3 ms
2025-07-01 11:23:59.416 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-07-01 11:23:59.417 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-01 11:23:59.419 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-07-01 11:23:59.420 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-07-01 11:24:08.351 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 23044 (D:\CETWorkSpace\ngap-server\target\classes started by dell in D:\CETWorkSpace\ngap-server)
2025-07-01 11:24:08.352 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-07-01 11:24:08.434 - WARN - [Logger.java:44] : No MyBatis mapper was found in '[com.cet.electric.ngapserver]' package. Please check your configuration.
2025-07-01 11:24:08.454 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-07-01 11:24:08.455 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-07-01 11:24:08.455 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-01 11:24:08.465 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-07-01 11:24:08.465 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 112 ms
2025-07-01 11:24:08.501 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-07-01 11:24:08.526 - WARN - [AbstractApplicationContext.java:559] : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [org/mybatis/spring/boot/autoconfigure/MybatisAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [D:\CETWorkSpace\ngap-server\target\classes\mapper\DeviceMapper.xml]'; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\CETWorkSpace\ngap-server\target\classes\mapper\DeviceMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.cet.electric.ngapserver.entity.Device'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.cet.electric.ngapserver.entity.Device
2025-07-01 11:24:08.526 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-01 11:24:08.527 - INFO - [DirectJDKLog.java:173] : Stopping service [Tomcat]
2025-07-01 11:24:08.531 - INFO - [ConditionEvaluationReportLoggingListener.java:136] : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-01 11:24:08.533 -ERROR - [SpringApplication.java:837] : Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [org/mybatis/spring/boot/autoconfigure/MybatisAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [D:\CETWorkSpace\ngap-server\target\classes\mapper\DeviceMapper.xml]'; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\CETWorkSpace\ngap-server\target\classes\mapper\DeviceMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.cet.electric.ngapserver.entity.Device'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.cet.electric.ngapserver.entity.Device
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:637)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1336)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1176)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:405)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1237)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at com.cet.electric.ngapserver.NgapServerApplication.main(NgapServerApplication.java:19)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [D:\CETWorkSpace\ngap-server\target\classes\mapper\DeviceMapper.xml]'; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\CETWorkSpace\ngap-server\target\classes\mapper\DeviceMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.cet.electric.ngapserver.entity.Device'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.cet.electric.ngapserver.entity.Device
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:652)
	... 25 common frames omitted
Caused by: org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [D:\CETWorkSpace\ngap-server\target\classes\mapper\DeviceMapper.xml]'; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\CETWorkSpace\ngap-server\target\classes\mapper\DeviceMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.cet.electric.ngapserver.entity.Device'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.cet.electric.ngapserver.entity.Device
	at org.mybatis.spring.SqlSessionFactoryBean.buildSqlSessionFactory(SqlSessionFactoryBean.java:613)
	at org.mybatis.spring.SqlSessionFactoryBean.afterPropertiesSet(SqlSessionFactoryBean.java:491)
	at org.mybatis.spring.SqlSessionFactoryBean.getObject(SqlSessionFactoryBean.java:633)
	at org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration.sqlSessionFactory(MybatisAutoConfiguration.java:179)
	at org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$b8f500bf.CGLIB$sqlSessionFactory$1(<generated>)
	at org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$b8f500bf$$FastClassBySpringCGLIB$$d68b1917.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$b8f500bf.sqlSessionFactory(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 26 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\CETWorkSpace\ngap-server\target\classes\mapper\DeviceMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.cet.electric.ngapserver.entity.Device'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.cet.electric.ngapserver.entity.Device
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:123)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.parse(XMLMapperBuilder.java:95)
	at org.mybatis.spring.SqlSessionFactoryBean.buildSqlSessionFactory(SqlSessionFactoryBean.java:611)
	... 39 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.cet.electric.ngapserver.entity.Device'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.cet.electric.ngapserver.entity.Device
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:118)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:263)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:254)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElements(XMLMapperBuilder.java:246)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:119)
	... 41 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.cet.electric.ngapserver.entity.Device'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.cet.electric.ngapserver.entity.Device
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:120)
	at org.apache.ibatis.builder.BaseBuilder.resolveAlias(BaseBuilder.java:149)
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:116)
	... 45 common frames omitted
Caused by: java.lang.ClassNotFoundException: Cannot find class: com.cet.electric.ngapserver.entity.Device
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:196)
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:89)
	at org.apache.ibatis.io.Resources.classForName(Resources.java:261)
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:116)
	... 47 common frames omitted
2025-07-01 11:24:13.039 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 23044 (D:\CETWorkSpace\ngap-server\target\classes started by dell in D:\CETWorkSpace\ngap-server)
2025-07-01 11:24:13.039 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-07-01 11:24:13.256 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-07-01 11:24:13.256 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-07-01 11:24:13.256 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-01 11:24:13.264 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-07-01 11:24:13.264 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 224 ms
2025-07-01 11:24:13.359 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-07-01 11:24:13.424 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-01 11:24:13.436 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-01 11:24:13.451 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-07-01 11:24:13.454 - INFO - [OptionalLiveReloadServer.java:58] : LiveReload server is running on port 35729
2025-07-01 11:24:13.482 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-07-01 11:24:13.496 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-07-01 11:24:13.496 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-07-01 11:24:13.497 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-07-01 11:24:13.500 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-07-01 11:24:13.555 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-07-01 11:24:13.567 - INFO - [TaskSchedule.java:38] : 开始删除
2025-07-01 11:24:13.568 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 0.55 seconds (JVM running for 67.638)
2025-07-01 11:24:13.568 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-07-01 11:24:13.569 - INFO - [HikariDataSource.java:110] : HikariPool-2 - Starting...
2025-07-01 11:24:13.569 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-07-01 11:24:13.569 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-07-01 11:24:13.570 - INFO - [HikariDataSource.java:123] : HikariPool-2 - Start completed.
2025-07-01 11:24:13.570 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-07-01 11:24:13.570 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-07-01 11:24:13.570 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-07-01 11:24:13.570 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-07-01 11:24:13.571 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-07-01 11:24:13.571 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-07-01 11:24:13.571 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-07-01 11:24:13.571 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-07-01 11:24:13.572 - INFO - [ConditionEvaluationDeltaLoggingListener.java:63] : Condition evaluation unchanged
2025-07-01 11:25:00.266 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-07-01 11:25:00.267 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-01 11:25:00.268 - INFO - [HikariDataSource.java:350] : HikariPool-2 - Shutdown initiated...
2025-07-01 11:25:00.270 - INFO - [HikariDataSource.java:352] : HikariPool-2 - Shutdown completed.
