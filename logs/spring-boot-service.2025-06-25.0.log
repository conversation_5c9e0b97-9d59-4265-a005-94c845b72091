2025-06-25 08:51:12.881 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 08:51:12.983 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 08:51:12.985 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 08:51:12.985 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 08:51:12.985 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 08:51:12.986 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 08:52:18.579 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 08:52:18.580 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 08:52:18.582 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 08:52:18.583 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 08:52:18.584 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 08:52:18.584 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 08:53:01.722 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 08:53:01.722 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 08:53:01.723 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 08:53:01.723 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 08:53:01.724 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 08:53:01.724 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 08:54:15.503 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 08:54:15.504 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 08:54:15.505 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 08:54:15.506 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 08:54:15.507 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 08:54:15.507 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 08:54:57.764 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 08:54:57.765 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 08:54:57.765 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 08:54:57.766 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 08:54:57.766 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 08:54:57.766 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 08:55:20.403 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 08:55:20.403 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 08:55:20.404 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 08:55:20.404 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 08:55:20.404 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 08:55:20.404 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 08:57:15.333 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 08:57:15.333 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 08:57:15.334 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 08:57:15.334 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 08:57:15.334 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 08:57:15.334 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 08:59:59.957 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 08:59:59.958 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 08:59:59.958 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 08:59:59.959 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 08:59:59.960 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 08:59:59.960 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 09:02:39.877 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 09:02:39.877 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 09:02:39.878 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 09:02:39.878 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 09:02:39.878 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 09:02:39.879 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 09:04:04.323 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 09:04:04.324 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 09:04:04.324 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 09:04:04.324 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 09:04:04.325 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 09:04:04.325 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 09:04:50.611 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 09:04:50.611 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 09:04:50.611 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 09:04:50.612 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 09:04:50.612 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 09:04:50.612 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 09:19:50.707 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplicationTests on DESKTOP-9LFKQ75 with PID 31912 (started by dell in D:\CETWorkSpace\ngap-server)
2025-06-25 09:19:50.708 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-06-25 09:19:52.174 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-25 09:19:52.695 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-06-25 09:19:52.814 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-06-25 09:19:53.035 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-06-25 09:19:53.229 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-06-25 09:19:53.261 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-06-25 09:19:53.284 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-06-25 09:19:53.308 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-06-25 09:19:53.505 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-06-25 09:19:53.525 - INFO - [TaskSchedule.java:38] : 开始删除
2025-06-25 09:19:53.525 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-06-25 09:19:53.528 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplicationTests in 3.332 seconds (JVM running for 9.74)
2025-06-25 09:19:53.548 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-06-25 09:19:53.552 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-25 09:19:53.552 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-25 09:19:53.552 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-06-25 09:19:53.552 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-06-25 09:19:53.702 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-06-25 09:19:53.702 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-25 09:19:53.709 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-06-25 09:19:53.744 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-06-25 09:19:53.744 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-06-25 09:19:53.781 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-06-25 09:19:53.781 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-06-25 09:19:54.965 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:54.965 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:54.966 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:54.993 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=1
2025-06-25 09:19:54.994 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 1, 指标数量: 2
2025-06-25 09:19:55.042 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.042 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.042 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.062 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=1, queryDTO=UserVoltageReportQueryDTO(keyword=用户, pageNum=1, pageSize=10)
2025-06-25 09:19:55.062 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 1, 用户数量: 2
2025-06-25 09:19:55.072 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.072 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.072 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.074 - INFO - [SimulationController.java:60] : 接收到创建仿真任务请求: projectId=100, simulationName=测试仿真
2025-06-25 09:19:55.075 - INFO - [SimulationController.java:73] : 仿真任务创建成功，ID: 1
2025-06-25 09:19:55.082 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.082 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.083 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.083 - INFO - [SimulationController.java:183] : 接收到获取仿真策略列表请求
2025-06-25 09:19:55.083 - INFO - [SimulationController.java:186] : 仿真策略列表获取成功，总数量: 2
2025-06-25 09:19:55.090 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.091 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.091 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.092 - INFO - [SimulationController.java:118] : 接收到删除仿真任务请求: simulationId=1
2025-06-25 09:19:55.092 - INFO - [SimulationController.java:122] : 仿真任务删除成功，ID: 1
2025-06-25 09:19:55.098 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.098 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.098 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.098 - INFO - [SimulationController.java:244] : 接收到获取仿真任务指标数据请求: simulationId=1, metricName=voltage
2025-06-25 09:19:55.099 - INFO - [SimulationController.java:249] : 获取仿真任务指标数据成功，任务ID: 1, 指标名称: voltage, 数据点数量: 3
2025-06-25 09:19:55.107 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.107 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.107 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.110 - INFO - [SimulationController.java:156] : 接收到上传文件请求，文件名：test.dss, 文件类型：dss, 文件大小：17
2025-06-25 09:19:55.110 - INFO - [SimulationController.java:162] : 文件上传成功，保存路径：/path/to/uploaded/file
2025-06-25 09:19:55.116 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.116 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.116 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.119 - INFO - [SimulationController.java:276] : 接收到导出仿真任务指标数据请求: simulationId=1, queryDTO=BatchMetricExportDTO(pic=chart, metricIds=[voltage, current], startTimestamp=1000)
2025-06-25 09:19:55.124 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.124 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.124 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.125 - INFO - [SimulationController.java:206] : 接收到导入仿真任务请求，文件名：simulation.json, 文件大小：36
2025-06-25 09:19:55.125 - INFO - [SimulationController.java:210] : 仿真任务导入成功，仿真任务ID：1
2025-06-25 09:19:55.131 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.131 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.131 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.133 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=1, queryDTO=BatchMetricQueryDTO(metricIds=[voltage, current], startTimestamp=1000)
2025-06-25 09:19:55.133 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 1, 指标数量: 2
2025-06-25 09:19:55.141 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.141 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.141 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.142 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=4
2025-06-25 09:19:55.143 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 4, 总数据点: 1000, 合格数据点: 985, 合格率: 98.50%
2025-06-25 09:19:55.151 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.151 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.151 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.152 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=1
2025-06-25 09:19:55.152 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 1, 总数据点: 10000, 合格数据点: 8550, 合格率: 85.50%
2025-06-25 09:19:55.159 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.159 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.159 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.160 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=3
2025-06-25 09:19:55.160 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 3, 总数据点: 100, 合格数据点: 75, 合格率: 75.00%
2025-06-25 09:19:55.167 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.167 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.167 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.168 - INFO - [SimulationController.java:130] : 接收到获取仿真任务详情请求: simulationId=1
2025-06-25 09:19:55.168 - INFO - [SimulationController.java:134] : 获取仿真任务详情成功，ID: 1
2025-06-25 09:19:55.175 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.175 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.175 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.176 - INFO - [SimulationController.java:82] : 接收到重命名仿真任务请求: simulationId=1, newName=新仿真名称
2025-06-25 09:19:55.177 - INFO - [SimulationController.java:86] : 仿真任务重命名成功，ID: 1, 新名称: 新仿真名称
2025-06-25 09:19:55.184 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.185 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.185 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.186 - INFO - [SimulationController.java:60] : 接收到创建仿真任务请求: projectId=100, simulationName=  测试仿真  
2025-06-25 09:19:55.186 - INFO - [SimulationController.java:73] : 仿真任务创建成功，ID: 1
2025-06-25 09:19:55.194 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.194 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.194 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.195 - INFO - [SimulationController.java:195] : 接收到导出仿真任务请求: simulationId=1
2025-06-25 09:19:55.199 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.199 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.200 - INFO - [FrameworkServlet.java:547] : Completed initialization in 1 ms
2025-06-25 09:19:55.202 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=10, sortBy=created_at, sortOrder=desc, projectId=1, keyword=null, startTime=null, endTime=null)
2025-06-25 09:19:55.202 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 0
2025-06-25 09:19:55.208 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.209 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.209 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.209 - INFO - [SimulationController.java:170] : 接收到运行仿真任务请求: simulationId=1
2025-06-25 09:19:55.210 - INFO - [SimulationController.java:176] : 仿真任务运行成功，任务ID: 1
2025-06-25 09:19:55.214 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.214 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.214 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.215 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=1
2025-06-25 09:19:55.215 - INFO - [SimulationController.java:294] : 获取台区电压统计报表成功，台区ID: 1, 用户总数: 100, 合格率: 95.5
2025-06-25 09:19:55.221 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.221 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.221 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.223 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=0
2025-06-25 09:19:55.223 -ERROR - [SimulationController.java:340] : 计算仿真电压合格率失败: 仿真ID不能为空或无效
2025-06-25 09:19:55.228 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.228 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.228 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.231 - INFO - [SimulationController.java:102] : 接收到更新仿真任务请求: simulationId=1, simulationModel=新模型, simulationDraft=新草稿, simulationScript=新脚本, nodes=新节点, controlStrategy=新控制策略
2025-06-25 09:19:55.231 - INFO - [SimulationController.java:108] : 仿真任务更新成功，ID: 1, 更新后模型: 新模型, 更新后脚本: 新脚本
2025-06-25 09:19:55.237 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.237 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.237 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.238 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=2
2025-06-25 09:19:55.238 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 2, 总数据点: 0, 合格数据点: 0, 合格率: 0.00%
2025-06-25 09:19:55.244 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.244 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.244 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.245 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=999
2025-06-25 09:19:55.245 -ERROR - [SimulationController.java:340] : 计算仿真电压合格率失败: 仿真任务不存在
2025-06-25 09:19:55.249 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.249 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.249 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.250 - INFO - [SimulationController.java:142] : 接收到复制仿真任务请求: simulationId=1
2025-06-25 09:19:55.250 - INFO - [SimulationController.java:146] : 仿真任务复制成功，新ID: 2
2025-06-25 09:19:55.255 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.255 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.255 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.256 - INFO - [SimulationController.java:218] : 接收到获取仿真任务输出路径请求: simulationId=1
2025-06-25 09:19:55.256 - INFO - [SimulationController.java:223] : 获取仿真任务输出路径成功，任务ID: 1, 输出路径: /output/path/simulation_1
2025-06-25 09:19:55.267 -ERROR - [DeviceServiceImpl.java:141] : 设备更新失败：参数代码已被其他设备使用: CONFLICT_PARAM
2025-06-25 09:19:55.269 -ERROR - [DeviceServiceImpl.java:168] : 设备删除失败：指定ID的设备不存在: 999
2025-06-25 09:19:55.269 - INFO - [DeviceServiceImpl.java:152] : 设备更新成功: Device(parameterId=1, deviceType=TRANSFORMER, parameterCode=UPDATED_PARAM, version=2.0)
2025-06-25 09:19:55.269 -ERROR - [DeviceServiceImpl.java:105] : 查询设备失败：ID不能为空
2025-06-25 09:19:55.271 - INFO - [DeviceServiceImpl.java:60] : 开始查询设备列表, page: 1, size: 10, sortBy: invalidField, sortOrder: desc, keyword: null
2025-06-25 09:19:55.271 - WARN - [DeviceServiceImpl.java:74] : 无效的排序字段: invalidField，使用默认排序字段 parameterId
2025-06-25 09:19:55.271 - INFO - [DeviceServiceImpl.java:90] : 查询设备列表成功，查询到 1 条数据
2025-06-25 09:19:55.271 - INFO - [DeviceServiceImpl.java:53] : 设备创建成功: Device(parameterId=null, deviceType=LINE, parameterCode=NEW_PARAM_001, version=1.0)
2025-06-25 09:19:55.272 - INFO - [DeviceServiceImpl.java:60] : 开始查询设备列表, page: 1, size: 10, sortBy: parameterId, sortOrder: desc, keyword: null
2025-06-25 09:19:55.272 - INFO - [DeviceServiceImpl.java:90] : 查询设备列表成功，查询到 1 条数据
2025-06-25 09:19:55.273 - INFO - [DeviceServiceImpl.java:178] : 设备删除成功: 1
2025-06-25 09:19:55.273 -ERROR - [DeviceServiceImpl.java:133] : 设备更新失败：指定ID的设备不存在: 999
2025-06-25 09:19:55.274 -ERROR - [DeviceServiceImpl.java:161] : 设备删除失败：ID不能为空
2025-06-25 09:19:55.274 -ERROR - [DeviceServiceImpl.java:42] : 设备创建失败：参数代码已存在: EXISTING_PARAM
2025-06-25 09:19:55.275 -ERROR - [DeviceServiceImpl.java:111] : 查询设备失败：指定ID的设备不存在: 999
2025-06-25 09:19:55.276 - INFO - [DeviceServiceImpl.java:96] : 开始查询设备总数, keyword: null
2025-06-25 09:19:55.277 - INFO - [DeviceServiceImpl.java:98] : 查询设备总数完成，共 5 条记录
2025-06-25 09:19:55.277 - INFO - [DeviceServiceImpl.java:115] : 查询设备成功: Device(parameterId=1, deviceType=LINE, parameterCode=TEST_PARAM_001, version=1.0)
2025-06-25 09:19:55.290 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.290 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.291 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.292 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 2
2025-06-25 09:19:55.293 - INFO - [DeviceController.java:60] : 设备更新成功: Device(parameterId=1, deviceType=LINE, parameterCode=UPDATED_PARAM001, version=1.1)
2025-06-25 09:19:55.293 - INFO - [DeviceController.java:60] : 设备更新成功: Device(parameterId=2, deviceType=TRANSFORMER, parameterCode=UPDATED_PARAM002, version=2.1)
2025-06-25 09:19:55.297 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.298 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.298 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.299 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 2
2025-06-25 09:19:55.299 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 1
2025-06-25 09:19:55.299 -ERROR - [DeviceController.java:69] : 设备操作失败: 设备类型不能为空.
2025-06-25 09:19:55.305 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.305 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.305 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.307 - INFO - [DeviceController.java:81] : 接收到分页查询设备请求: DeviceQueryDTO [page=1, size=10, sortBy=parameterId, sortOrder=desc, keyword=test]
2025-06-25 09:19:55.307 - INFO - [DeviceController.java:93] : 设备查询成功，总数量: 2
2025-06-25 09:19:55.312 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.312 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.312 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.313 - INFO - [DeviceController.java:111] : 接收到删除设备请求: parameterId=1
2025-06-25 09:19:55.313 - INFO - [DeviceController.java:115] : 设备删除结果: true
2025-06-25 09:19:55.317 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.319 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.319 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.319 - INFO - [DeviceController.java:111] : 接收到删除设备请求: parameterId=1
2025-06-25 09:19:55.319 - INFO - [DeviceController.java:115] : 设备删除结果: false
2025-06-25 09:19:55.324 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.325 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.325 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.326 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 1
2025-06-25 09:19:55.326 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 1
2025-06-25 09:19:55.332 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.332 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.332 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.333 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 1
2025-06-25 09:19:55.334 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 1
2025-06-25 09:19:55.339 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.339 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.340 - INFO - [FrameworkServlet.java:547] : Completed initialization in 1 ms
2025-06-25 09:19:55.341 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 2
2025-06-25 09:19:55.341 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 1
2025-06-25 09:19:55.342 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 2
2025-06-25 09:19:55.347 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:19:55.347 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:19:55.347 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:19:55.347 - INFO - [DeviceController.java:100] : 接收到查询设备请求: parameterId=1
2025-06-25 09:19:55.348 - INFO - [DeviceController.java:104] : 设备查询成功: Device(parameterId=1, deviceType=LINE, parameterCode=PARAM001, version=1.0)
2025-06-25 09:20:03.976 - INFO - [DatabaseUtils.java:81] : 创建数据库目录: D:\CETWorkSpace\ngap-server\.\test_data - 成功: true
2025-06-25 09:20:04.308 - INFO - [DatabaseUtils.java:97] : 开始执行SQL脚本: db/non_existent.sql
2025-06-25 09:20:04.312 - INFO - [DatabaseUtils.java:97] : 开始执行SQL脚本: db/init.sql
2025-06-25 09:20:05.411 - INFO - [DatabaseUtils.java:132] : SQL脚本执行完成: db/init.sql
2025-06-25 09:20:05.417 - WARN - [DatabaseUtils.java:146] : 数据库连接测试失败: jdbc:invalid:url - No suitable driver found for jdbc:invalid:url
2025-06-25 09:20:05.497 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-06-25 09:20:05.698 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-06-25 09:20:06.837 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-06-25 09:20:06.837 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:20:06.837 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:20:06.837 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-06-25 09:20:06.837 - INFO - [VoltageQualityReportPerformanceTest.java:96] : === 中等文件性能测试 ===
2025-06-25 09:20:06.985 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit8947374137494619378\medium_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit8947374137494619378\电压合格率报表.xlsx, 处理记录数: 100, 耗时: 148ms
2025-06-25 09:20:06.985 - INFO - [VoltageQualityReportPerformanceTest.java:107] : 中等文件处理时间: 148ms
2025-06-25 09:20:06.985 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 1
  总记录数: 100
  总处理时间: 148ms
  平均处理时间: 148ms
  处理速度: 675.68 记录/秒
  文件处理速度: 6.76 文件/秒
  操作统计:
    calculateOverallStats: OperationStats{count=1, total=2ms, avg=2ms, min=2ms, max=2ms}
    generateReport: OperationStats{count=1, total=27ms, avg=27ms, min=27ms, max=27ms}
    generateVoltageReport: OperationStats{count=1, total=148ms, avg=148ms, min=148ms, max=148ms}
    readVoltageData: OperationStats{count=1, total=113ms, avg=113ms, min=113ms, max=113ms}
    calculateUserStats: OperationStats{count=1, total=2ms, avg=2ms, min=2ms, max=2ms}
    groupByUser: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
}
2025-06-25 09:20:06.986 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:20:06.986 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:20:07.007 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-06-25 09:20:07.137 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-06-25 09:20:08.271 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-06-25 09:20:08.272 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:20:08.272 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:20:08.272 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-06-25 09:20:08.272 - INFO - [VoltageQualityReportPerformanceTest.java:66] : === 小文件性能测试 ===
2025-06-25 09:20:08.294 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit6869393242967995508\small_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit6869393242967995508\电压合格率报表.xlsx, 处理记录数: 10, 耗时: 22ms
2025-06-25 09:20:08.315 - INFO - [VoltageQualityReportPerformanceTest.java:82] : 小文件处理时间 - 第一次: 22ms, 第二次: 21ms, 提升: 4.545454545454546%
2025-06-25 09:20:08.315 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 1
  总记录数: 10
  总处理时间: 22ms
  平均处理时间: 22ms
  处理速度: 454.55 记录/秒
  文件处理速度: 45.45 文件/秒
  操作统计:
    calculateOverallStats: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
    generateReport: OperationStats{count=1, total=7ms, avg=7ms, min=7ms, max=7ms}
    generateVoltageReport: OperationStats{count=2, total=43ms, avg=21ms, min=21ms, max=22ms}
    readVoltageData: OperationStats{count=1, total=15ms, avg=15ms, min=15ms, max=15ms}
    calculateUserStats: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
    groupByUser: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
}
2025-06-25 09:20:08.315 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:20:08.315 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:20:08.336 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-06-25 09:20:08.453 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-06-25 09:20:09.575 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-06-25 09:20:09.575 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:20:09.575 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:20:09.575 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-06-25 09:20:09.575 - INFO - [VoltageQualityReportPerformanceTest.java:141] : === 缓存效率测试 ===
2025-06-25 09:20:09.632 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit5222607745242219611\medium_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit5222607745242219611\电压合格率报表.xlsx, 处理记录数: 100, 耗时: 57ms
2025-06-25 09:20:09.632 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:20:09.682 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第1次处理时间: 50ms
2025-06-25 09:20:09.707 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第2次处理时间: 25ms
2025-06-25 09:20:09.734 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第3次处理时间: 27ms
2025-06-25 09:20:09.766 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第4次处理时间: 32ms
2025-06-25 09:20:09.796 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第5次处理时间: 30ms
2025-06-25 09:20:09.796 - INFO - [VoltageQualityReportPerformanceTest.java:162] : 缓存统计: CacheStats{size=6, hits=12, misses=18, hitRate=40.00%}
2025-06-25 09:20:09.796 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 0
  总记录数: 0
  总处理时间: 0ms
  平均处理时间: 0ms
  处理速度: 0.00 记录/秒
  文件处理速度: 0.00 文件/秒
  操作统计:
    generateVoltageReport: OperationStats{count=5, total=164ms, avg=32ms, min=25ms, max=50ms}
}
2025-06-25 09:20:09.796 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:20:09.796 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:20:09.817 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-06-25 09:20:09.937 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-06-25 09:20:11.086 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-06-25 09:20:11.086 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:20:11.086 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:20:11.086 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-06-25 09:20:11.086 - INFO - [VoltageQualityReportPerformanceTest.java:115] : === 大文件性能测试 ===
2025-06-25 09:20:11.456 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit1887052761863343431\large_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit1887052761863343431\电压合格率报表.xlsx, 处理记录数: 1000, 耗时: 370ms
2025-06-25 09:20:11.457 - INFO - [VoltageQualityReportPerformanceTest.java:126] : 大文件处理时间: 371ms
2025-06-25 09:20:11.457 - INFO - [VoltageQualityReportPerformanceTest.java:136] : 处理速度: 2702.70 记录/秒
2025-06-25 09:20:11.457 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 1
  总记录数: 1000
  总处理时间: 370ms
  平均处理时间: 370ms
  处理速度: 2702.70 记录/秒
  文件处理速度: 2.70 文件/秒
  操作统计:
    calculateOverallStats: OperationStats{count=1, total=4ms, avg=4ms, min=4ms, max=4ms}
    generateReport: OperationStats{count=1, total=87ms, avg=87ms, min=87ms, max=87ms}
    generateVoltageReport: OperationStats{count=1, total=371ms, avg=371ms, min=371ms, max=371ms}
    readVoltageData: OperationStats{count=1, total=267ms, avg=267ms, min=267ms, max=267ms}
    calculateUserStats: OperationStats{count=1, total=12ms, avg=12ms, min=12ms, max=12ms}
    groupByUser: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
}
2025-06-25 09:20:11.457 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:20:11.457 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:20:11.476 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-06-25 09:20:11.594 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-06-25 09:20:12.710 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-06-25 09:20:12.710 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:20:12.710 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:20:12.710 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-06-25 09:20:12.711 - INFO - [VoltageQualityReportPerformanceTest.java:175] : === 内存效率测试 ===
2025-06-25 09:20:13.399 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit2837508246823188929\large_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit2837508246823188929\电压合格率报表.xlsx, 处理记录数: 1000, 耗时: 348ms
2025-06-25 09:20:13.498 - INFO - [VoltageQualityReportPerformanceTest.java:190] : 内存使用: 初始=111MB, 最终=120MB, 增加=9MB
2025-06-25 09:20:13.498 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 1
  总记录数: 1000
  总处理时间: 348ms
  平均处理时间: 348ms
  处理速度: 2873.56 记录/秒
  文件处理速度: 2.87 文件/秒
  操作统计:
    calculateOverallStats: OperationStats{count=1, total=1ms, avg=1ms, min=1ms, max=1ms}
    generateReport: OperationStats{count=1, total=86ms, avg=86ms, min=86ms, max=86ms}
    generateVoltageReport: OperationStats{count=1, total=349ms, avg=349ms, min=349ms, max=349ms}
    readVoltageData: OperationStats{count=1, total=255ms, avg=255ms, min=255ms, max=255ms}
    calculateUserStats: OperationStats{count=1, total=6ms, avg=6ms, min=6ms, max=6ms}
    groupByUser: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
}
2025-06-25 09:20:13.499 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:20:13.499 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:20:14.547 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:20:14.552 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:20:14.562 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:20:14.562 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:20:14.572 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:20:14.584 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit1417374820258727512\test_voltage_data.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit1417374820258727512\电压合格率报表.xlsx, 处理记录数: 3, 耗时: 12ms
2025-06-25 09:20:14.584 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:20:14.596 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:20:14.602 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:20:14.611 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:20:14.612 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:20:16.215 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-25 09:20:16.215 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-25 09:20:16.216 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *******************************
2025-06-25 09:20:16.216 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-06-25 09:20:16.217 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-06-25 09:20:16.217 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-25 09:20:16.219 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-25 09:20:16.220 -ERROR - [DatabaseInitializer.java:54] : 数据库初始化失败
java.lang.ClassNotFoundException: invalid.driver.Class
	at java.net.URLClassLoader.findClass(URLClassLoader.java:381)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:335)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:264)
	at com.cet.electric.ngapserver.config.DatabaseInitializer.run(DatabaseInitializer.java:40)
	at com.cet.electric.ngapserver.config.DatabaseInitializerTest.testRun_WhenDriverLoadFails_ShouldThrowException(DatabaseInitializerTest.java:137)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.internal.runners.statements.ExpectException.evaluate(ExpectException.java:19)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.mockito.internal.runners.DefaultInternalRunner$1.run(DefaultInternalRunner.java:78)
	at org.mockito.internal.runners.DefaultInternalRunner.run(DefaultInternalRunner.java:84)
	at org.mockito.internal.runners.StrictRunner.run(StrictRunner.java:39)
	at org.mockito.junit.MockitoJUnitRunner.run(MockitoJUnitRunner.java:161)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
	at org.junit.vintage.engine.execution.RunnerExecutor.execute(RunnerExecutor.java:43)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.Iterator.forEachRemaining(Iterator.java:116)
	at java.util.Spliterators$IteratorSpliterator.forEachRemaining(Spliterators.java:1801)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:481)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:471)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.junit.vintage.engine.VintageTestEngine.executeAllChildren(VintageTestEngine.java:82)
	at org.junit.vintage.engine.VintageTestEngine.execute(VintageTestEngine.java:73)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:248)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$5(DefaultLauncher.java:211)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:226)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:199)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:132)
	at com.intellij.junit5.JUnit5IdeaTestRunner.startRunnerWithArgs(JUnit5IdeaTestRunner.java:57)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
2025-06-25 09:20:16.222 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-25 09:20:16.222 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-25 09:20:16.222 - INFO - [DatabaseInitializer.java:45] : 数据库文件不存在，开始创建数据库: *******************************
2025-06-25 09:20:16.222 - INFO - [DatabaseInitializer.java:66] : 开始初始化数据库...
2025-06-25 09:20:16.223 - INFO - [DatabaseUtils.java:81] : 创建数据库目录: D:\CETWorkSpace\ngap-server\.\test_data - 成功: true
2025-06-25 09:20:16.225 - INFO - [DatabaseInitializer.java:75] : 数据库连接创建成功，开始执行建表脚本...
2025-06-25 09:20:16.225 - INFO - [DatabaseUtils.java:97] : 开始执行SQL脚本: db/init.sql
2025-06-25 09:20:17.359 - INFO - [DatabaseUtils.java:132] : SQL脚本执行完成: db/init.sql
2025-06-25 09:20:17.359 - INFO - [DatabaseInitializer.java:80] : 数据库初始化完成
2025-06-25 09:20:17.360 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-25 09:20:17.504 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-25 09:20:17.504 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-25 09:20:17.504 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *******************************
2025-06-25 09:20:17.504 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-06-25 09:20:17.505 - WARN - [DatabaseInitializer.java:103] : 缺少必要的表: simulation
2025-06-25 09:20:17.505 - WARN - [DatabaseInitializer.java:103] : 缺少必要的表: device
2025-06-25 09:20:17.505 - WARN - [DatabaseInitializer.java:109] : 数据库表结构不完整，重新执行初始化脚本...
2025-06-25 09:20:17.505 - INFO - [DatabaseUtils.java:97] : 开始执行SQL脚本: db/init.sql
2025-06-25 09:20:18.730 - INFO - [DatabaseUtils.java:132] : SQL脚本执行完成: db/init.sql
2025-06-25 09:20:18.730 - INFO - [DatabaseInitializer.java:111] : 数据库表结构修复完成
2025-06-25 09:20:18.731 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-25 09:20:18.745 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:20:18.745 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:20:18.745 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:20:18.746 - INFO - [ProjectController.java:38] : 接收到创建项目请求: projectName=测试项目, projectType=
2025-06-25 09:20:18.746 - INFO - [ProjectController.java:51] : 项目创建成功，ID: 1
2025-06-25 09:20:18.752 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:20:18.752 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:20:18.752 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:20:18.753 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-25 09:20:18.753 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-25 09:20:18.758 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:20:18.758 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:20:18.758 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:20:18.759 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=10, sortBy=created_at, sortOrder=desc, projectType=null, keyword=null, startTime=null, endTime=null)
2025-06-25 09:20:18.759 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 0
2025-06-25 09:20:18.764 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:20:18.764 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:20:18.764 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:20:18.765 - INFO - [ProjectController.java:134] : 接收到导出项目配置文件请求: projectId=1
2025-06-25 09:20:18.769 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:20:18.769 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:20:18.769 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:20:18.770 - INFO - [ProjectController.java:38] : 接收到创建项目请求: projectName=  测试项目  , projectType=  General_scenario  
2025-06-25 09:20:18.770 - INFO - [ProjectController.java:51] : 项目创建成功，ID: 1
2025-06-25 09:20:18.775 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:20:18.776 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:20:18.776 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:20:18.776 - INFO - [ProjectController.java:142] : 接收到导入项目配置文件请求，文件名：project.json, 文件大小：66
2025-06-25 09:20:18.777 - INFO - [ProjectController.java:146] : 项目配置文件导入成功，项目ID：1
2025-06-25 09:20:18.781 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:20:18.781 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:20:18.781 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:20:18.782 - INFO - [ProjectController.java:119] : 接收到删除项目请求: projectId=1
2025-06-25 09:20:18.782 - INFO - [ProjectController.java:124] : 项目删除成功，项目ID: 1
2025-06-25 09:20:18.787 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:20:18.787 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:20:18.787 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:20:18.788 - INFO - [ProjectController.java:38] : 接收到创建项目请求: projectName=测试项目, projectType=General_scenario
2025-06-25 09:20:18.788 - INFO - [ProjectController.java:51] : 项目创建成功，ID: 1
2025-06-25 09:20:18.793 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:20:18.793 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:20:18.793 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:20:18.794 - INFO - [ProjectController.java:106] : 接收到重命名项目请求: projectId=1, newName=新项目名称
2025-06-25 09:20:18.794 - INFO - [ProjectController.java:111] : 项目重命名成功，项目ID: 1, 新名称: 新项目名称
2025-06-25 09:20:18.799 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:20:18.799 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:20:18.799 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:20:18.799 - INFO - [ProjectController.java:91] : 接收到复制项目请求: projectId=1
2025-06-25 09:20:18.799 - INFO - [ProjectController.java:96] : 项目复制成功，原项目ID: 1, 新项目ID: 2
2025-06-25 09:20:19.432 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-25 09:20:19.433 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-25 09:20:19.433 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:8080, DSS文件路径: /test/path/test.dss
2025-06-25 09:20:19.434 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 200 OK
2025-06-25 09:20:19.434 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-25 09:20:19.435 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:8080, DSS文件路径: /test/path/test.dss
2025-06-25 09:20:19.435 - WARN - [DssUtils.java:74] : DSS服务返回空的错误响应，已创建默认错误信息
2025-06-25 09:20:19.435 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 500 INTERNAL_SERVER_ERROR
2025-06-25 09:20:19.435 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-25 09:20:19.435 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:8080, DSS文件路径: /test/path/test.dss
2025-06-25 09:20:19.435 - INFO - [DssUtils.java:88] : 为DSS错误响应添加output_data字段
2025-06-25 09:20:19.435 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 500 INTERNAL_SERVER_ERROR
2025-06-25 09:20:19.436 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-25 09:20:19.436 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:8080, DSS文件路径: /test/path/test.dss
2025-06-25 09:20:19.436 - INFO - [DssUtils.java:88] : 为DSS错误响应添加output_data字段
2025-06-25 09:20:19.436 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 400 BAD_REQUEST
2025-06-25 09:20:21.667 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-06-25 09:20:21.668 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-25 09:20:21.672 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-06-25 09:20:21.675 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-06-25 09:22:42.674 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 09:22:42.675 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 09:22:42.692 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 09:22:42.692 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 09:22:42.693 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 09:22:42.693 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 09:25:13.592 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 09:25:13.592 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 09:25:13.593 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 09:25:13.593 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 09:25:13.593 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 09:25:13.593 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 09:28:23.785 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplicationTests on DESKTOP-9LFKQ75 with PID 32664 (started by dell in D:\CETWorkSpace\ngap-server)
2025-06-25 09:28:23.786 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-06-25 09:28:25.039 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-25 09:28:25.739 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-06-25 09:28:25.853 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-06-25 09:28:26.064 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-06-25 09:28:26.280 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-06-25 09:28:26.311 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-06-25 09:28:26.327 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-06-25 09:28:26.349 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-06-25 09:28:26.474 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-06-25 09:28:26.492 - INFO - [TaskSchedule.java:38] : 开始删除
2025-06-25 09:28:26.493 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-06-25 09:28:26.495 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplicationTests in 3.189 seconds (JVM running for 7.257)
2025-06-25 09:28:26.500 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-25 09:28:26.500 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-25 09:28:26.500 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-06-25 09:28:26.500 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-06-25 09:28:26.519 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-06-25 09:28:26.616 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-06-25 09:28:26.616 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-25 09:28:26.629 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-06-25 09:28:26.644 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-06-25 09:28:26.644 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-06-25 09:28:26.672 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-06-25 09:28:26.673 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-06-25 09:28:27.413 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.413 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.413 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.440 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=1
2025-06-25 09:28:27.440 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 1, 指标数量: 2
2025-06-25 09:28:27.480 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.480 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.480 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.499 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=1, queryDTO=UserVoltageReportQueryDTO(keyword=用户, pageNum=1, pageSize=10)
2025-06-25 09:28:27.499 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 1, 用户数量: 2
2025-06-25 09:28:27.506 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.506 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.507 - INFO - [FrameworkServlet.java:547] : Completed initialization in 1 ms
2025-06-25 09:28:27.508 - INFO - [SimulationController.java:60] : 接收到创建仿真任务请求: projectId=100, simulationName=测试仿真
2025-06-25 09:28:27.508 - INFO - [SimulationController.java:73] : 仿真任务创建成功，ID: 1
2025-06-25 09:28:27.514 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.515 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.515 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.515 - INFO - [SimulationController.java:183] : 接收到获取仿真策略列表请求
2025-06-25 09:28:27.515 - INFO - [SimulationController.java:186] : 仿真策略列表获取成功，总数量: 2
2025-06-25 09:28:27.522 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.522 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.522 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.523 - INFO - [SimulationController.java:118] : 接收到删除仿真任务请求: simulationId=1
2025-06-25 09:28:27.523 - INFO - [SimulationController.java:122] : 仿真任务删除成功，ID: 1
2025-06-25 09:28:27.529 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.529 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.529 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.530 - INFO - [SimulationController.java:244] : 接收到获取仿真任务指标数据请求: simulationId=1, metricName=voltage
2025-06-25 09:28:27.530 - INFO - [SimulationController.java:249] : 获取仿真任务指标数据成功，任务ID: 1, 指标名称: voltage, 数据点数量: 3
2025-06-25 09:28:27.536 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.536 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.536 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.539 - INFO - [SimulationController.java:156] : 接收到上传文件请求，文件名：test.dss, 文件类型：dss, 文件大小：17
2025-06-25 09:28:27.539 - INFO - [SimulationController.java:162] : 文件上传成功，保存路径：/path/to/uploaded/file
2025-06-25 09:28:27.545 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.545 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.545 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.549 - INFO - [SimulationController.java:276] : 接收到导出仿真任务指标数据请求: simulationId=1, queryDTO=BatchMetricExportDTO(pic=chart, metricIds=[voltage, current], startTimestamp=1000)
2025-06-25 09:28:27.553 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.553 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.554 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.554 - INFO - [SimulationController.java:206] : 接收到导入仿真任务请求，文件名：simulation.json, 文件大小：36
2025-06-25 09:28:27.554 - INFO - [SimulationController.java:210] : 仿真任务导入成功，仿真任务ID：1
2025-06-25 09:28:27.560 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.561 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.561 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.562 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=1, queryDTO=BatchMetricQueryDTO(metricIds=[voltage, current], startTimestamp=1000)
2025-06-25 09:28:27.563 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 1, 指标数量: 2
2025-06-25 09:28:27.568 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.568 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.568 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.569 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=4
2025-06-25 09:28:27.569 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 4, 总数据点: 1000, 合格数据点: 985, 合格率: 98.50%
2025-06-25 09:28:27.576 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.577 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.577 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.577 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=1
2025-06-25 09:28:27.577 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 1, 总数据点: 10000, 合格数据点: 8550, 合格率: 85.50%
2025-06-25 09:28:27.582 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.582 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.583 - INFO - [FrameworkServlet.java:547] : Completed initialization in 1 ms
2025-06-25 09:28:27.583 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=3
2025-06-25 09:28:27.583 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 3, 总数据点: 100, 合格数据点: 75, 合格率: 75.00%
2025-06-25 09:28:27.589 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.589 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.589 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.589 - INFO - [SimulationController.java:130] : 接收到获取仿真任务详情请求: simulationId=1
2025-06-25 09:28:27.589 - INFO - [SimulationController.java:134] : 获取仿真任务详情成功，ID: 1
2025-06-25 09:28:27.594 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.594 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.594 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.595 - INFO - [SimulationController.java:82] : 接收到重命名仿真任务请求: simulationId=1, newName=新仿真名称
2025-06-25 09:28:27.595 - INFO - [SimulationController.java:86] : 仿真任务重命名成功，ID: 1, 新名称: 新仿真名称
2025-06-25 09:28:27.601 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.601 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.601 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.602 - INFO - [SimulationController.java:60] : 接收到创建仿真任务请求: projectId=100, simulationName=  测试仿真  
2025-06-25 09:28:27.602 - INFO - [SimulationController.java:73] : 仿真任务创建成功，ID: 1
2025-06-25 09:28:27.607 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.607 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.607 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.608 - INFO - [SimulationController.java:195] : 接收到导出仿真任务请求: simulationId=1
2025-06-25 09:28:27.612 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.612 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.612 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.613 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=10, sortBy=created_at, sortOrder=desc, projectId=1, keyword=null, startTime=null, endTime=null)
2025-06-25 09:28:27.613 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 0
2025-06-25 09:28:27.620 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.620 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.620 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.621 - INFO - [SimulationController.java:170] : 接收到运行仿真任务请求: simulationId=1
2025-06-25 09:28:27.621 - INFO - [SimulationController.java:176] : 仿真任务运行成功，任务ID: 1
2025-06-25 09:28:27.627 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.627 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.627 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.629 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=1
2025-06-25 09:28:27.629 - INFO - [SimulationController.java:294] : 获取台区电压统计报表成功，台区ID: 1, 用户总数: 100, 合格率: 95.5
2025-06-25 09:28:27.634 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.634 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.634 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.636 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=0
2025-06-25 09:28:27.636 -ERROR - [SimulationController.java:340] : 计算仿真电压合格率失败: 仿真ID不能为空或无效
2025-06-25 09:28:27.641 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.641 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.641 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.644 - INFO - [SimulationController.java:102] : 接收到更新仿真任务请求: simulationId=1, simulationModel=新模型, simulationDraft=新草稿, simulationScript=新脚本, nodes=新节点, controlStrategy=新控制策略
2025-06-25 09:28:27.644 - INFO - [SimulationController.java:108] : 仿真任务更新成功，ID: 1, 更新后模型: 新模型, 更新后脚本: 新脚本
2025-06-25 09:28:27.648 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.648 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.648 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.649 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=2
2025-06-25 09:28:27.649 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 2, 总数据点: 0, 合格数据点: 0, 合格率: 0.00%
2025-06-25 09:28:27.654 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.654 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.654 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.655 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=999
2025-06-25 09:28:27.655 -ERROR - [SimulationController.java:340] : 计算仿真电压合格率失败: 仿真任务不存在
2025-06-25 09:28:27.659 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.659 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.660 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.660 - INFO - [SimulationController.java:142] : 接收到复制仿真任务请求: simulationId=1
2025-06-25 09:28:27.660 - INFO - [SimulationController.java:146] : 仿真任务复制成功，新ID: 2
2025-06-25 09:28:27.664 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.664 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.665 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.665 - INFO - [SimulationController.java:218] : 接收到获取仿真任务输出路径请求: simulationId=1
2025-06-25 09:28:27.665 - INFO - [SimulationController.java:223] : 获取仿真任务输出路径成功，任务ID: 1, 输出路径: /output/path/simulation_1
2025-06-25 09:28:27.674 -ERROR - [DeviceServiceImpl.java:141] : 设备更新失败：参数代码已被其他设备使用: CONFLICT_PARAM
2025-06-25 09:28:27.675 -ERROR - [DeviceServiceImpl.java:168] : 设备删除失败：指定ID的设备不存在: 999
2025-06-25 09:28:27.675 - INFO - [DeviceServiceImpl.java:152] : 设备更新成功: Device(parameterId=1, deviceType=TRANSFORMER, parameterCode=UPDATED_PARAM, version=2.0)
2025-06-25 09:28:27.676 -ERROR - [DeviceServiceImpl.java:105] : 查询设备失败：ID不能为空
2025-06-25 09:28:27.676 - INFO - [DeviceServiceImpl.java:60] : 开始查询设备列表, page: 1, size: 10, sortBy: invalidField, sortOrder: desc, keyword: null
2025-06-25 09:28:27.676 - WARN - [DeviceServiceImpl.java:74] : 无效的排序字段: invalidField，使用默认排序字段 parameterId
2025-06-25 09:28:27.676 - INFO - [DeviceServiceImpl.java:90] : 查询设备列表成功，查询到 1 条数据
2025-06-25 09:28:27.677 - INFO - [DeviceServiceImpl.java:53] : 设备创建成功: Device(parameterId=null, deviceType=LINE, parameterCode=NEW_PARAM_001, version=1.0)
2025-06-25 09:28:27.677 - INFO - [DeviceServiceImpl.java:60] : 开始查询设备列表, page: 1, size: 10, sortBy: parameterId, sortOrder: desc, keyword: null
2025-06-25 09:28:27.677 - INFO - [DeviceServiceImpl.java:90] : 查询设备列表成功，查询到 1 条数据
2025-06-25 09:28:27.678 - INFO - [DeviceServiceImpl.java:178] : 设备删除成功: 1
2025-06-25 09:28:27.678 -ERROR - [DeviceServiceImpl.java:133] : 设备更新失败：指定ID的设备不存在: 999
2025-06-25 09:28:27.679 -ERROR - [DeviceServiceImpl.java:161] : 设备删除失败：ID不能为空
2025-06-25 09:28:27.680 -ERROR - [DeviceServiceImpl.java:42] : 设备创建失败：参数代码已存在: EXISTING_PARAM
2025-06-25 09:28:27.680 -ERROR - [DeviceServiceImpl.java:111] : 查询设备失败：指定ID的设备不存在: 999
2025-06-25 09:28:27.681 - INFO - [DeviceServiceImpl.java:96] : 开始查询设备总数, keyword: null
2025-06-25 09:28:27.681 - INFO - [DeviceServiceImpl.java:98] : 查询设备总数完成，共 5 条记录
2025-06-25 09:28:27.681 - INFO - [DeviceServiceImpl.java:115] : 查询设备成功: Device(parameterId=1, deviceType=LINE, parameterCode=TEST_PARAM_001, version=1.0)
2025-06-25 09:28:27.693 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.693 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.693 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.694 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 2
2025-06-25 09:28:27.695 - INFO - [DeviceController.java:60] : 设备更新成功: Device(parameterId=1, deviceType=LINE, parameterCode=UPDATED_PARAM001, version=1.1)
2025-06-25 09:28:27.695 - INFO - [DeviceController.java:60] : 设备更新成功: Device(parameterId=2, deviceType=TRANSFORMER, parameterCode=UPDATED_PARAM002, version=2.1)
2025-06-25 09:28:27.698 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.699 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.699 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.700 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 2
2025-06-25 09:28:27.700 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 1
2025-06-25 09:28:27.700 -ERROR - [DeviceController.java:69] : 设备操作失败: 设备类型不能为空.
2025-06-25 09:28:27.704 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.704 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.704 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.705 - INFO - [DeviceController.java:81] : 接收到分页查询设备请求: DeviceQueryDTO [page=1, size=10, sortBy=parameterId, sortOrder=desc, keyword=test]
2025-06-25 09:28:27.706 - INFO - [DeviceController.java:93] : 设备查询成功，总数量: 2
2025-06-25 09:28:27.710 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.710 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.710 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.711 - INFO - [DeviceController.java:111] : 接收到删除设备请求: parameterId=1
2025-06-25 09:28:27.711 - INFO - [DeviceController.java:115] : 设备删除结果: true
2025-06-25 09:28:27.714 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.714 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.714 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.715 - INFO - [DeviceController.java:111] : 接收到删除设备请求: parameterId=1
2025-06-25 09:28:27.715 - INFO - [DeviceController.java:115] : 设备删除结果: false
2025-06-25 09:28:27.719 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.719 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.719 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.721 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 1
2025-06-25 09:28:27.721 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 1
2025-06-25 09:28:27.725 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.725 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.725 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.726 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 1
2025-06-25 09:28:27.726 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 1
2025-06-25 09:28:27.729 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.729 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.730 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.731 - INFO - [DeviceController.java:35] : 接收到批量保存设备请求，数量: 2
2025-06-25 09:28:27.731 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 1
2025-06-25 09:28:27.731 - INFO - [DeviceController.java:64] : 设备创建成功，ID: 2
2025-06-25 09:28:27.735 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:27.735 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:27.735 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:27.736 - INFO - [DeviceController.java:100] : 接收到查询设备请求: parameterId=1
2025-06-25 09:28:27.736 - INFO - [DeviceController.java:104] : 设备查询成功: Device(parameterId=1, deviceType=LINE, parameterCode=PARAM001, version=1.0)
2025-06-25 09:28:32.472 - INFO - [DatabaseUtils.java:81] : 创建数据库目录: D:\CETWorkSpace\ngap-server\.\test_data - 成功: true
2025-06-25 09:28:33.103 - INFO - [DatabaseUtils.java:97] : 开始执行SQL脚本: db/non_existent.sql
2025-06-25 09:28:33.107 - INFO - [DatabaseUtils.java:97] : 开始执行SQL脚本: db/init.sql
2025-06-25 09:28:34.167 - INFO - [DatabaseUtils.java:132] : SQL脚本执行完成: db/init.sql
2025-06-25 09:28:34.174 - WARN - [DatabaseUtils.java:146] : 数据库连接测试失败: jdbc:invalid:url - No suitable driver found for jdbc:invalid:url
2025-06-25 09:28:34.233 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-06-25 09:28:34.360 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-06-25 09:28:34.845 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-06-25 09:28:34.845 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:28:34.845 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:28:34.845 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-06-25 09:28:34.845 - INFO - [VoltageQualityReportPerformanceTest.java:96] : === 中等文件性能测试 ===
2025-06-25 09:28:34.976 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit6430405959672478651\medium_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit6430405959672478651\电压合格率报表.xlsx, 处理记录数: 100, 耗时: 131ms
2025-06-25 09:28:34.976 - INFO - [VoltageQualityReportPerformanceTest.java:107] : 中等文件处理时间: 131ms
2025-06-25 09:28:34.976 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 1
  总记录数: 100
  总处理时间: 131ms
  平均处理时间: 131ms
  处理速度: 763.36 记录/秒
  文件处理速度: 7.63 文件/秒
  操作统计:
    calculateOverallStats: OperationStats{count=1, total=4ms, avg=4ms, min=4ms, max=4ms}
    generateReport: OperationStats{count=1, total=31ms, avg=31ms, min=31ms, max=31ms}
    generateVoltageReport: OperationStats{count=1, total=131ms, avg=131ms, min=131ms, max=131ms}
    readVoltageData: OperationStats{count=1, total=92ms, avg=92ms, min=92ms, max=92ms}
    calculateUserStats: OperationStats{count=1, total=2ms, avg=2ms, min=2ms, max=2ms}
    groupByUser: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
}
2025-06-25 09:28:34.977 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:28:34.977 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:28:34.996 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-06-25 09:28:35.047 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-06-25 09:28:35.516 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-06-25 09:28:35.516 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:28:35.516 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:28:35.516 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-06-25 09:28:35.516 - INFO - [VoltageQualityReportPerformanceTest.java:66] : === 小文件性能测试 ===
2025-06-25 09:28:35.535 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit484069908157667290\small_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit484069908157667290\电压合格率报表.xlsx, 处理记录数: 10, 耗时: 19ms
2025-06-25 09:28:35.558 - INFO - [VoltageQualityReportPerformanceTest.java:82] : 小文件处理时间 - 第一次: 19ms, 第二次: 23ms, 提升: -21.052631578947366%
2025-06-25 09:28:35.558 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 1
  总记录数: 10
  总处理时间: 19ms
  平均处理时间: 19ms
  处理速度: 526.32 记录/秒
  文件处理速度: 52.63 文件/秒
  操作统计:
    calculateOverallStats: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
    generateReport: OperationStats{count=1, total=8ms, avg=8ms, min=8ms, max=8ms}
    generateVoltageReport: OperationStats{count=2, total=42ms, avg=21ms, min=19ms, max=23ms}
    readVoltageData: OperationStats{count=1, total=11ms, avg=11ms, min=11ms, max=11ms}
    calculateUserStats: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
    groupByUser: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
}
2025-06-25 09:28:35.558 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:28:35.558 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:28:35.571 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-06-25 09:28:35.621 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-06-25 09:28:36.094 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-06-25 09:28:36.094 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:28:36.094 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:28:36.094 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-06-25 09:28:36.094 - INFO - [VoltageQualityReportPerformanceTest.java:141] : === 缓存效率测试 ===
2025-06-25 09:28:36.133 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit3343029532155043001\medium_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit3343029532155043001\电压合格率报表.xlsx, 处理记录数: 100, 耗时: 39ms
2025-06-25 09:28:36.133 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:28:36.169 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第1次处理时间: 36ms
2025-06-25 09:28:36.187 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第2次处理时间: 17ms
2025-06-25 09:28:36.204 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第3次处理时间: 16ms
2025-06-25 09:28:36.219 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第4次处理时间: 15ms
2025-06-25 09:28:36.237 - INFO - [VoltageQualityReportPerformanceTest.java:157] : 第5次处理时间: 18ms
2025-06-25 09:28:36.237 - INFO - [VoltageQualityReportPerformanceTest.java:162] : 缓存统计: CacheStats{size=6, hits=12, misses=18, hitRate=40.00%}
2025-06-25 09:28:36.237 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 0
  总记录数: 0
  总处理时间: 0ms
  平均处理时间: 0ms
  处理速度: 0.00 记录/秒
  文件处理速度: 0.00 文件/秒
  操作统计:
    generateVoltageReport: OperationStats{count=5, total=102ms, avg=20ms, min=15ms, max=36ms}
}
2025-06-25 09:28:36.237 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:28:36.238 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:28:36.252 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-06-25 09:28:36.302 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-06-25 09:28:36.776 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-06-25 09:28:36.776 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:28:36.776 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:28:36.776 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-06-25 09:28:36.776 - INFO - [VoltageQualityReportPerformanceTest.java:115] : === 大文件性能测试 ===
2025-06-25 09:28:37.002 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit6859149673133449655\large_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit6859149673133449655\电压合格率报表.xlsx, 处理记录数: 1000, 耗时: 226ms
2025-06-25 09:28:37.002 - INFO - [VoltageQualityReportPerformanceTest.java:126] : 大文件处理时间: 226ms
2025-06-25 09:28:37.002 - INFO - [VoltageQualityReportPerformanceTest.java:136] : 处理速度: 4424.78 记录/秒
2025-06-25 09:28:37.003 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 1
  总记录数: 1000
  总处理时间: 226ms
  平均处理时间: 226ms
  处理速度: 4424.78 记录/秒
  文件处理速度: 4.42 文件/秒
  操作统计:
    calculateOverallStats: OperationStats{count=1, total=4ms, avg=4ms, min=4ms, max=4ms}
    generateReport: OperationStats{count=1, total=38ms, avg=38ms, min=38ms, max=38ms}
    generateVoltageReport: OperationStats{count=1, total=226ms, avg=226ms, min=226ms, max=226ms}
    readVoltageData: OperationStats{count=1, total=173ms, avg=173ms, min=173ms, max=173ms}
    calculateUserStats: OperationStats{count=1, total=10ms, avg=10ms, min=10ms, max=10ms}
    groupByUser: OperationStats{count=1, total=1ms, avg=1ms, min=1ms, max=1ms}
}
2025-06-25 09:28:37.003 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:28:37.003 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:28:37.013 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: small_test.xlsx - 用户数: 10, 电压列数: 96, 文件大小: 16KB
2025-06-25 09:28:37.061 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: medium_test.xlsx - 用户数: 100, 电压列数: 96, 文件大小: 128KB
2025-06-25 09:28:37.514 - INFO - [VoltageQualityReportPerformanceTest.java:241] : 创建测试文件: large_test.xlsx - 用户数: 1000, 电压列数: 96, 文件大小: 1247KB
2025-06-25 09:28:37.514 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:28:37.514 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:28:37.514 - INFO - [VoltageQualityReportPerformanceTest.java:49] : 性能测试环境准备完成
2025-06-25 09:28:37.514 - INFO - [VoltageQualityReportPerformanceTest.java:175] : === 内存效率测试 ===
2025-06-25 09:28:37.939 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit8588004942649558892\large_test.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit8588004942649558892\电压合格率报表.xlsx, 处理记录数: 1000, 耗时: 198ms
2025-06-25 09:28:38.019 - INFO - [VoltageQualityReportPerformanceTest.java:190] : 内存使用: 初始=54MB, 最终=64MB, 增加=10MB
2025-06-25 09:28:38.020 - INFO - [VoltageQualityReportPerformanceTest.java:57] : 性能测试报告:
VoltagePerformanceReport{
  总文件数: 1
  总记录数: 1000
  总处理时间: 198ms
  平均处理时间: 198ms
  处理速度: 5050.51 记录/秒
  文件处理速度: 5.05 文件/秒
  操作统计:
    calculateOverallStats: OperationStats{count=1, total=1ms, avg=1ms, min=1ms, max=1ms}
    generateReport: OperationStats{count=1, total=36ms, avg=36ms, min=36ms, max=36ms}
    generateVoltageReport: OperationStats{count=1, total=198ms, avg=198ms, min=198ms, max=198ms}
    readVoltageData: OperationStats{count=1, total=158ms, avg=158ms, min=158ms, max=158ms}
    calculateUserStats: OperationStats{count=1, total=3ms, avg=3ms, min=3ms, max=3ms}
    groupByUser: OperationStats{count=1, total=0ms, avg=0ms, min=0ms, max=0ms}
}
2025-06-25 09:28:38.020 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:28:38.020 - INFO - [VoltagePerformanceMonitor.java:100] : 性能统计数据已重置
2025-06-25 09:28:38.739 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:28:38.745 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:28:38.752 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:28:38.752 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:28:38.759 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:28:38.767 - INFO - [VoltageQualityReport.java:99] : 电压合格率报表生成完成: C:\Users\<USER>\AppData\Local\Temp\junit1941962485655466615\test_voltage_data.xlsx -> C:\Users\<USER>\AppData\Local\Temp\junit1941962485655466615\电压合格率报表.xlsx, 处理记录数: 3, 耗时: 8ms
2025-06-25 09:28:38.768 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:28:38.774 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:28:38.779 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:28:38.786 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:28:38.787 - INFO - [ExcelCacheManager.java:118] : 缓存已清空
2025-06-25 09:28:40.222 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-25 09:28:40.222 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-25 09:28:40.222 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *******************************
2025-06-25 09:28:40.223 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-06-25 09:28:40.224 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-06-25 09:28:40.225 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-25 09:28:40.227 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-25 09:28:40.228 -ERROR - [DatabaseInitializer.java:54] : 数据库初始化失败
java.lang.ClassNotFoundException: invalid.driver.Class
	at java.net.URLClassLoader.findClass(URLClassLoader.java:381)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:335)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:264)
	at com.cet.electric.ngapserver.config.DatabaseInitializer.run(DatabaseInitializer.java:40)
	at com.cet.electric.ngapserver.config.DatabaseInitializerTest.testRun_WhenDriverLoadFails_ShouldThrowException(DatabaseInitializerTest.java:137)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.internal.runners.statements.ExpectException.evaluate(ExpectException.java:19)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.mockito.internal.runners.DefaultInternalRunner$1.run(DefaultInternalRunner.java:78)
	at org.mockito.internal.runners.DefaultInternalRunner.run(DefaultInternalRunner.java:84)
	at org.mockito.internal.runners.StrictRunner.run(StrictRunner.java:39)
	at org.mockito.junit.MockitoJUnitRunner.run(MockitoJUnitRunner.java:161)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
	at org.junit.vintage.engine.execution.RunnerExecutor.execute(RunnerExecutor.java:43)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.Iterator.forEachRemaining(Iterator.java:116)
	at java.util.Spliterators$IteratorSpliterator.forEachRemaining(Spliterators.java:1801)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:481)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:471)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.junit.vintage.engine.VintageTestEngine.executeAllChildren(VintageTestEngine.java:82)
	at org.junit.vintage.engine.VintageTestEngine.execute(VintageTestEngine.java:73)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:248)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$5(DefaultLauncher.java:211)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:226)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:199)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:132)
	at com.intellij.junit5.JUnit5IdeaTestRunner.startRunnerWithArgs(JUnit5IdeaTestRunner.java:57)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
2025-06-25 09:28:40.230 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-25 09:28:40.230 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-25 09:28:40.230 - INFO - [DatabaseInitializer.java:45] : 数据库文件不存在，开始创建数据库: *******************************
2025-06-25 09:28:40.230 - INFO - [DatabaseInitializer.java:66] : 开始初始化数据库...
2025-06-25 09:28:40.231 - INFO - [DatabaseUtils.java:81] : 创建数据库目录: D:\CETWorkSpace\ngap-server\.\test_data - 成功: true
2025-06-25 09:28:40.231 - INFO - [DatabaseInitializer.java:75] : 数据库连接创建成功，开始执行建表脚本...
2025-06-25 09:28:40.231 - INFO - [DatabaseUtils.java:97] : 开始执行SQL脚本: db/init.sql
2025-06-25 09:28:41.324 - INFO - [DatabaseUtils.java:132] : SQL脚本执行完成: db/init.sql
2025-06-25 09:28:41.325 - INFO - [DatabaseInitializer.java:80] : 数据库初始化完成
2025-06-25 09:28:41.325 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-25 09:28:41.461 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-25 09:28:41.462 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-25 09:28:41.462 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *******************************
2025-06-25 09:28:41.462 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-06-25 09:28:41.463 - WARN - [DatabaseInitializer.java:103] : 缺少必要的表: simulation
2025-06-25 09:28:41.464 - WARN - [DatabaseInitializer.java:103] : 缺少必要的表: device
2025-06-25 09:28:41.464 - WARN - [DatabaseInitializer.java:109] : 数据库表结构不完整，重新执行初始化脚本...
2025-06-25 09:28:41.464 - INFO - [DatabaseUtils.java:97] : 开始执行SQL脚本: db/init.sql
2025-06-25 09:28:42.659 - INFO - [DatabaseUtils.java:132] : SQL脚本执行完成: db/init.sql
2025-06-25 09:28:42.660 - INFO - [DatabaseInitializer.java:111] : 数据库表结构修复完成
2025-06-25 09:28:42.660 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-25 09:28:42.679 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:42.679 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:42.679 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:42.680 - INFO - [ProjectController.java:38] : 接收到创建项目请求: projectName=测试项目, projectType=
2025-06-25 09:28:42.680 - INFO - [ProjectController.java:51] : 项目创建成功，ID: 1
2025-06-25 09:28:42.685 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:42.685 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:42.685 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:42.685 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-25 09:28:42.685 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-25 09:28:42.690 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:42.690 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:42.690 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:42.691 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=10, sortBy=created_at, sortOrder=desc, projectType=null, keyword=null, startTime=null, endTime=null)
2025-06-25 09:28:42.691 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 0
2025-06-25 09:28:42.695 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:42.695 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:42.695 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:42.695 - INFO - [ProjectController.java:134] : 接收到导出项目配置文件请求: projectId=1
2025-06-25 09:28:42.699 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:42.699 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:42.699 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:42.699 - INFO - [ProjectController.java:38] : 接收到创建项目请求: projectName=  测试项目  , projectType=  General_scenario  
2025-06-25 09:28:42.699 - INFO - [ProjectController.java:51] : 项目创建成功，ID: 1
2025-06-25 09:28:42.704 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:42.704 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:42.704 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:42.705 - INFO - [ProjectController.java:142] : 接收到导入项目配置文件请求，文件名：project.json, 文件大小：66
2025-06-25 09:28:42.705 - INFO - [ProjectController.java:146] : 项目配置文件导入成功，项目ID：1
2025-06-25 09:28:42.709 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:42.709 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:42.709 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:42.710 - INFO - [ProjectController.java:119] : 接收到删除项目请求: projectId=1
2025-06-25 09:28:42.710 - INFO - [ProjectController.java:124] : 项目删除成功，项目ID: 1
2025-06-25 09:28:42.714 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:42.714 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:42.714 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:42.715 - INFO - [ProjectController.java:38] : 接收到创建项目请求: projectName=测试项目, projectType=General_scenario
2025-06-25 09:28:42.715 - INFO - [ProjectController.java:51] : 项目创建成功，ID: 1
2025-06-25 09:28:42.719 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:42.719 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:42.720 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:42.720 - INFO - [ProjectController.java:106] : 接收到重命名项目请求: projectId=1, newName=新项目名称
2025-06-25 09:28:42.720 - INFO - [ProjectController.java:111] : 项目重命名成功，项目ID: 1, 新名称: 新项目名称
2025-06-25 09:28:42.724 - INFO - [MockServletContext.java:455] : Initializing Spring TestDispatcherServlet ''
2025-06-25 09:28:42.724 - INFO - [FrameworkServlet.java:525] : Initializing Servlet ''
2025-06-25 09:28:42.724 - INFO - [FrameworkServlet.java:547] : Completed initialization in 0 ms
2025-06-25 09:28:42.725 - INFO - [ProjectController.java:91] : 接收到复制项目请求: projectId=1
2025-06-25 09:28:42.725 - INFO - [ProjectController.java:96] : 项目复制成功，原项目ID: 1, 新项目ID: 2
2025-06-25 09:28:43.129 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-25 09:28:43.129 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-25 09:28:43.130 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:8080, DSS文件路径: /test/path/test.dss
2025-06-25 09:28:43.130 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 200 OK
2025-06-25 09:28:43.130 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-25 09:28:43.131 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:8080, DSS文件路径: /test/path/test.dss
2025-06-25 09:28:43.131 - WARN - [DssUtils.java:74] : DSS服务返回空的错误响应，已创建默认错误信息
2025-06-25 09:28:43.131 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 500 INTERNAL_SERVER_ERROR
2025-06-25 09:28:43.131 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-25 09:28:43.131 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:8080, DSS文件路径: /test/path/test.dss
2025-06-25 09:28:43.131 - INFO - [DssUtils.java:88] : 为DSS错误响应添加output_data字段
2025-06-25 09:28:43.131 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 500 INTERNAL_SERVER_ERROR
2025-06-25 09:28:43.131 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-25 09:28:43.132 - INFO - [DssUtils.java:32] : 开始调用DSS服务，URL: http://localhost:8080, DSS文件路径: /test/path/test.dss
2025-06-25 09:28:43.132 - INFO - [DssUtils.java:88] : 为DSS错误响应添加output_data字段
2025-06-25 09:28:43.132 - INFO - [DssUtils.java:55] : DSS服务调用完成，状态码: 400 BAD_REQUEST
2025-06-25 09:28:44.689 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-06-25 09:28:44.689 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-25 09:28:44.692 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-06-25 09:28:44.694 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-06-25 09:41:02.462 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 09:41:02.463 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 09:41:02.465 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 09:41:02.465 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 09:41:02.467 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 09:41:02.467 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 09:41:45.979 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 09:41:45.980 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 09:41:45.981 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 09:41:45.981 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 09:41:45.982 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 09:41:45.982 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 09:42:53.958 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 09:42:53.959 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 09:42:53.960 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 09:42:53.960 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 09:42:53.961 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 09:42:53.961 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 09:44:28.430 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 09:44:28.431 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 09:44:28.432 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 09:44:28.432 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 09:44:28.432 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 09:44:28.433 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 09:45:44.091 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 09:45:44.091 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 09:45:44.092 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 09:45:44.092 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 09:45:44.092 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 09:45:44.092 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 09:47:59.709 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=2
2025-06-25 09:47:59.709 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 2
2025-06-25 09:47:59.850 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-25 09:47:59.941 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script, 共 8 个子指标
2025-06-25 09:47:59.948 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/11/2/measured_data/太坪03柱上变压器.xlsx
2025-06-25 09:47:59.986 - INFO - [ExcelCacheManager.java:51] : Excel缓存管理器初始化完成，过期时间: 1800000ms
2025-06-25 09:47:59.988 - INFO - [VoltageObjectPool.java:46] : 电压对象池初始化完成
2025-06-25 09:47:59.989 - INFO - [VoltagePerformanceMonitor.java:28] : 电压性能监控器初始化完成
2025-06-25 09:48:01.380 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 太坪03柱上变压器.xlsx 的电压指标，共 1 个指标
2025-06-25 09:48:01.381 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-25 09:48:01.381 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 2, 指标数量: 2
2025-06-25 09:49:39.020 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=2
2025-06-25 09:49:39.021 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 2
2025-06-25 09:49:39.023 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-25 09:49:39.024 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script, 共 8 个子指标
2025-06-25 09:49:39.025 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/11/2/measured_data/太坪03柱上变压器.xlsx
2025-06-25 09:49:39.160 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 太坪03柱上变压器.xlsx 的电压指标，共 1 个指标
2025-06-25 09:49:39.161 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-25 09:49:39.161 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 2, 指标数量: 2
2025-06-25 09:50:22.536 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=2
2025-06-25 09:50:22.537 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 2
2025-06-25 09:50:22.538 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-25 09:50:22.539 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script, 共 8 个子指标
2025-06-25 09:50:22.539 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/11/2/measured_data/太坪03柱上变压器.xlsx
2025-06-25 09:50:22.635 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 太坪03柱上变压器.xlsx 的电压指标，共 1 个指标
2025-06-25 09:50:22.635 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-25 09:50:22.635 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 2, 指标数量: 2
2025-06-25 09:51:19.964 - INFO - [LineCodeController.java:97] : 接收到查询线路代码请求: lineCodeId=5
2025-06-25 09:51:19.965 -ERROR - [LineCodeServiceImpl.java:108] : 查询线路代码失败：指定ID的线路代码不存在: 5
2025-06-25 09:51:19.966 -ERROR - [GlobalExceptionHandler.java:22] : 查询线路代码失败：指定ID的线路代码不存在.
com.cet.electric.ngapserver.service.impl.LineCodeServiceImpl.getLineCodeById(LineCodeServiceImpl.java:109)
com.cet.electric.ngapserver.web.controller.LineCodeController.getLineCodeById(LineCodeController.java:99)
java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
java.base/java.lang.reflect.Method.invoke(Method.java:580)
org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-25 09:51:22.092 - INFO - [LineCodeController.java:97] : 接收到查询线路代码请求: lineCodeId=2
2025-06-25 09:51:22.093 -ERROR - [LineCodeServiceImpl.java:108] : 查询线路代码失败：指定ID的线路代码不存在: 2
2025-06-25 09:51:22.093 -ERROR - [GlobalExceptionHandler.java:22] : 查询线路代码失败：指定ID的线路代码不存在.
com.cet.electric.ngapserver.service.impl.LineCodeServiceImpl.getLineCodeById(LineCodeServiceImpl.java:109)
com.cet.electric.ngapserver.web.controller.LineCodeController.getLineCodeById(LineCodeController.java:99)
java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
java.base/java.lang.reflect.Method.invoke(Method.java:580)
org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-25 09:51:23.368 - INFO - [LineCodeController.java:97] : 接收到查询线路代码请求: lineCodeId=3
2025-06-25 09:51:23.369 -ERROR - [LineCodeServiceImpl.java:108] : 查询线路代码失败：指定ID的线路代码不存在: 3
2025-06-25 09:51:23.369 -ERROR - [GlobalExceptionHandler.java:22] : 查询线路代码失败：指定ID的线路代码不存在.
com.cet.electric.ngapserver.service.impl.LineCodeServiceImpl.getLineCodeById(LineCodeServiceImpl.java:109)
com.cet.electric.ngapserver.web.controller.LineCodeController.getLineCodeById(LineCodeController.java:99)
java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
java.base/java.lang.reflect.Method.invoke(Method.java:580)
org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-25 09:58:15.373 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 09:58:15.383 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 09:58:15.385 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 09:58:15.385 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 09:58:15.385 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 09:58:15.386 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 09:58:38.755 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 09:58:38.756 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 09:58:38.757 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 09:58:38.758 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 09:58:38.759 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 09:58:38.759 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:03:06.901 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:03:06.902 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:03:06.905 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:03:06.906 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:03:06.906 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:03:06.906 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:03:47.879 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:03:47.880 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:03:47.882 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:03:47.882 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:03:47.883 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:03:47.883 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:05:11.624 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:05:11.624 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:05:11.626 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:05:11.626 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:05:11.627 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:05:11.627 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:08:19.225 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=2
2025-06-25 10:08:19.239 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 2
2025-06-25 10:08:19.240 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-25 10:08:19.297 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script, 共 8 个子指标
2025-06-25 10:08:19.297 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/11/2/measured_data/太坪03柱上变压器.xlsx
2025-06-25 10:08:19.495 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 太坪03柱上变压器.xlsx 的电压指标，共 1 个指标
2025-06-25 10:08:19.495 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-25 10:08:19.496 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 2, 指标数量: 2
2025-06-25 10:08:19.523 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=2
2025-06-25 10:08:19.523 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 2
2025-06-25 10:08:19.524 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-25 10:08:19.525 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script, 共 8 个子指标
2025-06-25 10:08:19.525 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/11/2/measured_data/太坪03柱上变压器.xlsx
2025-06-25 10:08:19.625 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 太坪03柱上变压器.xlsx 的电压指标，共 1 个指标
2025-06-25 10:08:19.625 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-25 10:08:19.626 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 2, 指标数量: 2
2025-06-25 10:08:19.776 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=2
2025-06-25 10:08:19.776 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 2
2025-06-25 10:08:19.777 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-25 10:08:19.778 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script, 共 8 个子指标
2025-06-25 10:08:19.778 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/11/2/measured_data/太坪03柱上变压器.xlsx
2025-06-25 10:08:19.867 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 太坪03柱上变压器.xlsx 的电压指标，共 1 个指标
2025-06-25 10:08:19.868 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-25 10:08:19.868 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 2, 指标数量: 2
2025-06-25 10:08:20.031 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=2
2025-06-25 10:08:20.032 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 2
2025-06-25 10:08:20.032 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-25 10:08:20.033 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script, 共 8 个子指标
2025-06-25 10:08:20.033 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/11/2/measured_data/太坪03柱上变压器.xlsx
2025-06-25 10:08:20.097 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 太坪03柱上变压器.xlsx 的电压指标，共 1 个指标
2025-06-25 10:08:20.098 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-25 10:08:20.098 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 2, 指标数量: 2
2025-06-25 10:08:20.246 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=2
2025-06-25 10:08:20.246 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 2
2025-06-25 10:08:20.247 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-25 10:08:20.249 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script, 共 8 个子指标
2025-06-25 10:08:20.249 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/11/2/measured_data/太坪03柱上变压器.xlsx
2025-06-25 10:08:20.326 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 太坪03柱上变压器.xlsx 的电压指标，共 1 个指标
2025-06-25 10:08:20.327 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-25 10:08:20.327 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 2, 指标数量: 2
2025-06-25 10:08:53.668 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:08:53.668 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:08:53.669 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:08:53.669 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:08:53.670 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:08:53.670 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:09:37.701 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:09:37.702 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:09:37.702 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:09:37.703 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:09:37.703 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:09:37.703 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:09:39.155 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:09:39.155 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:09:39.156 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:09:39.156 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:09:39.157 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:09:39.157 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:10:13.581 - INFO - [LineCodeController.java:97] : 接收到查询线路代码请求: lineCodeId=17
2025-06-25 10:10:13.583 - INFO - [LineCodeServiceImpl.java:112] : 查询线路代码成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-25 10:10:13.583 - INFO - [LineCodeController.java:101] : 线路代码查询成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-25 10:10:24.734 - INFO - [LineCodeController.java:97] : 接收到查询线路代码请求: lineCodeId=17
2025-06-25 10:10:24.734 - INFO - [LineCodeServiceImpl.java:112] : 查询线路代码成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-25 10:10:24.734 - INFO - [LineCodeController.java:101] : 线路代码查询成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-25 10:10:24.740 - INFO - [LineCodeController.java:97] : 接收到查询线路代码请求: lineCodeId=17
2025-06-25 10:10:24.741 - INFO - [LineCodeServiceImpl.java:112] : 查询线路代码成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-25 10:10:24.741 - INFO - [LineCodeController.java:101] : 线路代码查询成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-25 10:10:24.906 - INFO - [LineCodeController.java:97] : 接收到查询线路代码请求: lineCodeId=17
2025-06-25 10:10:24.906 - INFO - [LineCodeServiceImpl.java:112] : 查询线路代码成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-25 10:10:24.907 - INFO - [LineCodeController.java:101] : 线路代码查询成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-25 10:10:25.082 - INFO - [LineCodeController.java:97] : 接收到查询线路代码请求: lineCodeId=17
2025-06-25 10:10:25.082 - INFO - [LineCodeServiceImpl.java:112] : 查询线路代码成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-25 10:10:25.083 - INFO - [LineCodeController.java:101] : 线路代码查询成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-25 10:10:25.237 - INFO - [LineCodeController.java:97] : 接收到查询线路代码请求: lineCodeId=17
2025-06-25 10:10:25.238 - INFO - [LineCodeServiceImpl.java:112] : 查询线路代码成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-25 10:10:25.238 - INFO - [LineCodeController.java:101] : 线路代码查询成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-25 10:10:32.496 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:10:32.496 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:10:32.497 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:10:32.498 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:10:32.498 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:10:32.498 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:11:24.724 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:11:24.725 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:11:24.726 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:11:24.726 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:11:24.727 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:11:24.727 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:11:25.376 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:11:25.377 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:11:25.378 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:11:25.379 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:11:25.379 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:11:25.380 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:12:03.663 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:12:03.664 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:12:03.665 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:12:03.666 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:12:03.667 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:12:03.667 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:12:53.076 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:12:53.077 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:12:53.078 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:12:53.078 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:12:53.079 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:12:53.079 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:12:53.637 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:12:53.637 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:12:53.638 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:12:53.638 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:12:53.638 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:12:53.639 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:13:51.658 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:13:51.659 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:13:51.660 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:13:51.661 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:13:51.662 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:13:51.662 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:14:39.126 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:14:39.126 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:14:39.128 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:14:39.128 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:14:39.128 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:14:39.128 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:16:10.986 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:16:10.987 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:16:10.988 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:16:10.988 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:16:10.989 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:16:10.989 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:22:08.892 - INFO - [LineCodeController.java:97] : 接收到查询线路代码请求: lineCodeId=17
2025-06-25 10:22:08.893 - INFO - [LineCodeServiceImpl.java:112] : 查询线路代码成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-25 10:22:08.894 - INFO - [LineCodeController.java:101] : 线路代码查询成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-25 10:24:59.271 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:24:59.272 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:24:59.274 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:24:59.274 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:24:59.275 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:24:59.276 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:25:59.571 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:25:59.572 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:25:59.572 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:25:59.573 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:25:59.573 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:25:59.573 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:28:47.576 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:28:47.576 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:28:47.577 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:28:47.577 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:28:47.577 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:28:47.578 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:29:44.204 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:29:44.205 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:29:44.206 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:29:44.207 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:29:44.208 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:29:44.208 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:30:45.342 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:30:45.342 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:30:45.343 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:30:45.343 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:30:45.343 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:30:45.343 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:33:59.420 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:33:59.421 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:33:59.423 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:33:59.425 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:33:59.426 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:33:59.426 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:34:49.997 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:34:49.997 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:34:49.997 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:34:49.998 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:34:49.998 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:34:49.998 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:37:13.166 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:37:13.166 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:37:13.167 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:37:13.167 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:37:13.167 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:37:13.168 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:37:43.224 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:37:43.224 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:37:43.225 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:37:43.225 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:37:43.225 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:37:43.225 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:38:27.379 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:38:27.380 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:38:27.380 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:38:27.380 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:38:27.381 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:38:27.381 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:39:01.008 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:39:01.008 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:39:01.009 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:39:01.009 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:39:01.010 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:39:01.010 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:39:24.554 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:39:24.554 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:39:24.555 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:39:24.555 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:39:24.555 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:39:24.555 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:41:57.809 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:41:57.810 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:41:57.812 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:41:57.813 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:41:57.813 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:41:57.814 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:42:05.051 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:42:05.052 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:42:05.054 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:42:05.056 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:42:05.057 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:42:05.057 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:43:25.871 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:43:25.872 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:43:25.872 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:43:25.873 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:43:25.873 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:43:25.873 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:43:41.018 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:43:41.018 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:43:41.020 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:43:41.020 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:43:41.020 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:43:41.020 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:45:56.460 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:45:56.460 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:45:56.461 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:45:56.461 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:45:56.461 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:45:56.461 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:47:13.470 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:47:13.470 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:47:13.471 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:47:13.471 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:47:13.471 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:47:13.471 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:48:13.687 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:48:13.687 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:48:13.688 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:48:13.688 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:48:13.688 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:48:13.688 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:50:15.409 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:50:15.410 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:50:15.410 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:50:15.411 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:50:15.411 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:50:15.411 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:52:04.667 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:52:04.668 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:52:04.670 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:52:04.671 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:52:04.672 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:52:04.672 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:52:16.806 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:52:16.806 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:52:16.807 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:52:16.807 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:52:16.807 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:52:16.807 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:53:23.244 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:53:23.245 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:53:23.246 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:53:23.246 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:53:23.246 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:53:23.246 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:54:04.677 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:54:04.677 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:54:04.680 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:54:04.680 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:54:04.681 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:54:04.681 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:56:06.921 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:56:06.921 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:56:06.922 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:56:06.922 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:56:06.922 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:56:06.923 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:58:26.179 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:58:26.180 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:58:26.181 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:58:26.181 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:58:26.181 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:58:26.181 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 10:59:15.311 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 10:59:15.312 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 10:59:15.313 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 10:59:15.313 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 10:59:15.313 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 10:59:15.313 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:00:31.953 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:00:31.954 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:00:31.956 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:00:31.956 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:00:31.956 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:00:31.957 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:03:11.763 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:03:11.763 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:03:11.766 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:03:11.766 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:03:11.766 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:03:11.766 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:04:57.320 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:04:57.321 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:04:57.322 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:04:57.322 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:04:57.322 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:04:57.322 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:05:02.383 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:05:02.383 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:05:02.384 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:05:02.384 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:05:02.384 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:05:02.384 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:05:15.336 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:05:15.337 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:05:15.338 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:05:15.338 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:05:15.338 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:05:15.339 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:24:48.384 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:24:48.385 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:24:48.386 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:24:48.386 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:24:48.387 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:24:48.387 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:26:14.308 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:26:14.309 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:26:14.311 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:26:14.311 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:26:14.313 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:26:14.314 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:26:24.110 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:26:24.111 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:26:24.113 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:26:24.114 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:26:24.114 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:26:24.115 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:26:31.745 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:26:31.745 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:26:31.746 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:26:31.746 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:26:31.747 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:26:31.747 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:27:34.702 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:27:34.703 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:27:34.705 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:27:34.705 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:27:34.706 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:27:34.706 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:27:38.960 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:27:38.961 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:27:38.962 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:27:38.963 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:27:38.964 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:27:38.964 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:30:11.343 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:30:11.343 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:30:11.344 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:30:11.345 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:30:11.345 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:30:11.345 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:30:25.398 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:30:25.399 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:30:25.400 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:30:25.401 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:30:25.401 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:30:25.401 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:30:31.943 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:30:31.943 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:30:31.943 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:30:31.944 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:30:31.944 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:30:31.944 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:36:12.857 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:36:12.858 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:36:12.861 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:36:12.861 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:36:12.862 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:36:12.862 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:37:08.548 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:37:08.563 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:37:08.565 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:37:08.566 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:37:08.567 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:37:08.568 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:37:36.436 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:37:36.437 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:37:36.439 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:37:36.439 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:37:36.441 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:37:36.442 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:37:42.508 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:37:42.508 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:37:42.510 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:37:42.511 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:37:42.512 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:37:42.513 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:38:00.849 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:38:00.850 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:38:00.852 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:38:00.852 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:38:00.853 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:38:00.853 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:38:29.061 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:38:29.061 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:38:29.061 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:38:29.062 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:38:29.062 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:38:29.062 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:39:28.942 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:39:28.943 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:39:28.944 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:39:28.945 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:39:28.945 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:39:28.946 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:39:41.903 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:39:41.904 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:39:41.905 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:39:41.905 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:39:41.905 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:39:41.905 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:39:48.982 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:39:48.982 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:39:48.982 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:39:48.983 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:39:48.983 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:39:48.983 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:41:03.061 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:41:03.062 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:41:03.063 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:41:03.064 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:41:03.064 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:41:03.065 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:41:43.594 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:41:43.594 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:41:43.595 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:41:43.595 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:41:43.596 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:41:43.596 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:41:49.193 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:41:49.194 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:41:49.195 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:41:49.196 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:41:49.197 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:41:49.198 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:43:19.632 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:43:19.633 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:43:19.634 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:43:19.634 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:43:19.634 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:43:19.635 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:43:24.536 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:43:24.537 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:43:24.539 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:43:24.539 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:43:24.540 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:43:24.540 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:43:32.724 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:43:32.725 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:43:32.727 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:43:32.727 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:43:32.728 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:43:32.729 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:44:55.598 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:44:55.599 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:44:55.602 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:44:55.603 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:44:55.603 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:44:55.604 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:45:01.399 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:45:01.400 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:45:01.401 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:45:01.402 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:45:01.403 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:45:01.403 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:45:32.635 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:45:32.636 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:45:32.638 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:45:32.639 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:45:32.640 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:45:32.640 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:47:25.492 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:47:25.493 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:47:25.496 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:47:25.496 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:47:25.497 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:47:25.497 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:48:19.565 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:48:19.566 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:48:19.566 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:48:19.567 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:48:19.567 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:48:19.567 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:49:30.317 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:49:30.318 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:49:30.319 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:49:30.320 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:49:30.320 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:49:30.321 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:49:58.194 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:49:58.194 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:49:58.194 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:49:58.194 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:49:58.196 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:49:58.196 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:50:26.676 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:50:26.676 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:50:26.678 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:50:26.679 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:50:26.680 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:50:26.681 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:50:35.652 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:50:35.653 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:50:35.654 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:50:35.655 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:50:35.655 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:50:35.656 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:50:42.867 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:50:42.867 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:50:42.868 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:50:42.868 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:50:42.868 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:50:42.868 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:51:53.030 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:51:53.031 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:51:53.032 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:51:53.033 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:51:53.034 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:51:53.035 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:52:02.979 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:52:02.980 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:52:02.982 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:52:02.982 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:52:02.983 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:52:02.984 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:52:45.344 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:52:45.345 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:52:45.346 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:52:45.346 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:52:45.347 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:52:45.348 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:52:51.379 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:52:51.380 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:52:51.381 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:52:51.382 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:52:51.384 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:52:51.384 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:53:19.127 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:53:19.128 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:53:19.130 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:53:19.131 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:53:19.132 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:53:19.132 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:53:24.928 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:53:24.928 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:53:24.930 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:53:24.931 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:53:24.931 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:53:24.932 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:54:01.664 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:54:01.664 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:54:01.666 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:54:01.667 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:54:01.669 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:54:01.669 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 11:57:57.788 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 11:57:57.788 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 11:57:57.789 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 11:57:57.789 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 11:57:57.789 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 11:57:57.789 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 15:00:17.873 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 15:00:17.878 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 15:00:17.884 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 15:00:17.885 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 15:00:17.885 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 15:00:17.885 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 15:00:21.613 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 15:00:21.614 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 15:00:21.615 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 15:00:21.615 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 15:00:21.616 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 15:00:21.616 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 15:00:23.710 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 15:00:23.711 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 15:00:23.712 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 15:00:23.712 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 15:00:23.712 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 15:00:23.712 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 15:00:29.707 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-25 15:00:29.707 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-25 15:00:29.708 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-25 15:00:29.708 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-25 15:00:29.708 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-25 15:00:29.708 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-25 15:30:08.993 - INFO - [TaskSchedule.java:38] : 开始删除
2025-06-25 15:30:08.994 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-06-25 15:30:09.004 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-06-25 15:30:09.005 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-06-25 15:30:09.022 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-06-25 15:30:09.022 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-06-25 15:46:43.048 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-25 15:46:43.049 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-25 15:46:43.049 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-25 15:46:43.049 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-25 15:46:43.050 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-25 15:46:43.050 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-25 15:46:43.056 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-25 15:46:43.056 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-25 15:46:43.057 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-25 15:46:43.057 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-25 15:50:40.724 - INFO - [ProjectController.java:38] : 接收到创建项目请求: projectName=aaa, projectType=General_scenario
2025-06-25 15:50:41.220 - INFO - [ProjectServiceImpl.java:80] : 项目创建成功: Project(projectId=27, projectName=aaa, projectType=General_scenario, createdAt=1750837840724, updatedAt=1750837840724, isDeleted=0)
2025-06-25 15:50:41.221 - INFO - [ProjectController.java:51] : 项目创建成功，ID: 27
2025-06-25 15:50:41.235 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-25 15:50:41.235 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-25 15:50:41.236 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 11 条数据
2025-06-25 15:50:41.236 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-25 15:50:41.236 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 11 个项目
2025-06-25 15:50:41.236 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 11
2025-06-25 15:50:48.802 - INFO - [ProjectController.java:119] : 接收到删除项目请求: projectId=27
2025-06-25 15:50:48.802 - INFO - [ProjectServiceImpl.java:383] : 开始删除项目, 项目ID: 27
2025-06-25 15:50:48.922 - INFO - [ProjectServiceImpl.java:418] : 项目删除成功: 项目ID: 27
2025-06-25 15:50:48.922 - INFO - [ProjectController.java:124] : 项目删除成功，项目ID: 27
2025-06-25 15:50:48.932 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-25 15:50:48.932 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-25 15:50:48.933 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-25 15:50:48.933 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-25 15:50:48.934 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-25 15:50:48.934 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-25 15:52:22.784 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=测试, startTime=1735660800000, endTime=1767196800000)
2025-06-25 15:52:22.785 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 测试
2025-06-25 15:52:22.786 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 4 条数据
2025-06-25 15:52:22.786 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: 测试, startTime: 1735660800000, endTime: 1767196800000
2025-06-25 15:52:22.786 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 4 个项目
2025-06-25 15:52:22.786 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 4
2025-06-25 15:58:20.706 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=25, keyword=, startTime=null, endTime=null)
2025-06-25 15:58:20.707 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 25, keyword: , startTime: null, endTime: null
2025-06-25 15:58:20.708 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-25 15:58:20.709 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-25 15:59:00.949 - INFO - [SimulationController.java:60] : 接收到创建仿真任务请求: projectId=25, simulationName=AAA
2025-06-25 15:59:01.475 - INFO - [SimulationServiceImpl.java:218] : 仿真任务创建成功: Simulation(simulationId=31, projectId=25, projectName=调压器模型测试, simulationName=AAA, simulationModel=null, simulationDraft=null, inputData=null, outputData=null, measuredData=null, simulationScript=null, nodes=null, controlStrategy=null, runStatus=READY, createdAt=1750838340950, updatedAt=1750838340950, isDeleted=0)
2025-06-25 15:59:01.476 - INFO - [SimulationServiceImpl.java:228] : 文件夹创建成功: D:\CETWorkSpace\ngap-server/data/25/31
2025-06-25 15:59:01.476 - INFO - [SimulationController.java:73] : 仿真任务创建成功，ID: 31
2025-06-25 15:59:01.490 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=25, keyword=, startTime=null, endTime=null)
2025-06-25 15:59:01.490 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 25, keyword: , startTime: null, endTime: null
2025-06-25 15:59:01.491 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 2 条数据
2025-06-25 15:59:01.492 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 2
2025-06-25 16:02:28.814 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=asc, projectId=25, keyword=, startTime=null, endTime=null)
2025-06-25 16:02:28.814 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: asc, projectId: 25, keyword: , startTime: null, endTime: null
2025-06-25 16:02:28.815 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 2 条数据
2025-06-25 16:02:28.816 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 2
2025-06-25 16:02:51.470 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=25, keyword=a, startTime=null, endTime=null)
2025-06-25 16:02:51.470 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 25, keyword: a, startTime: null, endTime: null
2025-06-25 16:02:51.471 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 2 条数据
2025-06-25 16:02:51.471 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 2
2025-06-25 16:02:54.748 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=25, keyword=A, startTime=null, endTime=null)
2025-06-25 16:02:54.749 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 25, keyword: A, startTime: null, endTime: null
2025-06-25 16:02:54.750 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 2 条数据
2025-06-25 16:02:54.750 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 2
2025-06-25 16:02:56.595 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=updated_at, sortOrder=asc, projectId=25, keyword=A, startTime=null, endTime=null)
2025-06-25 16:02:56.596 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: updated_at, sortOrder: asc, projectId: 25, keyword: A, startTime: null, endTime: null
2025-06-25 16:02:56.597 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 2 条数据
2025-06-25 16:02:56.597 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 2
2025-06-25 16:02:58.448 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=updated_at, sortOrder=desc, projectId=25, keyword=A, startTime=null, endTime=null)
2025-06-25 16:02:58.448 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: updated_at, sortOrder: desc, projectId: 25, keyword: A, startTime: null, endTime: null
2025-06-25 16:02:58.449 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 2 条数据
2025-06-25 16:02:58.449 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 2
2025-06-25 16:03:38.896 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-25 16:03:38.896 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-25 16:03:38.897 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-25 16:03:38.897 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-25 16:03:38.897 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-25 16:03:38.897 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-25 16:03:40.155 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=21, keyword=, startTime=null, endTime=null)
2025-06-25 16:03:40.155 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 21, keyword: , startTime: null, endTime: null
2025-06-25 16:03:40.276 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 6 条数据
2025-06-25 16:03:40.277 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 6
2025-06-25 16:08:14.377 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=24
2025-06-25 16:08:14.382 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=24, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=1, pageSize=50)
2025-06-25 16:08:14.539 - INFO - [SimulationController.java:294] : 获取台区电压统计报表成功，台区ID: 24, 用户总数: 214, 合格率: 85.25734986694737
2025-06-25 16:08:14.571 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 24, 用户数量: 214
2025-06-25 16:10:59.376 - INFO - [SimulationController.java:195] : 接收到导出仿真任务请求: simulationId=24
2025-06-25 16:10:59.377 - INFO - [SimulationServiceImpl.java:953] : 开始导出仿真配置文件，仿真ID: 24
2025-06-25 16:10:59.406 - INFO - [SimulationServiceImpl.java:962] : 临时JSON文件已创建: C:\Users\<USER>\AppData\Local\Temp\simulation_config_6118002898863007352.json
2025-06-25 16:10:59.440 - INFO - [SimulationServiceImpl.java:967] : 成功生成仿真配置JSON内容
2025-06-25 16:10:59.446 - INFO - [SimulationServiceImpl.java:971] : 成功将配置数据写入临时文件: C:\Users\<USER>\AppData\Local\Temp\simulation_config_6118002898863007352.json
2025-06-25 16:10:59.446 - INFO - [SimulationServiceImpl.java:985] : 找到仿真数据文件夹: D:\CETWorkSpace\ngap-server/data/21/24
2025-06-25 16:10:59.447 - INFO - [SimulationServiceImpl.java:989] : 临时ZIP文件已创建: C:\Users\<USER>\AppData\Local\Temp\simulation_export_8447402212693170877.zip
2025-06-25 16:10:59.477 - INFO - [SimulationServiceImpl.java:1005] : 成功将配置JSON文件添加到ZIP
2025-06-25 16:11:04.045 - INFO - [SimulationServiceImpl.java:1010] : 成功将仿真数据文件夹添加到ZIP
2025-06-25 16:11:04.140 - INFO - [SimulationServiceImpl.java:1019] : 仿真配置和数据已成功打包并准备下载: C:\Users\<USER>\AppData\Local\Temp\simulation_export_8447402212693170877.zip
2025-06-25 16:11:04.141 - INFO - [SimulationServiceImpl.java:1027] : 临时JSON文件已成功删除: C:\Users\<USER>\AppData\Local\Temp\simulation_config_6118002898863007352.json
2025-06-25 16:11:04.142 - INFO - [SimulationServiceImpl.java:1035] : 临时ZIP文件已成功删除: C:\Users\<USER>\AppData\Local\Temp\simulation_export_8447402212693170877.zip
2025-06-25 16:11:20.553 - INFO - [SimulationController.java:195] : 接收到导出仿真任务请求: simulationId=24
2025-06-25 16:11:20.553 - INFO - [SimulationServiceImpl.java:953] : 开始导出仿真配置文件，仿真ID: 24
2025-06-25 16:11:20.557 - INFO - [SimulationServiceImpl.java:962] : 临时JSON文件已创建: C:\Users\<USER>\AppData\Local\Temp\simulation_config_14168530686891846376.json
2025-06-25 16:11:20.570 - INFO - [SimulationServiceImpl.java:967] : 成功生成仿真配置JSON内容
2025-06-25 16:11:20.575 - INFO - [SimulationServiceImpl.java:971] : 成功将配置数据写入临时文件: C:\Users\<USER>\AppData\Local\Temp\simulation_config_14168530686891846376.json
2025-06-25 16:11:20.575 - INFO - [SimulationServiceImpl.java:985] : 找到仿真数据文件夹: D:\CETWorkSpace\ngap-server/data/21/24
2025-06-25 16:11:20.575 - INFO - [SimulationServiceImpl.java:989] : 临时ZIP文件已创建: C:\Users\<USER>\AppData\Local\Temp\simulation_export_4249746830666343954.zip
2025-06-25 16:11:20.600 - INFO - [SimulationServiceImpl.java:1005] : 成功将配置JSON文件添加到ZIP
2025-06-25 16:11:21.011 - INFO - [SimulationServiceImpl.java:1010] : 成功将仿真数据文件夹添加到ZIP
2025-06-25 16:11:21.096 - INFO - [SimulationServiceImpl.java:1019] : 仿真配置和数据已成功打包并准备下载: C:\Users\<USER>\AppData\Local\Temp\simulation_export_4249746830666343954.zip
2025-06-25 16:11:21.097 - INFO - [SimulationServiceImpl.java:1027] : 临时JSON文件已成功删除: C:\Users\<USER>\AppData\Local\Temp\simulation_config_14168530686891846376.json
2025-06-25 16:11:21.098 - INFO - [SimulationServiceImpl.java:1035] : 临时ZIP文件已成功删除: C:\Users\<USER>\AppData\Local\Temp\simulation_export_4249746830666343954.zip
2025-06-25 16:22:53.125 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=24
2025-06-25 16:22:53.125 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=24, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=1, pageSize=50)
2025-06-25 16:22:53.192 - INFO - [SimulationController.java:294] : 获取台区电压统计报表成功，台区ID: 24, 用户总数: 214, 合格率: 85.25734986694737
2025-06-25 16:22:53.220 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 24, 用户数量: 214
2025-06-25 16:25:29.057 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=24
2025-06-25 16:25:29.057 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 24
2025-06-25 16:25:29.059 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script
2025-06-25 16:25:30.784 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script, 共 216 个子指标
2025-06-25 16:25:30.784 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/21/24/measured_data/工作簿1.xlsx
2025-06-25 16:25:30.786 - INFO - [ExcelUtils.java:50] : 检测到大文件(5.55MB)，使用优化处理器
2025-06-25 16:25:33.346 - INFO - [OptimizedExcelProcessor.java:94] : 指标提取完成: 文件=D:\CETWorkSpace\ngap-server/data/21/24/measured_data/工作簿1.xlsx, 耗时=2556ms, 指标数=956
2025-06-25 16:25:33.349 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 工作簿1.xlsx 的电压指标，共 1 个指标
2025-06-25 16:25:33.350 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-25 16:25:33.350 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 24, 指标数量: 2
2025-06-25 16:25:33.444 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=24
2025-06-25 16:25:33.444 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 24
2025-06-25 16:25:33.446 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script
2025-06-25 16:25:33.446 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script
2025-06-25 16:25:33.447 - INFO - [CsvUtils.java:375] : 找到 216 个CSV文件，开始处理
2025-06-25 16:25:33.567 - INFO - [CsvUtils.java:389] : 完成电压数据提取，处理文件数: 216/216, 总数据点: 23424, 合格数据点: 23161, 合格率: 98.88%
2025-06-25 16:25:33.567 - INFO - [SimulationServiceImpl.java:1914] : 仿真任务 24 电压合格率计算完成：总数据点=23424, 合格数据点=23161, 合格率=98.88%, 处理时间=121ms
2025-06-25 16:25:33.568 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 24, 总数据点: 23424, 合格数据点: 23161, 合格率: 98.88%
2025-06-25 16:26:16.381 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=18
2025-06-25 16:26:16.381 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 18
2025-06-25 16:26:16.532 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script
2025-06-25 16:26:18.297 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script, 共 214 个子指标
2025-06-25 16:26:18.300 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/21/18/measured_data/五天实测数据.xlsx
2025-06-25 16:26:18.636 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 五天实测数据.xlsx 的电压指标，共 1 个指标
2025-06-25 16:26:18.636 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-25 16:26:18.636 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 18, 指标数量: 2
2025-06-25 16:26:18.721 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=18
2025-06-25 16:26:18.721 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 18
2025-06-25 16:26:18.723 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script
2025-06-25 16:26:18.723 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script
2025-06-25 16:26:18.724 - INFO - [CsvUtils.java:375] : 找到 214 个CSV文件，开始处理
2025-06-25 16:26:18.818 - INFO - [CsvUtils.java:389] : 完成电压数据提取，处理文件数: 214/214, 总数据点: 23136, 合格数据点: 22880, 合格率: 98.89%
2025-06-25 16:26:18.818 - INFO - [SimulationServiceImpl.java:1914] : 仿真任务 18 电压合格率计算完成：总数据点=23136, 合格数据点=22880, 合格率=98.89%, 处理时间=95ms
2025-06-25 16:26:18.818 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 18, 总数据点: 23136, 合格数据点: 22880, 合格率: 98.89%
2025-06-25 16:26:39.290 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=18, queryDTO=BatchMetricQueryDTO(metricIds=[circuit_Mon_monitor_generator_10_pq_1.P1 (kW)], startTimestamp=null)
2025-06-25 16:26:39.290 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 18, metricIds: [circuit_Mon_monitor_generator_10_pq_1.P1 (kW)]
2025-06-25 16:26:39.291 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: circuit_Mon_monitor_generator_10_pq_1.P1 (kW)
2025-06-25 16:26:39.292 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_pq_1.csv, 指标ID: circuit_Mon_monitor_generator_10_pq_1.P1 (kW)
2025-06-25 16:26:39.295 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_pq_1.csv, 指标: circuit_Mon_monitor_generator_10_pq_1.P1 (kW), 数据点数: 96
2025-06-25 16:26:39.296 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 96
2025-06-25 16:26:39.296 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 18, 指标数量: 1
2025-06-25 16:26:47.822 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=18, queryDTO=BatchMetricQueryDTO(metricIds=[circuit_Mon_monitor_generator_10_pq_1.P1 (kW), 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1], startTimestamp=null)
2025-06-25 16:26:47.823 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 18, metricIds: [circuit_Mon_monitor_generator_10_pq_1.P1 (kW), 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1]
2025-06-25 16:26:47.823 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: circuit_Mon_monitor_generator_10_pq_1.P1 (kW)
2025-06-25 16:26:47.825 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_pq_1.csv, 指标ID: circuit_Mon_monitor_generator_10_pq_1.P1 (kW)
2025-06-25 16:26:47.826 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_pq_1.csv, 指标: circuit_Mon_monitor_generator_10_pq_1.P1 (kW), 数据点数: 96
2025-06-25 16:26:47.826 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:26:47.827 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:26:47.828 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv
2025-06-25 16:26:47.828 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 五天实测数据.xlsx
2025-06-25 16:26:47.828 - INFO - [ExcelUtils.java:338] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:26:48.060 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 476 个数据点
2025-06-25 16:26:48.060 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 2, 失败: 0, 总共获取数据点: 572
2025-06-25 16:26:48.061 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 18, 指标数量: 2
2025-06-25 16:27:07.137 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=18, queryDTO=BatchMetricQueryDTO(metricIds=[circuit_Mon_monitor_generator_10_pq_1.P1 (kW), 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1], startTimestamp=1725120000000)
2025-06-25 16:27:07.137 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 18, metricIds: [circuit_Mon_monitor_generator_10_pq_1.P1 (kW), 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1]
2025-06-25 16:27:07.137 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: circuit_Mon_monitor_generator_10_pq_1.P1 (kW)
2025-06-25 16:27:07.139 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_pq_1.csv, 指标ID: circuit_Mon_monitor_generator_10_pq_1.P1 (kW)
2025-06-25 16:27:07.139 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_pq_1.csv, 指标: circuit_Mon_monitor_generator_10_pq_1.P1 (kW), 数据点数: 96
2025-06-25 16:27:07.140 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:27:07.141 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:27:07.141 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv
2025-06-25 16:27:07.141 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 五天实测数据.xlsx
2025-06-25 16:27:07.142 - INFO - [ExcelUtils.java:338] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:27:07.390 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 476 个数据点
2025-06-25 16:27:07.390 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 2, 失败: 0, 总共获取数据点: 572
2025-06-25 16:27:07.391 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 18, 指标数量: 2
2025-06-25 16:27:13.381 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=18, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1], startTimestamp=1725120000000)
2025-06-25 16:27:13.382 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 18, metricIds: [用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1]
2025-06-25 16:27:13.382 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:27:13.383 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:27:13.383 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv
2025-06-25 16:27:13.383 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 五天实测数据.xlsx
2025-06-25 16:27:13.384 - INFO - [ExcelUtils.java:338] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:27:13.616 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 476 个数据点
2025-06-25 16:27:13.616 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 476
2025-06-25 16:27:13.617 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 18, 指标数量: 1
2025-06-25 16:27:15.115 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=18, queryDTO=BatchMetricQueryDTO(metricIds=[circuit_Mon_monitor_generator_10_vi_1.V1, 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1], startTimestamp=1725120000000)
2025-06-25 16:27:15.116 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 18, metricIds: [circuit_Mon_monitor_generator_10_vi_1.V1, 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1]
2025-06-25 16:27:15.116 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-25 16:27:15.117 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-25 16:27:15.118 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V1, 数据点数: 96
2025-06-25 16:27:15.119 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:27:15.120 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:27:15.120 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv
2025-06-25 16:27:15.121 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 五天实测数据.xlsx
2025-06-25 16:27:15.121 - INFO - [ExcelUtils.java:338] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:27:15.338 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 476 个数据点
2025-06-25 16:27:15.338 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 2, 失败: 0, 总共获取数据点: 572
2025-06-25 16:27:15.338 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 18, 指标数量: 2
2025-06-25 16:27:23.810 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=18, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1], startTimestamp=1725120000000)
2025-06-25 16:27:23.811 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 18, metricIds: [用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1]
2025-06-25 16:27:23.811 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:27:23.840 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:27:23.840 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv
2025-06-25 16:27:23.840 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 五天实测数据.xlsx
2025-06-25 16:27:23.840 - INFO - [ExcelUtils.java:338] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:27:24.078 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 476 个数据点
2025-06-25 16:27:24.078 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 476
2025-06-25 16:27:24.079 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 18, 指标数量: 1
2025-06-25 16:27:35.242 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=18, queryDTO=BatchMetricQueryDTO(metricIds=[circuit_Mon_monitor_generator_10_vi_1.V1, 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1], startTimestamp=1725120000000)
2025-06-25 16:27:35.242 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 18, metricIds: [circuit_Mon_monitor_generator_10_vi_1.V1, 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1]
2025-06-25 16:27:35.242 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-25 16:27:35.243 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-25 16:27:35.244 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V1, 数据点数: 96
2025-06-25 16:27:35.244 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:27:35.245 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:27:35.245 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv
2025-06-25 16:27:35.245 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 五天实测数据.xlsx
2025-06-25 16:27:35.246 - INFO - [ExcelUtils.java:338] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:27:35.437 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 476 个数据点
2025-06-25 16:27:35.437 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 2, 失败: 0, 总共获取数据点: 572
2025-06-25 16:27:35.437 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 18, 指标数量: 2
2025-06-25 16:28:01.812 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=18, queryDTO=BatchMetricQueryDTO(metricIds=[用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1], startTimestamp=1725120000000)
2025-06-25 16:28:01.812 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 18, metricIds: [用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1]
2025-06-25 16:28:01.812 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:28:01.813 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:28:01.813 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv
2025-06-25 16:28:01.814 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 五天实测数据.xlsx
2025-06-25 16:28:01.814 - INFO - [ExcelUtils.java:338] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:28:02.077 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 476 个数据点
2025-06-25 16:28:02.077 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 476
2025-06-25 16:28:02.077 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 18, 指标数量: 1
2025-06-25 16:29:55.612 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=18, queryDTO=BatchMetricQueryDTO(metricIds=[circuit_Mon_monitor_generator_10_vi_1.V1, 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1], startTimestamp=1725120000000)
2025-06-25 16:29:55.612 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 18, metricIds: [circuit_Mon_monitor_generator_10_vi_1.V1, 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1]
2025-06-25 16:29:55.612 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-25 16:29:55.613 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-25 16:29:55.613 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V1, 数据点数: 96
2025-06-25 16:29:55.614 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:29:55.615 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:29:55.615 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv
2025-06-25 16:29:55.615 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 五天实测数据.xlsx
2025-06-25 16:29:55.616 - INFO - [ExcelUtils.java:338] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:29:55.873 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 476 个数据点
2025-06-25 16:29:55.874 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 2, 失败: 0, 总共获取数据点: 572
2025-06-25 16:29:55.874 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 18, 指标数量: 2
2025-06-25 16:29:59.316 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=18, queryDTO=BatchMetricQueryDTO(metricIds=[circuit_Mon_monitor_generator_10_vi_1.V1, circuit_Mon_monitor_generator_10_vi_1.V2, 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1], startTimestamp=1725120000000)
2025-06-25 16:29:59.317 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 18, metricIds: [circuit_Mon_monitor_generator_10_vi_1.V1, circuit_Mon_monitor_generator_10_vi_1.V2, 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1]
2025-06-25 16:29:59.317 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-25 16:29:59.318 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-25 16:29:59.319 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V1, 数据点数: 96
2025-06-25 16:29:59.319 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: circuit_Mon_monitor_generator_10_vi_1.V2
2025-06-25 16:29:59.320 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V2
2025-06-25 16:29:59.320 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V2, 数据点数: 96
2025-06-25 16:29:59.320 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:29:59.321 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:29:59.322 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv
2025-06-25 16:29:59.322 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 五天实测数据.xlsx
2025-06-25 16:29:59.322 - INFO - [ExcelUtils.java:338] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:29:59.545 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 476 个数据点
2025-06-25 16:29:59.545 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 3, 失败: 0, 总共获取数据点: 668
2025-06-25 16:29:59.546 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 18, 指标数量: 3
2025-06-25 16:30:00.099 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=18, queryDTO=BatchMetricQueryDTO(metricIds=[circuit_Mon_monitor_generator_10_vi_1.V1, circuit_Mon_monitor_generator_10_vi_1.VAngle1, circuit_Mon_monitor_generator_10_vi_1.V2, 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1], startTimestamp=1725120000000)
2025-06-25 16:30:00.099 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 18, metricIds: [circuit_Mon_monitor_generator_10_vi_1.V1, circuit_Mon_monitor_generator_10_vi_1.VAngle1, circuit_Mon_monitor_generator_10_vi_1.V2, 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1]
2025-06-25 16:30:00.099 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-25 16:30:00.101 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-25 16:30:00.102 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V1, 数据点数: 96
2025-06-25 16:30:00.102 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: circuit_Mon_monitor_generator_10_vi_1.VAngle1
2025-06-25 16:30:00.103 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.VAngle1
2025-06-25 16:30:00.103 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.VAngle1, 数据点数: 96
2025-06-25 16:30:00.103 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: circuit_Mon_monitor_generator_10_vi_1.V2
2025-06-25 16:30:00.105 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V2
2025-06-25 16:30:00.105 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V2, 数据点数: 96
2025-06-25 16:30:00.105 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:30:00.106 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:30:00.107 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv
2025-06-25 16:30:00.107 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 五天实测数据.xlsx
2025-06-25 16:30:00.107 - INFO - [ExcelUtils.java:338] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:30:00.346 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 476 个数据点
2025-06-25 16:30:00.346 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 4, 失败: 0, 总共获取数据点: 764
2025-06-25 16:30:00.346 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 18, 指标数量: 4
2025-06-25 16:30:00.790 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=18, queryDTO=BatchMetricQueryDTO(metricIds=[circuit_Mon_monitor_generator_10_vi_1.V1, circuit_Mon_monitor_generator_10_vi_1.VAngle1, circuit_Mon_monitor_generator_10_vi_1.V2, circuit_Mon_monitor_generator_10_vi_1.VAngle2, 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1], startTimestamp=1725120000000)
2025-06-25 16:30:00.790 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 18, metricIds: [circuit_Mon_monitor_generator_10_vi_1.V1, circuit_Mon_monitor_generator_10_vi_1.VAngle1, circuit_Mon_monitor_generator_10_vi_1.V2, circuit_Mon_monitor_generator_10_vi_1.VAngle2, 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1]
2025-06-25 16:30:00.790 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-25 16:30:00.791 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-25 16:30:00.792 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V1, 数据点数: 96
2025-06-25 16:30:00.792 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: circuit_Mon_monitor_generator_10_vi_1.VAngle1
2025-06-25 16:30:00.793 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.VAngle1
2025-06-25 16:30:00.794 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.VAngle1, 数据点数: 96
2025-06-25 16:30:00.794 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: circuit_Mon_monitor_generator_10_vi_1.V2
2025-06-25 16:30:00.795 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V2
2025-06-25 16:30:00.795 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V2, 数据点数: 96
2025-06-25 16:30:00.795 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: circuit_Mon_monitor_generator_10_vi_1.VAngle2
2025-06-25 16:30:00.797 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.VAngle2
2025-06-25 16:30:00.797 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.VAngle2, 数据点数: 96
2025-06-25 16:30:00.797 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:30:00.799 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:30:00.799 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv
2025-06-25 16:30:00.799 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 五天实测数据.xlsx
2025-06-25 16:30:00.800 - INFO - [ExcelUtils.java:338] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:30:01.065 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 476 个数据点
2025-06-25 16:30:01.065 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 5, 失败: 0, 总共获取数据点: 860
2025-06-25 16:30:01.065 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 18, 指标数量: 5
2025-06-25 16:34:49.441 - INFO - [SimulationController.java:276] : 接收到导出仿真任务指标数据请求: simulationId=18, queryDTO=BatchMetricExportDTO(pic=data:image/png;base64,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, metricIds=[circuit_Mon_monitor_generator_10_vi_1.V1, circuit_Mon_monitor_generator_10_vi_1.VAngle1, circuit_Mon_monitor_generator_10_vi_1.V2, circuit_Mon_monitor_generator_10_vi_1.VAngle2, 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1], startTimestamp=1725120000000)
2025-06-25 16:34:49.443 - INFO - [SimulationServiceImpl.java:1558] : 开始导出仿真指标数据，仿真ID: 18, 指标数量: 5
2025-06-25 16:34:49.445 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 18, metricIds: [circuit_Mon_monitor_generator_10_vi_1.V1, circuit_Mon_monitor_generator_10_vi_1.VAngle1, circuit_Mon_monitor_generator_10_vi_1.V2, circuit_Mon_monitor_generator_10_vi_1.VAngle2, 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1]
2025-06-25 16:34:49.445 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-25 16:34:49.446 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-25 16:34:49.452 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V1, 数据点数: 96
2025-06-25 16:34:49.452 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: circuit_Mon_monitor_generator_10_vi_1.VAngle1
2025-06-25 16:34:49.453 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.VAngle1
2025-06-25 16:34:49.454 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.VAngle1, 数据点数: 96
2025-06-25 16:34:49.454 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: circuit_Mon_monitor_generator_10_vi_1.V2
2025-06-25 16:34:49.456 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V2
2025-06-25 16:34:49.456 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V2, 数据点数: 96
2025-06-25 16:34:49.456 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: circuit_Mon_monitor_generator_10_vi_1.VAngle2
2025-06-25 16:34:49.457 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.VAngle2
2025-06-25 16:34:49.458 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.VAngle2, 数据点数: 96
2025-06-25 16:34:49.458 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 18, metricId: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:34:49.459 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:34:49.459 - INFO - [CsvUtils.java:153] : CSV文件不存在: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script/用户电压.csv
2025-06-25 16:34:49.459 - INFO - [SimulationServiceImpl.java:1446] : 开始处理Excel指标数据文件: 五天实测数据.xlsx
2025-06-25 16:34:49.459 - INFO - [ExcelUtils.java:338] : 开始获取指标数据, 指标ID: 用户电压.用户编号-4208807520278.用户名称-胡进保.电压等级-220.相别-1
2025-06-25 16:34:49.717 - INFO - [SimulationServiceImpl.java:1458] : 成功获取Excel电压指标数据，共 476 个数据点
2025-06-25 16:34:49.717 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 5, 失败: 0, 总共获取数据点: 860
2025-06-25 16:34:49.717 - INFO - [SimulationServiceImpl.java:1567] : 成功获取5个指标的数据
2025-06-25 16:34:50.880 - INFO - [SimulationServiceImpl.java:1575] : 仿真指标数据已成功导出为Excel文件: simulation_metrics_18.xlsx
2025-06-25 16:37:22.950 - INFO - [SimulationController.java:305] : 接收到获取用户电压合格率统计报表请求: simulationId=24, queryDTO=UserVoltageReportQueryDTO(keyword=, pageNum=1, pageSize=50)
2025-06-25 16:37:22.953 - INFO - [SimulationController.java:290] : 接收到获取台区电压统计报表请求: simulationId=24
2025-06-25 16:37:22.983 - INFO - [SimulationController.java:294] : 获取台区电压统计报表成功，台区ID: 24, 用户总数: 214, 合格率: 85.25734986694737
2025-06-25 16:37:22.999 - INFO - [SimulationController.java:317] : 获取用户电压合格率统计报表成功，仿真任务ID: 24, 用户数量: 214
2025-06-25 16:38:31.949 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=24
2025-06-25 16:38:31.950 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 24
2025-06-25 16:38:31.952 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script
2025-06-25 16:38:33.710 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script, 共 216 个子指标
2025-06-25 16:38:33.710 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/21/24/measured_data/工作簿1.xlsx
2025-06-25 16:38:33.710 - INFO - [ExcelUtils.java:50] : 检测到大文件(5.55MB)，使用优化处理器
2025-06-25 16:38:33.710 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 工作簿1.xlsx 的电压指标，共 1 个指标
2025-06-25 16:38:33.710 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-25 16:38:33.711 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 24, 指标数量: 2
2025-06-25 16:38:33.820 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=24
2025-06-25 16:38:33.821 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 24
2025-06-25 16:38:33.823 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script
2025-06-25 16:38:33.823 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script
2025-06-25 16:38:33.824 - INFO - [CsvUtils.java:375] : 找到 216 个CSV文件，开始处理
2025-06-25 16:38:33.909 - INFO - [CsvUtils.java:389] : 完成电压数据提取，处理文件数: 216/216, 总数据点: 23424, 合格数据点: 23161, 合格率: 98.88%
2025-06-25 16:38:33.909 - INFO - [SimulationServiceImpl.java:1914] : 仿真任务 24 电压合格率计算完成：总数据点=23424, 合格数据点=23161, 合格率=98.88%, 处理时间=86ms
2025-06-25 16:38:33.909 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 24, 总数据点: 23424, 合格数据点: 23161, 合格率: 98.88%
2025-06-25 16:52:42.904 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-06-25 16:52:42.905 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-25 16:52:42.907 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-06-25 16:52:42.909 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-06-25 16:53:13.188 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 26000 (D:\CETWorkSpace\ngap-server\ngap-server.jar started by dell in D:\CETWorkSpace\ngap-server)
2025-06-25 16:53:13.194 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-06-25 16:53:15.384 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-06-25 16:53:15.396 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-06-25 16:53:15.397 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-06-25 16:53:15.398 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-25 16:53:15.465 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-06-25 16:53:15.466 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 2200 ms
2025-06-25 16:53:16.211 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-25 16:53:16.801 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-06-25 16:53:16.927 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-06-25 16:53:17.027 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-06-25 16:53:17.257 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-06-25 16:53:17.286 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-06-25 16:53:17.363 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-06-25 16:53:17.365 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-06-25 16:53:17.405 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-06-25 16:53:17.446 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-06-25 16:53:17.685 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-06-25 16:53:17.729 - INFO - [TaskSchedule.java:38] : 开始删除
2025-06-25 16:53:17.729 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-06-25 16:53:17.732 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 5.455 seconds (JVM running for 6.375)
2025-06-25 16:53:17.751 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-06-25 16:53:17.829 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-25 16:53:17.829 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-25 16:53:17.831 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-06-25 16:53:17.831 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-06-25 16:53:17.976 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-06-25 16:53:17.976 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-25 16:53:17.980 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-06-25 16:53:18.056 - INFO - [TaskSchedule.java:54] : 找到1个已标记删除的项目记录
2025-06-25 16:53:18.056 - INFO - [TaskSchedule.java:64] : 准备删除项目: ID=27, 名称=aaa
2025-06-25 16:53:18.205 - INFO - [TaskSchedule.java:69] : 成功从数据库中物理删除1个项目记录
2025-06-25 16:53:18.206 - INFO - [TaskSchedule.java:90] : 项目文件夹不存在，无需删除: D:\CETWorkSpace\ngap-server\data\27
2025-06-25 16:53:18.207 - INFO - [TaskSchedule.java:93] : 成功清理0个项目的文件
2025-06-25 16:53:18.208 - INFO - [TaskSchedule.java:59] : 项目删除完毕
2025-06-25 16:53:18.208 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-06-25 16:53:18.237 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-06-25 16:53:18.238 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-06-25 16:56:06.908 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-06-25 16:56:06.909 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-25 16:56:06.910 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-06-25 16:56:06.912 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-06-25 16:56:21.613 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 37764 (D:\CETWorkSpace\ngap-server\ngap-server.jar started by dell in D:\CETWorkSpace\ngap-server)
2025-06-25 16:56:21.615 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-06-25 16:56:23.291 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-06-25 16:56:23.299 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-06-25 16:56:23.300 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-06-25 16:56:23.300 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-25 16:56:23.357 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-06-25 16:56:23.358 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 1709 ms
2025-06-25 16:56:24.096 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-25 16:56:24.582 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-06-25 16:56:24.707 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-06-25 16:56:24.817 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-06-25 16:56:25.035 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-06-25 16:56:25.062 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-06-25 16:56:25.156 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-06-25 16:56:25.158 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-06-25 16:56:25.207 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-06-25 16:56:25.267 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-06-25 16:56:25.500 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-06-25 16:56:25.530 - INFO - [TaskSchedule.java:38] : 开始删除
2025-06-25 16:56:25.531 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-06-25 16:56:25.533 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 4.646 seconds (JVM running for 5.009)
2025-06-25 16:56:25.551 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-06-25 16:56:25.638 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-25 16:56:25.639 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-25 16:56:25.641 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-06-25 16:56:25.642 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-06-25 16:56:25.730 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-06-25 16:56:25.731 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-25 16:56:25.732 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-06-25 16:56:25.749 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-06-25 16:56:25.749 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-06-25 16:56:25.750 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-06-25 16:56:25.750 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-06-25 17:20:37.704 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 13068 (D:\CETWorkSpace\ngap-server\ngap-server.jar started by dell in D:\CETWorkSpace\ngap-server)
2025-06-25 17:20:37.715 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-06-25 17:20:39.339 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-06-25 17:20:39.348 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-06-25 17:20:39.348 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-06-25 17:20:39.349 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-25 17:20:39.405 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-06-25 17:20:39.406 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 1647 ms
2025-06-25 17:20:40.022 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-25 17:20:40.492 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-06-25 17:20:40.579 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-06-25 17:20:40.677 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-06-25 17:20:40.886 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-06-25 17:20:40.916 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-06-25 17:20:40.979 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-06-25 17:20:40.980 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-06-25 17:20:41.013 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-06-25 17:20:41.055 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-06-25 17:20:41.270 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-06-25 17:20:41.302 - INFO - [TaskSchedule.java:38] : 开始删除
2025-06-25 17:20:41.302 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-06-25 17:20:41.304 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 4.292 seconds (JVM running for 4.625)
2025-06-25 17:20:41.319 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-06-25 17:20:41.372 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-25 17:20:41.373 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-25 17:20:41.384 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-06-25 17:20:41.385 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-06-25 17:20:41.990 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-06-25 17:20:41.991 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-25 17:20:41.994 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-06-25 17:20:42.025 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-06-25 17:20:42.025 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-06-25 17:20:42.071 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-06-25 17:20:42.071 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-06-25 17:21:16.321 - INFO - [DirectJDKLog.java:173] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 17:21:16.321 - INFO - [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-06-25 17:21:16.502 - INFO - [FrameworkServlet.java:547] : Completed initialization in 181 ms
2025-06-25 17:21:16.879 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-25 17:21:16.879 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-25 17:21:16.880 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-25 17:21:16.880 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-25 17:21:18.126 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-25 17:21:18.126 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-25 17:21:18.936 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-25 17:21:18.937 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-25 17:21:19.161 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-25 17:21:19.162 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-25 17:26:37.002 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-25 17:26:37.002 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-25 17:26:37.003 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-25 17:26:37.003 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-25 17:26:37.003 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-25 17:26:37.003 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-25 17:26:37.004 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-25 17:26:37.004 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-25 17:26:37.005 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-25 17:26:37.005 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-25 18:12:25.575 - INFO - [LineCodeController.java:97] : 接收到查询线路代码请求: lineCodeId=17
2025-06-25 18:12:25.583 - INFO - [LineCodeServiceImpl.java:112] : 查询线路代码成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-25 18:12:25.583 - INFO - [LineCodeController.java:101] : 线路代码查询成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-25 18:12:55.823 - INFO - [LineCodeController.java:97] : 接收到查询线路代码请求: lineCodeId=17
2025-06-25 18:12:55.824 - INFO - [LineCodeServiceImpl.java:112] : 查询线路代码成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
2025-06-25 18:12:55.825 - INFO - [LineCodeController.java:101] : 线路代码查询成功: LineCode(lineCodeId=17, lineCode={"name":"JKLYJ70","units":"km","nphases":3,"R1":0.46,"X1":0.398,"basefreq":50})
