2025-06-27 10:18:29.412 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-27 10:18:29.416 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-27 10:18:29.416 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-27 10:18:29.417 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-27 10:18:29.471 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-27 10:18:29.472 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-27 10:18:29.507 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-27 10:18:29.507 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-27 10:18:29.508 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-27 10:18:29.508 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-27 17:20:42.677 - INFO - [TaskSchedule.java:38] : 开始删除
2025-06-27 17:20:42.684 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-06-27 17:20:42.690 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-06-27 17:20:42.690 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-06-27 17:20:42.723 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-06-27 17:20:42.723 - INFO - [TaskSchedule.java:41] : 删除完毕
