2025-06-23 08:34:22.549 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 08:34:22.552 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 08:34:22.568 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 08:34:22.568 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 08:34:22.569 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 08:34:22.569 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 08:40:15.657 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 08:40:15.658 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 08:40:15.659 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 08:40:15.660 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 08:40:15.660 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 08:40:15.660 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 08:41:11.255 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 08:41:11.256 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 08:41:11.256 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 08:41:11.256 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 08:41:11.256 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 08:41:11.256 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 08:46:02.701 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 19452 (D:\CETWorkSpace\ngap-server\ngap-server.jar started by dell in D:\CETWorkSpace\ngap-server)
2025-06-23 08:46:02.716 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-06-23 08:46:03.983 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-06-23 08:46:03.991 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-06-23 08:46:03.991 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-06-23 08:46:03.992 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-23 08:46:04.042 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-06-23 08:46:04.042 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 1296 ms
2025-06-23 08:46:04.591 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-23 08:46:05.064 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-06-23 08:46:05.142 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-06-23 08:46:05.227 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-06-23 08:46:05.445 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-06-23 08:46:05.472 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-06-23 08:46:05.545 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-06-23 08:46:05.547 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-06-23 08:46:05.583 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-06-23 08:46:05.624 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-06-23 08:46:05.851 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-06-23 08:46:05.888 - INFO - [TaskSchedule.java:38] : 开始删除
2025-06-23 08:46:05.888 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-06-23 08:46:05.891 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 3.924 seconds (JVM running for 4.305)
2025-06-23 08:46:05.904 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-06-23 08:46:06.004 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-23 08:46:06.004 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-23 08:46:06.005 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-06-23 08:46:06.006 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-06-23 08:46:06.535 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-06-23 08:46:06.535 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-23 08:46:06.537 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-06-23 08:46:06.556 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-06-23 08:46:06.556 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-06-23 08:46:06.728 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-06-23 08:46:06.729 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-06-23 08:52:01.195 - INFO - [DirectJDKLog.java:173] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 08:52:01.195 - INFO - [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-06-23 08:52:01.213 - INFO - [FrameworkServlet.java:547] : Completed initialization in 18 ms
2025-06-23 08:52:01.290 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-23 08:52:01.291 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-23 08:52:01.292 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-23 08:52:01.292 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-23 08:52:01.422 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-23 08:52:01.423 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-23 08:52:01.476 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-23 08:52:01.476 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-23 08:52:01.479 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-23 08:52:01.479 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-23 08:52:08.250 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-06-23 08:52:08.251 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-06-23 08:52:08.254 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-23 08:52:08.256 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-23 08:52:20.119 - INFO - [SimulationController.java:130] : 接收到获取仿真任务详情请求: simulationId=30
2025-06-23 08:52:20.126 - INFO - [SimulationServiceImpl.java:589] : 获取仿真任务详情成功: ID=30
2025-06-23 08:52:20.127 - INFO - [SimulationController.java:134] : 获取仿真任务详情成功，ID: 30
2025-06-23 08:52:25.865 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 08:52:25.866 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 08:52:25.868 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 08:52:25.868 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 08:52:25.868 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 08:52:25.868 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 08:52:30.145 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 08:52:30.147 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 08:52:30.150 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 08:52:30.150 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 08:52:30.152 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 08:52:30.154 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 08:52:36.410 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 08:52:36.412 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 08:52:36.413 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 08:52:36.413 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 08:52:36.414 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 08:52:36.414 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 08:52:39.798 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 08:52:39.798 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 08:52:39.800 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 08:52:39.800 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 08:52:39.801 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 08:52:39.801 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 08:52:44.667 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 08:52:44.668 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 08:52:44.670 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 08:52:44.671 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 08:52:44.672 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 08:52:44.673 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 08:54:35.504 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-06-23 08:54:35.506 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-23 08:54:35.508 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-06-23 08:54:35.510 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-06-23 08:54:56.125 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 10600 (D:\CETWorkSpace\ngap-server\ngap-server.jar started by dell in D:\CETWorkSpace\ngap-server)
2025-06-23 08:54:56.127 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-06-23 08:54:57.633 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-06-23 08:54:57.644 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-06-23 08:54:57.644 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-06-23 08:54:57.645 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-23 08:54:57.684 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-06-23 08:54:57.684 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 1524 ms
2025-06-23 08:54:58.224 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-23 08:54:58.595 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-06-23 08:54:58.671 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-06-23 08:54:58.750 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-06-23 08:54:58.938 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-06-23 08:54:58.954 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-06-23 08:54:59.004 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-06-23 08:54:59.005 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-06-23 08:54:59.044 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-06-23 08:54:59.096 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-06-23 08:54:59.300 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-06-23 08:54:59.324 - INFO - [TaskSchedule.java:38] : 开始删除
2025-06-23 08:54:59.324 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-06-23 08:54:59.327 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 3.808 seconds (JVM running for 4.706)
2025-06-23 08:54:59.338 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-06-23 08:54:59.406 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-23 08:54:59.406 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-23 08:54:59.408 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-06-23 08:54:59.408 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-06-23 08:54:59.543 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-06-23 08:54:59.544 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-23 08:54:59.549 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-06-23 08:54:59.565 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-06-23 08:54:59.566 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-06-23 08:54:59.597 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-06-23 08:54:59.598 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-06-23 08:56:52.659 - INFO - [DirectJDKLog.java:173] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 08:56:52.660 - INFO - [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-06-23 08:56:52.675 - INFO - [FrameworkServlet.java:547] : Completed initialization in 15 ms
2025-06-23 08:56:52.804 - INFO - [SimulationController.java:102] : 接收到更新仿真任务请求: simulationId=30, simulationModel=, simulationDraft={"x":229,"y":120,"scale":0.97,"pens":[{"width":97,"height":97,"ratio":true,"name":"image","deviceType":"battery","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_163)'%3e%3crect%20x='3.5'%20y='0.5'%20width='17'%20height='23'%20stroke='black'/%3e%3cpath%20d='M1%208V16'%20stroke='black'/%3e%3cpath%20d='M23%208V16'%20stroke='black'/%3e%3cpath%20d='M10.3333%207H15.7879L12.4545%2011.2H17L9.72727%2019L11.2424%2013.3H7L10.3333%207Z'%20stroke='black'%20stroke-linejoin='round'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_163'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"386f5d5","x":0.5,"y":1,"penId":"8a39a6d"}],"simulationConfig":{"phases":3,"baseKV":10,"pu":1,"customScript":""},"id":"8a39a6d","children":[],"x":-24.***************,"y":-13.490106163677652,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"3e2fdfc","lineAnchor":"e0ceaf","anchor":"386f5d5"}],"ex":291.*************,"ey":157.71489383632237,"center":{"x":243.**************,"y":109.21489383632237}},{"width":97,"height":97,"ratio":true,"name":"image","deviceType":"transformer","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='12'%20cy='8'%20r='5.5'%20stroke='black'/%3e%3ccircle%20cx='12'%20cy='16'%20r='5.5'%20stroke='black'/%3e%3cpath%20d='M12%200V2'%20stroke='black'/%3e%3cpath%20d='M12%2022V24'%20stroke='black'/%3e%3c/svg%3e","anchors":[{"id":"1c3afb7f","x":0.5,"y":0,"background":"blue","flag":"wind1","penId":"25e213a7"},{"id":"fb474e0","x":0.5,"y":1,"background":"red","flag":"wind2","penId":"25e213a7"}],"simulationConfig":{"name":"","buses":[],"phases":3,"windings":2,"XHL":0,"customScript":"","bank":"","maxTap":1.1,"minTap":0.9,"numTaps":32,"percentLoadloss":0,"wdgs":[{"wdg":1,"buses":"","conn":"WYE","kv":12.47,"kva":0,"percentR":0,"tap":0},{"wdg":2,"buses":"","conn":"WYE","kv":12.47,"kva":0,"percentR":0,"tap":0}]},"id":"25e213a7","children":[],"x":-24.***************,"y":117.**************,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"3e2fdfc","lineAnchor":"ee8fa09","anchor":"1c3afb7f"}],"ex":291.*************,"ey":306.*************,"center":{"x":243.**************,"y":257.*************}},{"id":"3e2fdfc","name":"line","lineName":"line","x":24.**************3,"y":83.*************,"type":1,"toArrow":"line","lineWidth":1,"length":34.**************,"ex":24.**************3,"ey":117.**************,"width":0,"height":34.**************,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"e0ceaf","x":0,"y":0,"penId":"3e2fdfc","connectTo":"8a39a6d","anchorId":"386f5d5","start":true,"lineLength":34.**************},{"x":0,"y":1,"id":"ee8fa09","penId":"3e2fdfc","connectTo":"25e213a7","anchorId":"1c3afb7f"}],"rotate":0,"center":{"x":243.**************,"y":183.41989383632244}}],"origin":{"x":9.356938775510173,"y":9.304893836322435},"center":{"x":302,"y":293},"paths":{},"template":"69f28d8","simulationConfig":{"name":"","number":24,"stepSize":"1h","mode":"daily","hour":0,"sec":0,"voltageBases":[0.22,0.38,10],"defaultBaseFrequency":60},"name":"meta2d.2025620170307","version":"1.0.68","dataPoints":[]}, simulationScript=, nodes=, controlStrategy={}
2025-06-23 08:56:52.818 - INFO - [SimulationServiceImpl.java:336] : 仿真任务draft更新: 30
2025-06-23 08:56:53.013 - INFO - [SimulationServiceImpl.java:380] : 仿真任务更新成功: ID=30
2025-06-23 08:56:53.014 - INFO - [SimulationController.java:108] : 仿真任务更新成功，ID: 30, 更新后模型: null, 更新后脚本: null
2025-06-23 08:57:19.639 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-06-23 08:57:19.641 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-23 08:57:19.643 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-06-23 08:57:19.645 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-06-23 08:57:23.821 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 1132 (D:\CETWorkSpace\ngap-server\ngap-server.jar started by dell in D:\CETWorkSpace\ngap-server)
2025-06-23 08:57:23.822 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-06-23 08:57:25.643 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-06-23 08:57:25.655 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-06-23 08:57:25.656 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-06-23 08:57:25.656 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-23 08:57:25.732 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-06-23 08:57:25.732 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 1877 ms
2025-06-23 08:57:26.249 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-23 08:57:26.757 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-06-23 08:57:26.842 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-06-23 08:57:26.950 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-06-23 08:57:27.131 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-06-23 08:57:27.155 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-06-23 08:57:27.217 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-06-23 08:57:27.217 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-06-23 08:57:27.248 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-06-23 08:57:27.299 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-06-23 08:57:27.500 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-06-23 08:57:27.529 - INFO - [TaskSchedule.java:38] : 开始删除
2025-06-23 08:57:27.530 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-06-23 08:57:27.532 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 4.418 seconds (JVM running for 5.226)
2025-06-23 08:57:27.546 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-06-23 08:57:27.598 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-23 08:57:27.599 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-23 08:57:27.601 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-06-23 08:57:27.601 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-06-23 08:57:27.701 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-06-23 08:57:27.708 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-06-23 08:57:27.708 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-23 08:57:27.718 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-06-23 08:57:27.718 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-06-23 08:57:27.770 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-06-23 08:57:27.770 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-06-23 08:58:36.417 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-06-23 08:58:36.418 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-23 08:58:36.420 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-06-23 08:58:36.421 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-06-23 08:58:41.190 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 2604 (D:\CETWorkSpace\ngap-server\ngap-server.jar started by dell in D:\CETWorkSpace\ngap-server)
2025-06-23 08:58:41.192 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-06-23 08:58:42.452 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-06-23 08:58:42.459 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-06-23 08:58:42.460 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-06-23 08:58:42.460 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-23 08:58:42.498 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-06-23 08:58:42.498 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 1279 ms
2025-06-23 08:58:42.995 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-23 08:58:43.337 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-06-23 08:58:43.417 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-06-23 08:58:43.498 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-06-23 08:58:43.670 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-06-23 08:58:43.694 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-06-23 08:58:43.734 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-06-23 08:58:43.735 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-06-23 08:58:43.753 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-06-23 08:58:43.784 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-06-23 08:58:43.956 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-06-23 08:58:43.981 - INFO - [TaskSchedule.java:38] : 开始删除
2025-06-23 08:58:43.981 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-06-23 08:58:43.983 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 3.385 seconds (JVM running for 3.713)
2025-06-23 08:58:43.993 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-06-23 08:58:44.039 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-23 08:58:44.039 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-23 08:58:44.040 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-06-23 08:58:44.041 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-06-23 08:58:44.143 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-06-23 08:58:44.143 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-23 08:58:44.147 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-06-23 08:58:44.172 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-06-23 08:58:44.172 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-06-23 08:58:44.173 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-06-23 08:58:44.174 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-06-23 08:59:07.186 - INFO - [DirectJDKLog.java:173] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 08:59:07.187 - INFO - [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-06-23 08:59:07.194 - INFO - [FrameworkServlet.java:547] : Completed initialization in 7 ms
2025-06-23 08:59:07.221 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-23 08:59:07.221 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-23 08:59:07.222 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-23 08:59:07.222 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-23 08:59:07.263 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-23 08:59:07.264 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-23 08:59:07.341 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-23 08:59:07.341 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-23 08:59:07.345 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-23 08:59:07.345 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-23 08:59:09.171 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-06-23 08:59:09.172 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-06-23 08:59:09.173 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-23 08:59:09.174 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-23 08:59:12.528 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-23 08:59:12.528 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-23 08:59:12.528 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-23 08:59:12.528 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-23 08:59:12.529 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-23 08:59:12.529 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-23 08:59:12.533 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-23 08:59:12.533 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-23 08:59:12.535 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-23 08:59:12.535 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-23 08:59:29.014 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=25, keyword=, startTime=null, endTime=null)
2025-06-23 08:59:29.014 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 25, keyword: , startTime: null, endTime: null
2025-06-23 08:59:29.080 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-23 08:59:29.080 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-23 09:00:23.363 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:00:23.364 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:00:23.367 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:00:23.367 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:00:23.368 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:00:23.369 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:00:48.505 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:00:48.506 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:00:48.508 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:00:48.508 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:00:48.508 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:00:48.508 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:00:53.678 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:00:53.679 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:00:53.681 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:00:53.681 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:00:53.683 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:00:53.684 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:01:00.228 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:01:00.229 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:01:00.232 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:01:00.233 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:01:00.234 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:01:00.234 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:01:06.610 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:01:06.611 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:01:06.613 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:01:06.613 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:01:06.613 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:01:06.614 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:01:26.121 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:01:26.122 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:01:26.122 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:01:26.123 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:01:26.123 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:01:26.124 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:01:38.276 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:01:38.277 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:01:38.279 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:01:38.279 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:01:38.280 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:01:38.280 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:01:44.955 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:01:44.956 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:01:44.958 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:01:44.958 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:01:44.959 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:01:44.959 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:01:49.989 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:01:49.990 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:01:49.992 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:01:49.993 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:01:49.994 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:01:49.995 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:03:09.658 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:03:09.659 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:03:09.661 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:03:09.662 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:03:09.663 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:03:09.663 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:03:17.900 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:03:17.901 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:03:17.904 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:03:17.905 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:03:17.907 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:03:17.908 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:03:27.206 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:03:27.206 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:03:27.207 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:03:27.207 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:03:27.208 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:03:27.208 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:06:22.931 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-23 09:06:22.931 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-23 09:06:22.931 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-23 09:06:22.932 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-23 09:06:22.932 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-23 09:06:22.933 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-23 09:06:22.936 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-23 09:06:22.937 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-23 09:06:22.938 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-23 09:06:22.939 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-23 09:07:49.725 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:07:49.725 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:07:49.726 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:07:49.726 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:07:49.727 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:07:49.727 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:07:56.783 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:07:56.784 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:07:56.785 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:07:56.785 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:07:56.786 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:07:56.786 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:08:57.152 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:08:57.153 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:08:57.155 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:08:57.156 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:08:57.157 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:08:57.157 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:09:03.890 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:09:03.892 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:09:03.895 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:09:03.895 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:09:03.895 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:09:03.895 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:09:45.915 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:09:45.915 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:09:45.917 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:09:45.917 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:09:45.919 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:09:45.919 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:09:50.325 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:09:50.325 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:09:50.326 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:09:50.326 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:09:50.326 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:09:50.326 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:10:15.433 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:10:15.434 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:10:15.436 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:10:15.436 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:10:15.437 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:10:15.437 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:10:16.149 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:10:16.150 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:10:16.152 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:10:16.153 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:10:16.155 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:10:16.156 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:10:20.974 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:10:20.975 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:10:20.977 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:10:20.978 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:10:20.980 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:10:20.980 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:11:27.415 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:11:27.415 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:11:27.417 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:11:27.418 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:11:27.419 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:11:27.419 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:11:32.171 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:11:32.172 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:11:32.173 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:11:32.174 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:11:32.174 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:11:32.175 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:13:14.422 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:13:14.423 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:13:14.426 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:13:14.426 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:13:14.428 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:13:14.428 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:13:19.078 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:13:19.079 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:13:19.081 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:13:19.082 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:13:19.083 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:13:19.084 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:14:04.961 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:14:04.961 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:14:04.964 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:14:04.964 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:14:04.965 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:14:04.965 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:14:09.053 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:14:09.054 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:14:09.055 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:14:09.055 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:14:09.055 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:14:09.055 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:16:31.222 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:16:31.223 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:16:31.224 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:16:31.225 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:16:31.226 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:16:31.226 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:16:35.188 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:16:35.189 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:16:35.191 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:16:35.191 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:16:35.192 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:16:35.193 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:17:00.269 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:17:00.269 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:17:00.270 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:17:00.270 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:17:00.271 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:17:00.271 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:17:04.621 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:17:04.622 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:17:04.623 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:17:04.624 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:17:04.624 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:17:04.625 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:18:56.828 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:18:56.829 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:18:56.831 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:18:56.832 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:18:56.833 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:18:56.833 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:19:00.872 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:19:00.872 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:19:00.872 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:19:00.872 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:19:00.873 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:19:00.873 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:19:52.589 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:19:52.590 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:19:52.591 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:19:52.592 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:19:52.593 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:19:52.593 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:19:56.364 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:19:56.364 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:19:56.367 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:19:56.367 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:19:56.367 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:19:56.368 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:20:01.853 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:20:01.854 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:20:01.856 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:20:01.856 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:20:01.857 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:20:01.858 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:20:58.194 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:20:58.194 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:20:58.195 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:20:58.195 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:20:58.195 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:20:58.195 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:21:12.888 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:21:12.888 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:21:12.890 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:21:12.890 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:21:12.891 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:21:12.892 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:22:28.950 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:22:28.950 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:22:28.951 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:22:28.951 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:22:28.951 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:22:28.951 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:22:32.160 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:22:32.160 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:22:32.162 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:22:32.162 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:22:32.162 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:22:32.162 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:23:08.263 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:23:08.264 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:23:08.265 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:23:08.266 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:23:08.266 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:23:08.267 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:23:12.374 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:23:12.375 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:23:12.377 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:23:12.377 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:23:12.378 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:23:12.379 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:23:23.826 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:23:23.826 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:23:23.827 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:23:23.828 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:23:23.828 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:23:23.828 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:24:12.886 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:24:12.886 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:24:12.888 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:24:12.888 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:24:12.889 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:24:12.889 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:24:17.221 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:24:17.222 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:24:17.224 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:24:17.224 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:24:17.225 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:24:17.225 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:24:54.132 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:24:54.133 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:24:54.135 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:24:54.135 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:24:54.136 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:24:54.137 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:24:57.631 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:24:57.631 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:24:57.634 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:24:57.634 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:24:57.635 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:24:57.635 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:25:07.464 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:25:07.464 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:25:07.465 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:25:07.466 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:25:07.466 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:25:07.468 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:26:15.747 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:26:15.748 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:26:15.749 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:26:15.749 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:26:15.749 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:26:15.749 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:26:19.624 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:26:19.625 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:26:19.626 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:26:19.627 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:26:19.628 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:26:19.628 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:29:10.570 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:29:10.571 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:29:10.574 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:29:10.574 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:29:10.574 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:29:10.575 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:29:17.166 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:29:17.167 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:29:17.169 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:29:17.169 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:29:17.171 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:29:17.171 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:29:32.663 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:29:32.664 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:29:32.667 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:29:32.667 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:29:32.668 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:29:32.668 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:30:59.583 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:30:59.583 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:30:59.585 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:30:59.585 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:30:59.586 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:30:59.586 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:31:03.385 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:31:03.386 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:31:03.388 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:31:03.388 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:31:03.389 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:31:03.389 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:32:52.678 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:32:52.679 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:32:52.682 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:32:52.682 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:32:52.683 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:32:52.684 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:33:47.111 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:33:47.111 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:33:47.113 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:33:47.113 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:33:47.114 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:33:47.114 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:47:03.476 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-23 09:47:03.477 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-23 09:47:03.477 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-23 09:47:03.477 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-23 09:47:03.477 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-23 09:47:03.477 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-23 09:47:03.486 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-23 09:47:03.486 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-23 09:47:03.488 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-23 09:47:03.489 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-23 09:47:04.444 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-23 09:47:04.444 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-23 09:47:04.444 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-23 09:47:04.444 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-23 09:47:04.444 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-23 09:47:04.444 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-23 09:47:04.445 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-23 09:47:04.445 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-23 09:47:04.445 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-23 09:47:04.445 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-23 09:47:07.260 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-23 09:47:07.261 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-23 09:47:07.261 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-23 09:47:07.261 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-23 09:47:07.281 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-23 09:47:07.281 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-23 09:47:07.282 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-23 09:47:07.282 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-23 09:47:07.283 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-23 09:47:07.283 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-23 09:48:03.862 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-06-23 09:48:03.863 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-06-23 09:48:03.905 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-23 09:48:03.906 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-23 09:48:07.197 - INFO - [SimulationController.java:130] : 接收到获取仿真任务详情请求: simulationId=30
2025-06-23 09:48:07.199 - INFO - [SimulationServiceImpl.java:589] : 获取仿真任务详情成功: ID=30
2025-06-23 09:48:07.199 - INFO - [SimulationController.java:134] : 获取仿真任务详情成功，ID: 30
2025-06-23 09:48:45.255 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=25, keyword=, startTime=null, endTime=null)
2025-06-23 09:48:45.256 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 25, keyword: , startTime: null, endTime: null
2025-06-23 09:48:45.258 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-23 09:48:45.259 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-23 09:48:46.727 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=28
2025-06-23 09:48:46.727 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 28
2025-06-23 09:48:46.759 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/25/28/simulation_script
2025-06-23 09:48:46.760 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/25/28/simulation_script, 共 0 个子指标
2025-06-23 09:48:46.760 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 1 个分类
2025-06-23 09:48:46.760 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 28, 指标数量: 1
2025-06-23 09:48:47.105 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=28
2025-06-23 09:48:47.106 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 28
2025-06-23 09:48:47.108 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/25/28/simulation_script
2025-06-23 09:48:47.108 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/25/28/simulation_script
2025-06-23 09:48:47.111 - INFO - [CsvUtils.java:371] : 目录中未找到CSV文件: D:\CETWorkSpace\ngap-server/data/25/28/simulation_script
2025-06-23 09:48:47.112 - WARN - [SimulationServiceImpl.java:1896] : 仿真任务 28 的输出目录中未找到电压数据，处理时间: 4ms
2025-06-23 09:48:47.114 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 28, 总数据点: 0, 合格数据点: 0, 合格率: 0.00%
2025-06-23 09:49:13.482 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=28
2025-06-23 09:49:13.482 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 28
2025-06-23 09:49:13.483 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/25/28/simulation_script
2025-06-23 09:49:13.483 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/25/28/simulation_script, 共 0 个子指标
2025-06-23 09:49:13.483 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 1 个分类
2025-06-23 09:49:13.483 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 28, 指标数量: 1
2025-06-23 09:49:13.755 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=28
2025-06-23 09:49:13.756 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 28
2025-06-23 09:49:13.758 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/25/28/simulation_script
2025-06-23 09:49:13.759 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/25/28/simulation_script
2025-06-23 09:49:13.759 - INFO - [CsvUtils.java:371] : 目录中未找到CSV文件: D:\CETWorkSpace\ngap-server/data/25/28/simulation_script
2025-06-23 09:49:13.759 - WARN - [SimulationServiceImpl.java:1896] : 仿真任务 28 的输出目录中未找到电压数据，处理时间: 0ms
2025-06-23 09:49:13.759 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 28, 总数据点: 0, 合格数据点: 0, 合格率: 0.00%
2025-06-23 09:49:19.800 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-23 09:49:19.801 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-23 09:49:19.804 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-23 09:49:19.805 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-23 09:49:19.805 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-23 09:49:19.805 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-23 09:49:19.964 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-23 09:49:19.965 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-23 09:49:19.965 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-23 09:49:19.965 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-23 09:49:21.373 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=25, keyword=, startTime=null, endTime=null)
2025-06-23 09:49:21.374 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 25, keyword: , startTime: null, endTime: null
2025-06-23 09:49:21.378 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-23 09:49:21.379 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-23 09:49:22.707 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=28
2025-06-23 09:49:22.708 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 28
2025-06-23 09:49:22.710 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/25/28/simulation_script
2025-06-23 09:49:22.710 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/25/28/simulation_script, 共 0 个子指标
2025-06-23 09:49:22.711 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 1 个分类
2025-06-23 09:49:22.711 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 28, 指标数量: 1
2025-06-23 09:49:22.779 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=28
2025-06-23 09:49:22.779 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 28
2025-06-23 09:49:22.782 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/25/28/simulation_script
2025-06-23 09:49:22.782 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/25/28/simulation_script
2025-06-23 09:49:22.783 - INFO - [CsvUtils.java:371] : 目录中未找到CSV文件: D:\CETWorkSpace\ngap-server/data/25/28/simulation_script
2025-06-23 09:49:22.783 - WARN - [SimulationServiceImpl.java:1896] : 仿真任务 28 的输出目录中未找到电压数据，处理时间: 1ms
2025-06-23 09:49:22.783 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 28, 总数据点: 0, 合格数据点: 0, 合格率: 0.00%
2025-06-23 09:49:27.347 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:49:27.348 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:49:27.350 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:49:27.350 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:49:27.351 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:49:27.351 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:49:44.693 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:49:44.693 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:49:44.693 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:49:44.694 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:49:44.694 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:49:44.694 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:50:18.622 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:50:18.623 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:50:18.625 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:50:18.625 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:50:18.626 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:50:18.626 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:50:28.180 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:50:28.181 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:50:28.182 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:50:28.182 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:50:28.182 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:50:28.182 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:50:35.583 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:50:35.584 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:50:35.585 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:50:35.585 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:50:35.586 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:50:35.586 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:50:57.470 - INFO - [SimulationController.java:102] : 接收到更新仿真任务请求: simulationId=30, simulationModel=, simulationDraft={"x":446,"y":198,"scale":0.97,"pens":[{"width":97,"height":97,"ratio":true,"name":"image","deviceType":"battery","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_163)'%3e%3crect%20x='3.5'%20y='0.5'%20width='17'%20height='23'%20stroke='black'/%3e%3cpath%20d='M1%208V16'%20stroke='black'/%3e%3cpath%20d='M23%208V16'%20stroke='black'/%3e%3cpath%20d='M10.3333%207H15.7879L12.4545%2011.2H17L9.72727%2019L11.2424%2013.3H7L10.3333%207Z'%20stroke='black'%20stroke-linejoin='round'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_163'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"386f5d5","x":0.5,"y":1,"penId":"8a39a6d"}],"simulationConfig":{"phases":3,"baseKV":10,"pu":1,"customScript":""},"id":"8a39a6d","children":[],"x":-256.*************,"y":-61.99010616367765,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"3e2fdfc","lineAnchor":"e0ceaf","anchor":"386f5d5"}],"ex":291.*************,"ey":157.71489383632237,"center":{"x":243.**************,"y":109.21489383632237}},{"width":97,"height":97,"ratio":true,"name":"image","deviceType":"transformer","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='12'%20cy='8'%20r='5.5'%20stroke='black'/%3e%3ccircle%20cx='12'%20cy='16'%20r='5.5'%20stroke='black'/%3e%3cpath%20d='M12%200V2'%20stroke='black'/%3e%3cpath%20d='M12%2022V24'%20stroke='black'/%3e%3c/svg%3e","anchors":[{"id":"1c3afb7f","x":0.5,"y":0,"background":"blue","flag":"wind1","penId":"25e213a7"},{"id":"fb474e0","x":0.5,"y":1,"background":"red","flag":"wind2","penId":"25e213a7"}],"simulationConfig":{"name":"","buses":[],"phases":3,"windings":2,"XHL":0.01,"customScript":"","bank":"","maxTap":1.1,"minTap":0.9,"numTaps":32,"percentLoadloss":0,"wdgs":[{"wdg":1,"buses":"","conn":"DELT","kv":10,"kva":200,"percentR":0.5,"tap":0},{"wdg":2,"buses":"","conn":"WYE","kv":0.4,"kva":200,"percentR":0.5,"tap":0}]},"id":"25e213a7","children":[],"x":-256.*************,"y":69.**************,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"3e2fdfc","lineAnchor":"ee8fa09","anchor":"1c3afb7f"}],"ex":291.*************,"ey":306.*************,"center":{"x":243.**************,"y":257.*************}},{"id":"3e2fdfc","name":"line","lineName":"line","x":-207.*************,"y":35.***************,"type":1,"toArrow":"line","lineWidth":1,"length":34.**************,"ex":-207.*************,"ey":69.**************,"width":0,"height":34.**************,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"e0ceaf","x":0,"y":0,"penId":"3e2fdfc","connectTo":"8a39a6d","anchorId":"386f5d5","start":true,"lineLength":34.**************},{"x":0,"y":1,"id":"ee8fa09","penId":"3e2fdfc","connectTo":"25e213a7","anchorId":"1c3afb7f"}],"rotate":0,"center":{"x":243.**************,"y":183.41989383632244}}],"origin":{"x":9.356938775510173,"y":9.304893836322435},"center":{"x":302,"y":293},"paths":{},"template":"69f28d8","simulationConfig":{"name":"","number":24,"stepSize":"1h","mode":"daily","hour":0,"sec":0,"voltageBases":[0.22,0.38,10],"defaultBaseFrequency":60},"name":"meta2d.2025620170307","version":"1.0.68","dataPoints":[]}, simulationScript=, nodes=, controlStrategy={}
2025-06-23 09:50:57.472 - INFO - [SimulationServiceImpl.java:336] : 仿真任务draft更新: 30
2025-06-23 09:50:57.572 - INFO - [SimulationServiceImpl.java:380] : 仿真任务更新成功: ID=30
2025-06-23 09:50:57.573 - INFO - [SimulationController.java:108] : 仿真任务更新成功，ID: 30, 更新后模型: null, 更新后脚本: null
2025-06-23 09:51:05.920 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:51:05.921 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:51:05.922 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:51:05.922 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:51:05.923 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:51:05.923 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:51:45.650 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:51:45.651 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:51:45.652 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:51:45.653 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:51:45.653 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:51:45.654 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:51:49.885 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:51:49.885 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:51:49.887 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:51:49.887 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:51:49.888 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:51:49.888 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:52:36.240 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:52:36.241 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:52:36.244 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:52:36.244 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:52:36.245 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:52:36.245 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:52:45.359 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:52:45.360 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:52:45.362 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:52:45.362 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:52:45.363 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:52:45.364 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:53:22.190 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:53:22.190 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:53:22.192 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:53:22.193 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:53:22.194 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:53:22.194 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:53:26.667 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:53:26.668 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:53:26.670 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:53:26.670 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:53:26.671 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:53:26.671 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:53:44.820 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:53:44.821 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:53:44.822 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:53:44.822 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:53:44.822 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:53:44.823 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:53:55.222 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-23 09:53:55.223 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-23 09:53:55.225 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-23 09:53:55.225 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-23 09:53:55.226 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-23 09:53:55.226 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-23 09:53:55.467 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-23 09:53:55.468 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-23 09:53:55.468 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-23 09:53:55.468 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-23 09:53:58.591 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=25, keyword=, startTime=null, endTime=null)
2025-06-23 09:53:58.591 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 25, keyword: , startTime: null, endTime: null
2025-06-23 09:53:58.592 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-23 09:53:58.592 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-23 09:53:59.260 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-23 09:53:59.261 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-23 09:53:59.261 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-23 09:53:59.262 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-23 09:53:59.262 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-23 09:53:59.262 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-23 09:53:59.263 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-23 09:53:59.263 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-23 09:53:59.264 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-23 09:53:59.264 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-23 09:54:00.063 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=28
2025-06-23 09:54:00.064 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 28
2025-06-23 09:54:00.066 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/25/28/simulation_script
2025-06-23 09:54:00.066 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/25/28/simulation_script, 共 0 个子指标
2025-06-23 09:54:00.067 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 1 个分类
2025-06-23 09:54:00.067 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 28, 指标数量: 1
2025-06-23 09:54:00.329 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=28
2025-06-23 09:54:00.422 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 28
2025-06-23 09:54:00.424 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/25/28/simulation_script
2025-06-23 09:54:00.424 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/25/28/simulation_script
2025-06-23 09:54:00.425 - INFO - [CsvUtils.java:371] : 目录中未找到CSV文件: D:\CETWorkSpace\ngap-server/data/25/28/simulation_script
2025-06-23 09:54:00.426 - WARN - [SimulationServiceImpl.java:1896] : 仿真任务 28 的输出目录中未找到电压数据，处理时间: 2ms
2025-06-23 09:54:00.426 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 28, 总数据点: 0, 合格数据点: 0, 合格率: 0.00%
2025-06-23 09:54:01.628 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:54:01.629 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:54:01.630 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:54:01.630 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:54:01.631 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:54:01.631 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:54:05.174 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-06-23 09:54:05.175 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-06-23 09:54:05.177 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-23 09:54:05.178 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-23 09:54:10.108 - INFO - [SimulationController.java:130] : 接收到获取仿真任务详情请求: simulationId=30
2025-06-23 09:54:10.109 - INFO - [SimulationServiceImpl.java:589] : 获取仿真任务详情成功: ID=30
2025-06-23 09:54:10.110 - INFO - [SimulationController.java:134] : 获取仿真任务详情成功，ID: 30
2025-06-23 09:54:10.276 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-23 09:54:10.277 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-23 09:54:10.280 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-23 09:54:10.280 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-23 09:54:10.281 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-23 09:54:10.282 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-23 09:54:10.282 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-23 09:54:10.282 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-23 09:54:10.283 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-23 09:54:10.283 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-23 09:54:13.530 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:54:13.530 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:54:13.532 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:54:13.533 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:54:13.533 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:54:13.533 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:54:26.347 - INFO - [SimulationController.java:102] : 接收到更新仿真任务请求: simulationId=30, simulationModel=, simulationDraft={"x":678,"y":247,"scale":0.97,"pens":[{"width":96.99999999999999,"height":96.99999999999999,"ratio":true,"name":"image","deviceType":"battery","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_163)'%3e%3crect%20x='3.5'%20y='0.5'%20width='17'%20height='23'%20stroke='black'/%3e%3cpath%20d='M1%208V16'%20stroke='black'/%3e%3cpath%20d='M23%208V16'%20stroke='black'/%3e%3cpath%20d='M10.3333%207H15.7879L12.4545%2011.2H17L9.72727%2019L11.2424%2013.3H7L10.3333%207Z'%20stroke='black'%20stroke-linejoin='round'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_163'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"386f5d5","x":0.5,"y":1,"penId":"8a39a6d"}],"simulationConfig":{"phases":3,"baseKV":10,"pu":1,"customScript":""},"id":"8a39a6d","children":[],"x":-256.*************,"y":-61.868481756338966,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"3e2fdfc","lineAnchor":"e0ceaf","anchor":"386f5d5"}],"ex":-159.*************2,"ey":35.13151824366102,"center":{"x":-207.**************,"y":-13.368481756338973}},{"width":96.99999999999999,"height":96.99999999999999,"ratio":true,"name":"image","deviceType":"transformer","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='12'%20cy='8'%20r='5.5'%20stroke='black'/%3e%3ccircle%20cx='12'%20cy='16'%20r='5.5'%20stroke='black'/%3e%3cpath%20d='M12%200V2'%20stroke='black'/%3e%3cpath%20d='M12%2022V24'%20stroke='black'/%3e%3c/svg%3e","anchors":[{"id":"1c3afb7f","x":0.5,"y":0,"background":"blue","flag":"wind1","penId":"25e213a7"},{"id":"fb474e0","x":0.5,"y":1,"background":"red","flag":"wind2","penId":"25e213a7"}],"simulationConfig":{"name":"","buses":[],"phases":3,"windings":2,"XHL":0.01,"customScript":"","bank":"","maxTap":1.1,"minTap":0.9,"numTaps":32,"percentLoadloss":0.2,"wdgs":[{"wdg":1,"buses":"","conn":"DELT","kv":10,"kva":200,"percentR":0.5,"tap":0},{"wdg":2,"buses":"","conn":"WYE","kv":0.4,"kva":200,"percentR":0.5,"tap":0}]},"id":"25e213a7","children":[],"x":-256.*************,"y":69.**************,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"3e2fdfc","lineAnchor":"ee8fa09","anchor":"1c3afb7f"}],"ex":-159.*************2,"ey":166.**************,"center":{"x":-207.**************,"y":118.**************}},{"id":"3e2fdfc","name":"line","lineName":"line","x":-207.**************,"y":35.***************,"type":1,"toArrow":"line","lineWidth":1,"length":34.**************,"ex":-207.**************,"ey":69.**************,"width":0,"height":34.**************,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"e0ceaf","x":0,"y":0,"penId":"3e2fdfc","connectTo":"8a39a6d","anchorId":"386f5d5","start":true,"lineLength":34.**************},{"x":0,"y":1,"id":"ee8fa09","penId":"3e2fdfc","connectTo":"25e213a7","anchorId":"1c3afb7f"}],"rotate":0,"center":{"x":-207.**************,"y":52.3365182436611}}],"origin":{"x":9.255728973407486,"y":9.426518243661132},"center":{"x":-246,"y":34},"paths":{},"template":"69f28d8","simulationConfig":{"name":"","number":24,"stepSize":"1h","mode":"daily","hour":0,"sec":0,"voltageBases":[0.22,0.38,10],"defaultBaseFrequency":60},"name":"meta2d.2025620170307","version":"1.0.68","dataPoints":[]}, simulationScript=, nodes=, controlStrategy={}
2025-06-23 09:54:26.349 - INFO - [SimulationServiceImpl.java:336] : 仿真任务draft更新: 30
2025-06-23 09:54:26.668 - INFO - [SimulationServiceImpl.java:380] : 仿真任务更新成功: ID=30
2025-06-23 09:54:26.668 - INFO - [SimulationController.java:108] : 仿真任务更新成功，ID: 30, 更新后模型: null, 更新后脚本: null
2025-06-23 09:54:30.953 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:54:30.954 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:54:30.956 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:54:30.957 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:54:30.958 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:54:30.958 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:54:35.383 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:54:35.383 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:54:35.384 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:54:35.384 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:54:35.385 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:54:35.385 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:54:36.033 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:54:36.034 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:54:36.036 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:54:36.036 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:54:36.037 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:54:36.037 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:54:41.192 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:54:41.192 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:54:41.194 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:54:41.195 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:54:41.195 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:54:41.196 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:54:50.457 - INFO - [SimulationController.java:102] : 接收到更新仿真任务请求: simulationId=30, simulationModel=, simulationDraft={"x":678,"y":247,"scale":0.95,"pens":[{"width":94.99999999999999,"height":94.99999999999999,"ratio":true,"name":"image","deviceType":"battery","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_163)'%3e%3crect%20x='3.5'%20y='0.5'%20width='17'%20height='23'%20stroke='black'/%3e%3cpath%20d='M1%208V16'%20stroke='black'/%3e%3cpath%20d='M23%208V16'%20stroke='black'/%3e%3cpath%20d='M10.3333%207H15.7879L12.4545%2011.2H17L9.72727%2019L11.2424%2013.3H7L10.3333%207Z'%20stroke='black'%20stroke-linejoin='round'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_163'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"386f5d5","x":0.5,"y":1,"penId":"8a39a6d"}],"simulationConfig":{"phases":3,"baseKV":10,"pu":1,"customScript":""},"id":"8a39a6d","children":[],"x":-524.*************,"y":-94.9053726832815,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"3e2fdfc","lineAnchor":"e0ceaf","anchor":"386f5d5"}],"ex":-159.*************,"ey":39.0875694138948,"center":{"x":-207.*************,"y":-8.412430586105195}},{"width":94.99999999999999,"height":94.99999999999999,"ratio":true,"name":"image","deviceType":"transformer","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='12'%20cy='8'%20r='5.5'%20stroke='black'/%3e%3ccircle%20cx='12'%20cy='16'%20r='5.5'%20stroke='black'/%3e%3cpath%20d='M12%200V2'%20stroke='black'/%3e%3cpath%20d='M12%2022V24'%20stroke='black'/%3e%3c/svg%3e","anchors":[{"id":"1c3afb7f","x":0.5,"y":0,"background":"blue","flag":"wind1","penId":"25e213a7"},{"id":"fb474e0","x":0.5,"y":1,"background":"red","flag":"wind2","penId":"25e213a7"}],"simulationConfig":{"name":"","buses":[],"phases":3,"windings":2,"XHL":0.01,"customScript":"","bank":"","maxTap":1.1,"minTap":0.9,"numTaps":32,"percentLoadloss":0.2,"wdgs":[{"wdg":1,"buses":"","conn":"DELT","kv":10,"kva":200,"percentR":0.5,"tap":0},{"wdg":2,"buses":"","conn":"WYE","kv":0.4,"kva":200,"percentR":0.5,"tap":0}]},"id":"25e213a7","children":[],"x":-524.*************,"y":33.***************,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"3e2fdfc","lineAnchor":"ee8fa09","anchor":"1c3afb7f"}],"ex":-159.*************,"ey":167.**************,"center":{"x":-207.*************,"y":120.**************}},{"id":"3e2fdfc","name":"line","lineName":"line","x":-477.*************7,"y":0.*****************,"type":1,"toArrow":"line","lineWidth":1,"length":33.**************,"ex":-477.*************7,"ey":33.**************,"width":0,"height":33.**************,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"e0ceaf","x":0,"y":0,"penId":"3e2fdfc","connectTo":"8a39a6d","anchorId":"386f5d5","start":true,"lineLength":33.**************},{"x":0,"y":1,"id":"ee8fa09","penId":"3e2fdfc","connectTo":"25e213a7","anchorId":"1c3afb7f"}],"rotate":0,"center":{"x":-207.*************7,"y":55.937827145853625}},{"anchors":[{"id":"ed9e431","x":0,"y":0.5,"background":"blue","penId":"5ab8fabb"},{"id":"e875ca0","x":1,"y":0.5,"penId":"5ab8fabb"}],"width":296,"height":5.75,"name":"rectangle","lineWidth":0,"background":"#222222","deviceType":"motherLine","simulationConfig":{"phases":3},"id":"5ab8fabb","children":[],"x":-513.9587628865979,"y":167.3305115110711,"fontSize":24,"lineHeight":1.5,"ex":-229.95876288659792,"ey":240.0734536082474,"center":{"x":-377.9587628865979,"y":237.1984536082474}}],"origin":{"x":5.497878891481548,"y":13.912569413894914},"center":{"x":-173,"y":227},"paths":{},"template":"69f28d8","simulationConfig":{"name":"","number":24,"stepSize":"1h","mode":"daily","hour":0,"sec":0,"voltageBases":[0.22,0.38,10],"defaultBaseFrequency":60},"name":"meta2d.2025620170307","version":"1.0.68","dataPoints":[]}, simulationScript=, nodes=, controlStrategy={}
2025-06-23 09:54:50.459 - INFO - [SimulationServiceImpl.java:336] : 仿真任务draft更新: 30
2025-06-23 09:54:50.653 - INFO - [SimulationServiceImpl.java:380] : 仿真任务更新成功: ID=30
2025-06-23 09:54:50.653 - INFO - [SimulationController.java:108] : 仿真任务更新成功，ID: 30, 更新后模型: null, 更新后脚本: null
2025-06-23 09:55:22.960 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:55:22.961 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:55:22.963 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:55:22.963 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:55:22.964 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:55:22.964 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:55:27.215 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:55:27.216 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:55:27.218 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:55:27.218 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:55:27.219 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:55:27.219 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:56:06.468 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:56:06.468 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:56:06.469 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:56:06.469 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:56:06.469 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:56:06.469 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:56:10.060 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:56:10.060 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:56:10.062 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:56:10.062 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:56:10.063 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:56:10.063 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:57:42.652 - INFO - [SimulationController.java:102] : 接收到更新仿真任务请求: simulationId=30, simulationModel=, simulationDraft={"x":678,"y":247,"scale":0.95,"pens":[{"width":94.99999999999999,"height":94.99999999999999,"ratio":true,"name":"image","deviceType":"battery","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_163)'%3e%3crect%20x='3.5'%20y='0.5'%20width='17'%20height='23'%20stroke='black'/%3e%3cpath%20d='M1%208V16'%20stroke='black'/%3e%3cpath%20d='M23%208V16'%20stroke='black'/%3e%3cpath%20d='M10.3333%207H15.7879L12.4545%2011.2H17L9.72727%2019L11.2424%2013.3H7L10.3333%207Z'%20stroke='black'%20stroke-linejoin='round'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_163'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"386f5d5","x":0.5,"y":1,"penId":"8a39a6d"}],"simulationConfig":{"phases":3,"baseKV":10,"pu":1,"customScript":""},"id":"8a39a6d","children":[],"x":-524.*************,"y":-94.9053726832815,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"3e2fdfc","lineAnchor":"e0ceaf","anchor":"386f5d5"}],"ex":-159.*************,"ey":39.0875694138948,"center":{"x":-207.*************,"y":-8.412430586105195}},{"width":94.99999999999999,"height":94.99999999999999,"ratio":true,"name":"image","deviceType":"transformer","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='12'%20cy='8'%20r='5.5'%20stroke='black'/%3e%3ccircle%20cx='12'%20cy='16'%20r='5.5'%20stroke='black'/%3e%3cpath%20d='M12%200V2'%20stroke='black'/%3e%3cpath%20d='M12%2022V24'%20stroke='black'/%3e%3c/svg%3e","anchors":[{"id":"1c3afb7f","x":0.5,"y":0,"background":"blue","flag":"wind1","penId":"25e213a7"},{"id":"fb474e0","x":0.5,"y":1,"background":"red","flag":"wind2","penId":"25e213a7"}],"simulationConfig":{"name":"","buses":[],"phases":3,"windings":2,"XHL":0.01,"customScript":"","bank":"","maxTap":1.1,"minTap":0.9,"numTaps":32,"percentLoadloss":0.2,"wdgs":[{"wdg":1,"buses":"","conn":"DELT","kv":10,"kva":200,"percentR":0.5,"tap":0},{"wdg":2,"buses":"","conn":"WYE","kv":0.4,"kva":200,"percentR":0.5,"tap":0}]},"id":"25e213a7","children":[],"x":-524.*************,"y":33.***************,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"3e2fdfc","lineAnchor":"ee8fa09","anchor":"1c3afb7f"}],"ex":-159.*************,"ey":167.**************,"center":{"x":-207.*************,"y":120.**************}},{"id":"3e2fdfc","name":"line","lineName":"line","x":-477.*************7,"y":0.*****************,"type":1,"toArrow":"line","lineWidth":1,"length":33.**************,"ex":-477.*************7,"ey":33.**************,"width":0,"height":33.**************,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"e0ceaf","x":0,"y":0,"penId":"3e2fdfc","connectTo":"8a39a6d","anchorId":"386f5d5","start":true,"lineLength":33.**************},{"x":0,"y":1,"id":"ee8fa09","penId":"3e2fdfc","connectTo":"25e213a7","anchorId":"1c3afb7f"}],"rotate":0,"center":{"x":-207.*************7,"y":55.937827145853625}},{"anchors":[{"id":"ed9e431","x":0,"y":0.5,"background":"blue","penId":"5ab8fabb"},{"id":"e875ca0","x":1,"y":0.5,"penId":"5ab8fabb"}],"width":296,"height":5.75,"name":"rectangle","lineWidth":0,"background":"#222222","deviceType":"motherLine","simulationConfig":{"phases":3},"id":"5ab8fabb","children":[],"x":-597.9587628865979,"y":164.4555115110711,"fontSize":24,"lineHeight":1.5,"ex":-229.95876288659792,"ey":240.0734536082474,"center":{"x":-377.9587628865979,"y":237.1984536082474}}],"origin":{"x":5.497878891481548,"y":13.912569413894914},"center":{"x":-173,"y":227},"paths":{},"template":"69f28d8","simulationConfig":{"name":"","number":24,"stepSize":"1h","mode":"daily","hour":0,"sec":0,"voltageBases":[0.22,0.38,10],"defaultBaseFrequency":60},"name":"meta2d.2025620170307","version":"1.0.68","dataPoints":[]}, simulationScript=, nodes=, controlStrategy={}
2025-06-23 09:57:42.656 - INFO - [SimulationServiceImpl.java:336] : 仿真任务draft更新: 30
2025-06-23 09:57:42.747 - INFO - [SimulationServiceImpl.java:380] : 仿真任务更新成功: ID=30
2025-06-23 09:57:42.748 - INFO - [SimulationController.java:108] : 仿真任务更新成功，ID: 30, 更新后模型: null, 更新后脚本: null
2025-06-23 09:58:08.025 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:58:08.025 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:58:08.027 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:58:08.027 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:58:08.028 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:58:08.028 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:58:41.910 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:58:41.911 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:58:41.912 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:58:41.913 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:58:41.914 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:58:41.914 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:58:45.648 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:58:45.649 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:58:45.650 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:58:45.650 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:58:45.651 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:58:45.651 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:59:20.964 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:59:20.964 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:59:20.967 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:59:20.967 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:59:20.968 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:59:20.969 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 09:59:24.969 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 09:59:24.969 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 09:59:24.971 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 09:59:24.972 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 09:59:24.973 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 09:59:24.973 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:00:01.168 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:00:01.168 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:00:01.168 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:00:01.169 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:00:01.169 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:00:01.169 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:00:04.829 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:00:04.830 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:00:04.830 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:00:04.830 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:00:04.831 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:00:04.831 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:00:46.441 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:00:46.442 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:00:46.443 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:00:46.444 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:00:46.444 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:00:46.444 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:00:50.161 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:00:50.162 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:00:50.164 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:00:50.164 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:00:50.164 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:00:50.164 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:01:31.635 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-23 10:01:31.635 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-23 10:01:31.635 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-23 10:01:31.635 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-23 10:01:31.636 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-23 10:01:31.636 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-23 10:01:31.636 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-23 10:01:31.636 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-23 10:01:31.637 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-23 10:01:31.637 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-23 10:01:34.485 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=25, keyword=, startTime=null, endTime=null)
2025-06-23 10:01:34.486 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 25, keyword: , startTime: null, endTime: null
2025-06-23 10:01:34.488 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-23 10:01:34.489 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-23 10:01:37.532 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-23 10:01:37.532 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-23 10:01:37.533 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-23 10:01:37.533 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-23 10:01:37.534 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-23 10:01:37.535 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-23 10:01:37.537 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-23 10:01:37.538 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-23 10:01:37.539 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-23 10:01:37.539 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-23 10:01:42.326 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=11, keyword=, startTime=null, endTime=null)
2025-06-23 10:01:42.327 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 11, keyword: , startTime: null, endTime: null
2025-06-23 10:01:42.499 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-23 10:01:42.500 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-23 10:01:46.236 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=2
2025-06-23 10:01:46.236 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 2
2025-06-23 10:01:46.239 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 10:01:46.308 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script, 共 8 个子指标
2025-06-23 10:01:46.309 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/11/2/measured_data/太坪03柱上变压器.xlsx
2025-06-23 10:01:46.346 - INFO - [ExcelCacheManager.java:51] : Excel缓存管理器初始化完成，过期时间: 1800000ms
2025-06-23 10:01:46.346 - INFO - [VoltageObjectPool.java:46] : 电压对象池初始化完成
2025-06-23 10:01:46.347 - INFO - [VoltagePerformanceMonitor.java:28] : 电压性能监控器初始化完成
2025-06-23 10:01:47.537 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 太坪03柱上变压器.xlsx 的电压指标，共 1 个指标
2025-06-23 10:01:47.538 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-23 10:01:47.538 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 2, 指标数量: 2
2025-06-23 10:01:47.592 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=2
2025-06-23 10:01:47.592 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 2
2025-06-23 10:01:47.594 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 10:01:47.594 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 10:01:47.594 - INFO - [CsvUtils.java:375] : 找到 8 个CSV文件，开始处理
2025-06-23 10:01:47.601 - INFO - [CsvUtils.java:389] : 完成电压数据提取，处理文件数: 8/8, 总数据点: 504, 合格数据点: 288, 合格率: 57.14%
2025-06-23 10:01:47.602 - INFO - [SimulationServiceImpl.java:1914] : 仿真任务 2 电压合格率计算完成：总数据点=504, 合格数据点=288, 合格率=57.14%, 处理时间=8ms
2025-06-23 10:01:47.602 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 2, 总数据点: 504, 合格数据点: 288, 合格率: 57.14%
2025-06-23 10:01:54.541 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:01:54.541 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:01:54.542 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:01:54.542 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:01:54.543 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:01:54.543 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:01:58.630 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:01:58.631 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:01:58.633 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:01:58.633 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:01:58.635 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:01:58.636 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:02:29.084 - INFO - [SimulationController.java:130] : 接收到获取仿真任务详情请求: simulationId=25
2025-06-23 10:02:29.089 - INFO - [SimulationServiceImpl.java:589] : 获取仿真任务详情成功: ID=25
2025-06-23 10:02:29.089 - INFO - [SimulationController.java:134] : 获取仿真任务详情成功，ID: 25
2025-06-23 10:02:41.510 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=14
2025-06-23 10:02:41.511 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 14
2025-06-23 10:02:41.580 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/21/14/simulation_script
2025-06-23 10:02:41.581 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/21/14/simulation_script
2025-06-23 10:02:41.582 - INFO - [CsvUtils.java:375] : 找到 194 个CSV文件，开始处理
2025-06-23 10:02:43.488 - INFO - [CsvUtils.java:389] : 完成电压数据提取，处理文件数: 194/194, 总数据点: 20256, 合格数据点: 18618, 合格率: 91.91%
2025-06-23 10:02:43.489 - INFO - [SimulationServiceImpl.java:1914] : 仿真任务 14 电压合格率计算完成：总数据点=20256, 合格数据点=18618, 合格率=91.91%, 处理时间=1908ms
2025-06-23 10:02:43.489 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 14, 总数据点: 20256, 合格数据点: 18618, 合格率: 91.91%
2025-06-23 10:02:43.739 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:02:43.739 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:02:43.740 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:02:43.740 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:02:43.741 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:02:43.741 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:02:47.365 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:02:47.366 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:02:47.367 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:02:47.367 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:02:47.368 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:02:47.368 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:02:55.052 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:02:55.053 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:02:55.054 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:02:55.055 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:02:55.055 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:02:55.056 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:03:06.472 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:03:06.472 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:03:06.474 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:03:06.474 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:03:06.475 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:03:06.476 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:05:55.365 - INFO - [SimulationController.java:170] : 接收到运行仿真任务请求: simulationId=30
2025-06-23 10:05:55.366 - INFO - [SimulationServiceImpl.java:821] : 开始运行仿真任务，仿真ID: 30
2025-06-23 10:05:55.368 -ERROR - [SimulationServiceImpl.java:850] : 运行仿真任务失败：DSS文件不存在，dssFilePath: D:/CETWorkSpace/ngap-server/data/26/30/simulation_script/simulationScript.dss
2025-06-23 10:05:55.398 -ERROR - [GlobalExceptionHandler.java:22] : 运行仿真任务失败：DSS脚本未保存
com.cet.electric.ngapserver.service.impl.SimulationServiceImpl.runSimulation(SimulationServiceImpl.java:851)
com.cet.electric.ngapserver.web.controller.SimulationController.runSimulation(SimulationController.java:174)
java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
java.base/java.lang.reflect.Method.invoke(Method.java:580)
org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-23 10:05:57.992 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:05:57.993 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:05:57.995 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:05:57.995 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:05:57.996 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:05:57.997 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:06:08.130 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:06:08.130 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:06:08.131 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:06:08.131 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:06:08.131 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:06:08.132 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:06:08.226 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:06:08.226 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:06:08.228 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:06:08.229 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:06:08.229 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:06:08.230 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:06:08.321 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:06:08.321 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:06:08.322 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:06:08.323 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:06:08.323 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:06:08.324 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:06:08.356 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:06:08.356 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:06:08.357 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:06:08.358 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:06:08.359 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:06:08.359 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:06:08.422 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:06:08.422 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:06:08.423 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:06:08.423 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:06:08.424 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:06:08.424 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:06:08.451 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:06:08.451 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:06:08.452 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:06:08.452 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:06:08.453 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:06:08.453 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:06:08.514 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:06:08.514 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:06:08.516 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:06:08.517 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:06:08.517 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:06:08.518 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:06:08.519 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:06:08.519 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:06:08.519 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:06:08.521 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:06:08.522 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:06:08.522 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:06:08.544 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:06:08.544 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:06:08.546 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:06:08.547 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:06:08.547 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:06:08.548 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:06:08.637 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:06:08.637 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:06:08.638 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:06:08.638 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:06:08.638 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:06:08.638 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:06:28.623 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:06:28.623 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:06:28.625 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:06:28.625 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:06:28.625 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:06:28.626 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:06:31.963 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:06:31.964 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:06:31.966 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:06:31.966 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:06:31.967 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:06:31.969 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:07:22.346 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:07:22.347 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:07:22.349 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:07:22.349 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:07:22.350 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:07:22.350 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:07:25.407 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:07:25.407 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:07:25.409 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:07:25.410 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:07:25.410 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:07:25.410 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:07:30.041 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:07:30.041 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:07:30.043 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:07:30.043 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:07:30.044 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:07:30.044 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:08:19.501 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:08:19.501 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:08:19.503 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:08:19.503 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:08:19.504 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:08:19.504 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:08:23.372 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:08:23.373 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:08:23.374 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:08:23.374 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:08:23.375 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:08:23.375 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:22:36.682 - INFO - [SimulationController.java:102] : 接收到更新仿真任务请求: simulationId=30, simulationModel=, simulationDraft={"x":678,"y":247,"scale":0.8299999999999998,"pens":[{"width":82.99999999999999,"height":82.99999999999999,"ratio":true,"name":"image","deviceType":"battery","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_163)'%3e%3crect%20x='3.5'%20y='0.5'%20width='17'%20height='23'%20stroke='black'/%3e%3cpath%20d='M1%208V16'%20stroke='black'/%3e%3cpath%20d='M23%208V16'%20stroke='black'/%3e%3cpath%20d='M10.3333%207H15.7879L12.4545%2011.2H17L9.72727%2019L11.2424%2013.3H7L10.3333%207Z'%20stroke='black'%20stroke-linejoin='round'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_163'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"386f5d5","x":0.5,"y":1,"penId":"8a39a6d"}],"simulationConfig":{"phases":3,"baseKV":10,"pu":1,"customScript":""},"id":"8a39a6d","children":[],"x":-477.**************,"y":-180.81551247674747,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"3e2fdfc","lineAnchor":"e0ceaf","anchor":"386f5d5"}],"ex":-394.**************,"ey":-97.81551247674749,"center":{"x":-435.**************,"y":-139.31551247674747}},{"width":82.99999999999999,"height":82.99999999999999,"ratio":true,"name":"image","deviceType":"transformer","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='12'%20cy='8'%20r='5.5'%20stroke='black'/%3e%3ccircle%20cx='12'%20cy='16'%20r='5.5'%20stroke='black'/%3e%3cpath%20d='M12%200V2'%20stroke='black'/%3e%3cpath%20d='M12%2022V24'%20stroke='black'/%3e%3c/svg%3e","anchors":[{"id":"1c3afb7f","x":0.5,"y":0,"background":"blue","flag":"wind1","penId":"25e213a7"},{"id":"fb474e0","x":0.5,"y":1,"background":"red","flag":"wind2","penId":"25e213a7"}],"simulationConfig":{"name":"","buses":[],"phases":3,"windings":2,"XHL":0.01,"customScript":"","bank":"","maxTap":1.1,"minTap":0.9,"numTaps":32,"percentLoadloss":0.2,"wdgs":[{"wdg":1,"buses":"","conn":"DELT","kv":10,"kva":200,"percentR":0.5,"tap":0,"node":{"name":"node_1"}},{"wdg":2,"buses":"","conn":"WYE","kv":0.4,"kva":200,"percentR":0.5,"tap":0,"node":{"name":"node_2"}}]},"id":"25e213a7","children":[],"x":-477.**************,"y":-68.**************,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"3e2fdfc","lineAnchor":"ee8fa09","anchor":"1c3afb7f"},{"lineId":"16b3dda8","lineAnchor":"e3dc598","anchor":"fb474e0"}],"ex":-394.**************,"ey":14.**************,"center":{"x":-435.**************,"y":-26.***************},"textTop":60,"text":"Transformer_1"},{"id":"3e2fdfc","name":"line","lineName":"line","x":-435.*************,"y":-97.**************,"type":1,"toArrow":"line","lineWidth":1,"length":29.***************,"ex":-435.*************,"ey":-68.**************,"width":0,"height":29.***************,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"e0ceaf","x":0,"y":0,"penId":"3e2fdfc","connectTo":"8a39a6d","anchorId":"386f5d5","start":true,"lineLength":29.***************},{"x":0,"y":1,"id":"ee8fa09","penId":"3e2fdfc","connectTo":"25e213a7","anchorId":"1c3afb7f"}],"rotate":0,"center":{"x":-435.*************,"y":-83.093708353036}},{"anchors":[{"id":"ed9e431","x":0,"y":0.5,"background":"blue","penId":"5ab8fabb"},{"id":"e875ca0","x":1,"y":0.5,"penId":"5ab8fabb"}],"width":227.15789473684222,"height":5.0236842105263175,"name":"rectangle","lineWidth":0,"background":"#222222","deviceType":"motherLine","simulationConfig":{"phases":3},"id":"5ab8fabb","children":[],"x":-405.70391606707994,"y":38.24847055621325,"fontSize":24,"lineHeight":1.5,"ex":-178.54602133023772,"ey":43.27215476673957,"center":{"x":-292.12496869865885,"y":40.760312661476405},"connectedLines":[{"lineId":"16b3dda8","lineAnchor":"f5d31b0","anchor":"ed9e431"},{"lineId":"d1cceaf","lineAnchor":"020958b","anchor":"ed9e431"}]},{"anchors":[{"id":"ed9e431","x":0,"y":0.5,"background":"blue","penId":"24edd86e"},{"id":"e875ca0","x":1,"y":0.5,"penId":"24edd86e"}],"width":227.15789473684222,"height":5.0236842105263175,"name":"rectangle","lineWidth":0,"background":"#222222","deviceType":"motherLine","simulationConfig":{"phases":3},"id":"24edd86e","children":[],"x":-546.7334876400244,"y":181.96952318779222,"fontSize":24,"lineHeight":1.5,"ex":-319.57559290318216,"ey":186.99320739831853,"center":{"x":-433.15454027160325,"y":184.48136529305538},"rotate":90,"connectedLines":[{"lineId":"d1cceaf","lineAnchor":"04f46ba","anchor":"ed9e431"}]},{"id":"16b3dda8","name":"line","lineName":"line","x":-435.6663823768665,"y":14.628095770675316,"type":1,"toArrow":"line","lineWidth":1,"length":39.75729048857504,"ex":-405.7039160670804,"ey":40.76031266147642,"width":29.962466309786123,"height":26.132216890801104,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"e3dc598","x":0,"y":0,"penId":"16b3dda8","connectTo":"25e213a7","anchorId":"fb474e0","start":true,"lineLength":39.75729048857504},{"x":1,"y":1,"id":"f5d31b0","penId":"16b3dda8","connectTo":"5ab8fabb","anchorId":"ed9e431"}],"center":{"x":-420.68514922197346,"y":27.694204216075867}},{"id":"d1cceaf","name":"line","lineName":"line","x":-433.1545402716034,"y":40.76031266147646,"type":1,"toArrow":"line","lineWidth":1,"length":40.76865559364498,"ex":-405.7039160670803,"ey":70.90241792463432,"width":27.450624204523137,"height":30.14210526315786,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"020958b","x":1,"y":0,"penId":"d1cceaf","connectTo":"5ab8fabb","anchorId":"ed9e431","start":true,"lineLength":40.76865559364498},{"x":0,"y":1,"id":"04f46ba","penId":"d1cceaf","connectTo":"24edd86e","anchorId":"ed9e431"}],"center":{"x":-419.42922816934185,"y":55.83136529305539}}],"origin":{"x":-13.894429039915906,"y":3.372794829206697},"center":{"x":-170,"y":-99},"paths":{},"template":"69f28d8","simulationConfig":{"name":"","number":24,"stepSize":"1h","mode":"daily","hour":0,"sec":0,"voltageBases":[0.22,0.38,10],"defaultBaseFrequency":60},"name":"meta2d.2025620170307","version":"1.0.68","dataPoints":[]}, simulationScript=, nodes=, controlStrategy={}
2025-06-23 10:22:36.685 - INFO - [SimulationServiceImpl.java:336] : 仿真任务draft更新: 30
2025-06-23 10:22:36.865 - INFO - [SimulationServiceImpl.java:380] : 仿真任务更新成功: ID=30
2025-06-23 10:22:36.865 - INFO - [SimulationController.java:108] : 仿真任务更新成功，ID: 30, 更新后模型: null, 更新后脚本: null
2025-06-23 10:22:37.070 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-23 10:22:37.071 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-23 10:22:37.071 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-23 10:22:37.071 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-23 10:22:37.071 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-23 10:22:37.071 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-23 10:22:37.073 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-23 10:22:37.073 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-23 10:22:37.073 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-23 10:22:37.074 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-23 10:22:39.243 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=25, keyword=, startTime=null, endTime=null)
2025-06-23 10:22:39.243 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 25, keyword: , startTime: null, endTime: null
2025-06-23 10:22:39.245 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-23 10:22:39.245 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-23 10:22:42.260 - INFO - [SimulationController.java:130] : 接收到获取仿真任务详情请求: simulationId=28
2025-06-23 10:22:42.261 - INFO - [SimulationServiceImpl.java:589] : 获取仿真任务详情成功: ID=28
2025-06-23 10:22:42.261 - INFO - [SimulationController.java:134] : 获取仿真任务详情成功，ID: 28
2025-06-23 10:22:54.777 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-23 10:22:54.777 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-23 10:22:54.777 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-23 10:22:54.777 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-23 10:22:54.777 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-23 10:22:54.777 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-23 10:22:54.777 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-23 10:22:54.777 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-23 10:22:54.778 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-23 10:22:54.778 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-23 10:22:55.837 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=26, keyword=, startTime=null, endTime=null)
2025-06-23 10:22:55.838 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 26, keyword: , startTime: null, endTime: null
2025-06-23 10:22:55.839 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-23 10:22:55.840 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-23 10:22:59.218 - INFO - [SimulationController.java:130] : 接收到获取仿真任务详情请求: simulationId=30
2025-06-23 10:22:59.220 - INFO - [SimulationServiceImpl.java:589] : 获取仿真任务详情成功: ID=30
2025-06-23 10:22:59.220 - INFO - [SimulationController.java:134] : 获取仿真任务详情成功，ID: 30
2025-06-23 10:23:05.313 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:23:05.313 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:23:05.314 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:23:05.314 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:23:05.314 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:23:05.314 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:23:06.714 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:23:06.715 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:23:06.716 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:23:06.717 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:23:06.717 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:23:06.718 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:23:10.054 - INFO - [SimulationController.java:102] : 接收到更新仿真任务请求: simulationId=30, simulationModel=, simulationDraft={"x":0,"y":0,"scale":1,"pens":[{"width":100,"height":100,"ratio":true,"name":"image","deviceType":"battery","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_163)'%3e%3crect%20x='3.5'%20y='0.5'%20width='17'%20height='23'%20stroke='black'/%3e%3cpath%20d='M1%208V16'%20stroke='black'/%3e%3cpath%20d='M23%208V16'%20stroke='black'/%3e%3cpath%20d='M10.3333%207H15.7879L12.4545%2011.2H17L9.72727%2019L11.2424%2013.3H7L10.3333%207Z'%20stroke='black'%20stroke-linejoin='round'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_163'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"1666a7d","x":0.5,"y":1,"penId":"5cef529"}],"simulationConfig":{"phases":3,"baseKV":0,"pu":1,"customScript":""},"id":"5cef529","children":[],"x":468,"y":143,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0},{"width":100,"height":100,"ratio":true,"name":"image","deviceType":"load","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_157)'%3e%3ccircle%20cx='12'%20cy='11.5'%20r='11'%20stroke='black'/%3e%3cpath%20d='M4%204L20%2019'%20stroke='black'/%3e%3cpath%20d='M4%2019L20%204'%20stroke='black'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_157'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"b9e3706","x":0.5,"y":0,"penId":"1ee2a52a"}],"simulationConfig":{"name":"","buses":[],"phases":3,"phasePositions":[],"kw":1,"kvar":0,"kv":12.47,"model":1,"customScript":"","outputMode":["VI","PQ"]},"loadShapeConfig":{"name":"","mult":[],"interval":1,"divisor":1},"id":"1ee2a52a","children":[],"x":433,"y":286,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0}],"origin":{"x":0,"y":0},"center":{"x":0,"y":0},"paths":{},"template":"454b508d","component":false,"autoSizeinPc":true,"rule":true,"ruleColor":"#888888","gridColor":"#e2e2e2","gridSize":20,"color":"#222222","activeColor":"#278df8","toArrow":"line","simulationConfig":{"name":"","number":24,"stepSize":"1h","mode":"daily","hour":0,"sec":0,"voltageBases":[0.22,0.38,10],"defaultBaseFrequency":60},"version":"1.0.68","dataPoints":[]}, simulationScript=, nodes=, controlStrategy={}
2025-06-23 10:23:10.055 - INFO - [SimulationServiceImpl.java:336] : 仿真任务draft更新: 30
2025-06-23 10:23:10.546 - INFO - [SimulationServiceImpl.java:380] : 仿真任务更新成功: ID=30
2025-06-23 10:23:10.547 - INFO - [SimulationController.java:108] : 仿真任务更新成功，ID: 30, 更新后模型: null, 更新后脚本: null
2025-06-23 10:23:22.858 - INFO - [SimulationController.java:102] : 接收到更新仿真任务请求: simulationId=30, simulationModel=, simulationDraft={"x":0,"y":0,"scale":1,"pens":[{"width":100,"height":100,"ratio":true,"name":"image","deviceType":"battery","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_163)'%3e%3crect%20x='3.5'%20y='0.5'%20width='17'%20height='23'%20stroke='black'/%3e%3cpath%20d='M1%208V16'%20stroke='black'/%3e%3cpath%20d='M23%208V16'%20stroke='black'/%3e%3cpath%20d='M10.3333%207H15.7879L12.4545%2011.2H17L9.72727%2019L11.2424%2013.3H7L10.3333%207Z'%20stroke='black'%20stroke-linejoin='round'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_163'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"1666a7d","x":0.5,"y":1,"penId":"5cef529"}],"simulationConfig":{"phases":3,"baseKV":0,"pu":1,"customScript":""},"id":"5cef529","children":[],"x":468,"y":143,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0},{"width":100,"height":100,"ratio":true,"name":"image","deviceType":"load","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_157)'%3e%3ccircle%20cx='12'%20cy='11.5'%20r='11'%20stroke='black'/%3e%3cpath%20d='M4%204L20%2019'%20stroke='black'/%3e%3cpath%20d='M4%2019L20%204'%20stroke='black'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_157'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"b9e3706","x":0.5,"y":0,"penId":"1ee2a52a"}],"simulationConfig":{"name":"","buses":[],"phases":3,"phasePositions":[],"kw":1,"kvar":0,"kv":12.47,"model":1,"customScript":"","outputMode":["VI","PQ"]},"loadShapeConfig":{"name":"","mult":[],"interval":1,"divisor":1},"id":"1ee2a52a","children":[],"x":433,"y":286,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0}],"origin":{"x":0,"y":0},"center":{"x":0,"y":0},"paths":{},"template":"454b508d","component":false,"autoSizeinPc":true,"rule":true,"ruleColor":"#888888","gridColor":"#e2e2e2","gridSize":20,"color":"#222222","activeColor":"#278df8","toArrow":"line","simulationConfig":{"name":"","number":24,"stepSize":"1h","mode":"daily","hour":0,"sec":0,"voltageBases":[0.22,0.38,10],"defaultBaseFrequency":60},"version":"1.0.68","dataPoints":[]}, simulationScript=, nodes=, controlStrategy={}
2025-06-23 10:23:22.860 - INFO - [SimulationServiceImpl.java:364] : 仿真任务没有变更，无需更新: 30
2025-06-23 10:23:22.861 - INFO - [SimulationController.java:108] : 仿真任务更新成功，ID: 30, 更新后模型: null, 更新后脚本: null
2025-06-23 10:25:55.717 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=23, keyword=, startTime=null, endTime=null)
2025-06-23 10:25:55.717 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 23, keyword: , startTime: null, endTime: null
2025-06-23 10:25:55.719 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-23 10:25:55.719 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-23 10:25:59.942 - INFO - [SimulationController.java:130] : 接收到获取仿真任务详情请求: simulationId=25
2025-06-23 10:25:59.944 - INFO - [SimulationServiceImpl.java:589] : 获取仿真任务详情成功: ID=25
2025-06-23 10:25:59.944 - INFO - [SimulationController.java:134] : 获取仿真任务详情成功，ID: 25
2025-06-23 10:26:05.123 - INFO - [SimulationController.java:102] : 接收到更新仿真任务请求: simulationId=25, simulationModel=, simulationDraft={"x":216,"y":-139,"scale":1,"pens":[{"width":100,"height":100,"ratio":true,"name":"image","deviceType":"battery","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_163)'%3e%3crect%20x='3.5'%20y='0.5'%20width='17'%20height='23'%20stroke='black'/%3e%3cpath%20d='M1%208V16'%20stroke='black'/%3e%3cpath%20d='M23%208V16'%20stroke='black'/%3e%3cpath%20d='M10.3333%207H15.7879L12.4545%2011.2H17L9.72727%2019L11.2424%2013.3H7L10.3333%207Z'%20stroke='black'%20stroke-linejoin='round'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_163'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"8b5e05f","x":0.5,"y":1,"penId":"9db96ca"}],"simulationConfig":{"phases":3,"baseKV":0,"pu":1,"customScript":""},"id":"9db96ca","children":[],"x":384.*************,"y":39.25,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"75ce3d2","lineAnchor":"dffe2d2","anchor":"8b5e05f"}],"ex":484.*************,"ey":139.25,"center":{"x":434.*************,"y":89.25}},{"width":100,"height":100,"ratio":true,"name":"image","deviceType":"regulator","image":"data:image/svg+xml,%3csvg%20width='25'%20height='25'%20viewBox='0%200%2025%2025'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='12.1406'%20cy='15.1758'%20r='5.47461'%20stroke='black'/%3e%3ccircle%20cx='12.1406'%20cy='10.0381'%20r='5.47461'%20transform='rotate(-180%2012.1406%2010.0381)'%20stroke='black'/%3e%3cpath%20d='M12.1406%2023.7734V21.2559'%20stroke='black'%20stroke-linecap='square'/%3e%3cpath%20d='M12.1406%201.44043L12.1406%203.95801'%20stroke='black'%20stroke-linecap='square'/%3e%3cpath%20d='M1.00276%2019.1217L0.593417%2019.4098L1.15728%2020.2342L1.56662%2019.9461L1.00276%2019.1217ZM23.481%204.33965C23.5307%204.06614%2023.3522%203.80515%2023.0823%203.75671L18.683%202.9674C18.4131%202.91897%2018.1539%203.10143%2018.1041%203.37494C18.0544%203.64846%2018.2329%203.90945%2018.5029%203.95788L22.4133%204.65949L21.6926%208.62141C21.6428%208.89492%2021.8213%209.15591%2022.0913%209.20435C22.3612%209.25278%2022.6204%209.07032%2022.6702%208.79681L23.481%204.33965ZM1.28469%2019.5339L1.56662%2019.9461L23.2741%204.66415L22.9922%204.25195L22.7103%203.83975L1.00276%2019.1217L1.28469%2019.5339Z'%20fill='black'/%3e%3c/svg%3e","anchors":[{"id":"7e38071d","x":0.5,"y":0,"background":"blue","flag":"wind1","penId":"28aaa06f"},{"id":"52d5607c","x":0.5,"y":1,"background":"red","flag":"wind2","penId":"28aaa06f"}],"simulationConfig":{"name":"","buses":[],"phases":3,"windings":2,"XHL":0,"customScript":"","bank":"","maxTap":1.1,"minTap":0.9,"numTaps":32,"percentLoadloss":0,"wdgs":[{"wdg":1,"buses":"","conn":"WYE","kv":12.47,"kva":0,"percentR":0,"tap":0,"node":{"name":"node_1"}},{"wdg":2,"buses":"","conn":"WYE","kv":12.47,"kva":0,"percentR":0,"tap":0,"node":{"name":"node_3"}}],"control":{"name":"","transformer":"","winding":1,"vreg":120,"band":3,"ptratio":60,"enabled":true}},"id":"28aaa06f","children":[],"x":394.*************,"y":210.25,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"75ce3d2","lineAnchor":"05cb015","anchor":"7e38071d"},{"lineId":"29323c22","lineAnchor":"5083a561","anchor":"52d5607c"}],"textTop":60,"text":"Regulator_1","ex":494.*************,"ey":310.25,"center":{"x":444.*************,"y":260.25}},{"id":"75ce3d2","name":"line","lineName":"line","x":434.75,"y":139.25,"type":1,"toArrow":"line","lineWidth":1,"length":71.*************,"ex":444.75,"ey":210.25,"width":10,"height":71,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"dffe2d2","x":0,"y":0,"penId":"75ce3d2","connectTo":"9db96ca","anchorId":"8b5e05f","start":true,"lineLength":71.*************},{"x":1,"y":1,"id":"05cb015","penId":"75ce3d2","connectTo":"28aaa06f","anchorId":"7e38071d"}],"rotate":0,"center":{"x":439.75,"y":174.75}},{"anchors":[{"id":"0ecc036","x":0,"y":0.5,"background":"blue","penId":"63feb73"},{"id":"643ce97","x":1,"y":0.5,"penId":"63feb73"},{"id":"23db7269","penId":"63feb73","x":0.5437499999999998,"y":0}],"width":600.0000000000001,"height":5,"name":"rectangle","lineWidth":0,"background":"#222222","deviceType":"motherLine","simulationConfig":{"phases":3},"id":"63feb73","children":[],"x":118.50000000000006,"y":376.75,"fontSize":24,"lineHeight":1.5,"rotate":0,"connectedLines":[{"lineId":"29323c22","lineAnchor":"d5f2f4","anchor":"23db7269"}],"ex":792.*************,"ey":392.75,"center":{"x":492.75000000000006,"y":390.25}},{"id":"29323c22","name":"line","lineName":"line","x":444.75,"y":310.25,"type":1,"toArrow":"line","lineWidth":1,"length":66.5,"ex":444.*************,"ey":376.75,"width":1.1368683772161603e-13,"height":66.5,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"5083a561","x":1,"y":0,"penId":"29323c22","connectTo":"28aaa06f","anchorId":"52d5607c","start":true,"lineLength":66.5},{"x":0,"y":1,"id":"d5f2f4","penId":"29323c22","connectTo":"63feb73","anchorId":"23db7269"}],"rotate":0,"center":{"x":318.75000000000006,"y":350.25},"lastConnected":{"63feb73":[{"lineId":"29323c22","lineAnchor":"d5f2f4","anchor":"0ecc036"}]}}],"origin":{"x":-2.2500000000001137,"y":-57.*************14},"center":{"x":490,"y":439},"paths":{},"template":"0f0305f","component":false,"autoSizeinPc":true,"rule":true,"ruleColor":"#888888","gridColor":"#e2e2e2","gridSize":20,"color":"#222222","activeColor":"#278df8","toArrow":"line","simulationConfig":{"name":"","number":24,"stepSize":"1h","mode":"daily","hour":0,"sec":0,"voltageBases":[0.22,0.38,10],"defaultBaseFrequency":60},"version":"1.0.68","dataPoints":[]}, simulationScript=, nodes=, controlStrategy={}
2025-06-23 10:26:05.125 - INFO - [SimulationServiceImpl.java:336] : 仿真任务draft更新: 25
2025-06-23 10:26:05.294 - INFO - [SimulationServiceImpl.java:380] : 仿真任务更新成功: ID=25
2025-06-23 10:26:05.294 - INFO - [SimulationController.java:108] : 仿真任务更新成功，ID: 25, 更新后模型: {"x":140,"y":21,"scale":1,"pens":[{"width":100,"height":100,"ratio":true,"name":"image","deviceType":"battery","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_163)'%3e%3crect%20x='3.5'%20y='0.5'%20width='17'%20height='23'%20stroke='black'/%3e%3cpath%20d='M1%208V16'%20stroke='black'/%3e%3cpath%20d='M23%208V16'%20stroke='black'/%3e%3cpath%20d='M10.3333%207H15.7879L12.4545%2011.2H17L9.72727%2019L11.2424%2013.3H7L10.3333%207Z'%20stroke='black'%20stroke-linejoin='round'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_163'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"8b5e05f","x":0.5,"y":1,"penId":"9db96ca"}],"simulationConfig":{"phases":3,"baseKV":0,"pu":1,"customScript":""},"id":"9db96ca","children":[],"x":384.*************,"y":39.25,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"75ce3d2","lineAnchor":"dffe2d2","anchor":"8b5e05f"}],"ex":484.*************,"ey":139.25,"center":{"x":434.*************,"y":89.25}},{"width":100,"height":100,"ratio":true,"name":"image","deviceType":"regulator","image":"data:image/svg+xml,%3csvg%20width='25'%20height='25'%20viewBox='0%200%2025%2025'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='12.1406'%20cy='15.1758'%20r='5.47461'%20stroke='black'/%3e%3ccircle%20cx='12.1406'%20cy='10.0381'%20r='5.47461'%20transform='rotate(-180%2012.1406%2010.0381)'%20stroke='black'/%3e%3cpath%20d='M12.1406%2023.7734V21.2559'%20stroke='black'%20stroke-linecap='square'/%3e%3cpath%20d='M12.1406%201.44043L12.1406%203.95801'%20stroke='black'%20stroke-linecap='square'/%3e%3cpath%20d='M1.00276%2019.1217L0.593417%2019.4098L1.15728%2020.2342L1.56662%2019.9461L1.00276%2019.1217ZM23.481%204.33965C23.5307%204.06614%2023.3522%203.80515%2023.0823%203.75671L18.683%202.9674C18.4131%202.91897%2018.1539%203.10143%2018.1041%203.37494C18.0544%203.64846%2018.2329%203.90945%2018.5029%203.95788L22.4133%204.65949L21.6926%208.62141C21.6428%208.89492%2021.8213%209.15591%2022.0913%209.20435C22.3612%209.25278%2022.6204%209.07032%2022.6702%208.79681L23.481%204.33965ZM1.28469%2019.5339L1.56662%2019.9461L23.2741%204.66415L22.9922%204.25195L22.7103%203.83975L1.00276%2019.1217L1.28469%2019.5339Z'%20fill='black'/%3e%3c/svg%3e","anchors":[{"id":"7e38071d","x":0.5,"y":0,"background":"blue","flag":"wind1","penId":"28aaa06f"},{"id":"52d5607c","x":0.5,"y":1,"background":"red","flag":"wind2","penId":"28aaa06f"}],"simulationConfig":{"name":"","buses":[],"phases":3,"windings":2,"XHL":0,"customScript":"","bank":"","maxTap":1.1,"minTap":0.9,"numTaps":32,"percentLoadloss":0,"wdgs":[{"wdg":1,"buses":"","conn":"WYE","kv":12.47,"kva":0,"percentR":0,"tap":0,"node":{"name":"node_1"}},{"wdg":2,"buses":"","conn":"WYE","kv":12.47,"kva":0,"percentR":0,"tap":0,"node":{"name":"node_3"}}],"control":{"name":"","transformer":"","winding":1,"vreg":120,"band":3,"ptratio":60,"enabled":true}},"id":"28aaa06f","children":[],"x":394.*************,"y":210.25,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"75ce3d2","lineAnchor":"05cb015","anchor":"7e38071d"},{"lineId":"29323c22","lineAnchor":"5083a561","anchor":"52d5607c"}],"textTop":60,"text":"Regulator_1","ex":494.*************,"ey":310.25,"center":{"x":444.*************,"y":260.25}},{"id":"75ce3d2","name":"line","lineName":"line","x":434.75,"y":139.25,"type":1,"toArrow":"line","lineWidth":1,"length":71.*************,"ex":444.75,"ey":210.25,"width":10,"height":71,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"dffe2d2","x":0,"y":0,"penId":"75ce3d2","connectTo":"9db96ca","anchorId":"8b5e05f","start":true,"lineLength":71.*************},{"x":1,"y":1,"id":"05cb015","penId":"75ce3d2","connectTo":"28aaa06f","anchorId":"7e38071d"}],"rotate":0,"center":{"x":439.75,"y":174.75}},{"anchors":[{"id":"0ecc036","x":0,"y":0.5,"background":"blue","penId":"63feb73"},{"id":"643ce97","x":1,"y":0.5,"penId":"63feb73"},{"id":"23db7269","penId":"63feb73","x":0.5437499999999998,"y":0}],"width":600.0000000000001,"height":5,"name":"rectangle","lineWidth":0,"background":"#222222","deviceType":"motherLine","simulationConfig":{"phases":3},"id":"63feb73","children":[],"x":161.75,"y":371.75,"fontSize":24,"lineHeight":1.5,"rotate":0,"connectedLines":[{"lineId":"29323c22","lineAnchor":"d5f2f4","anchor":"23db7269"}],"ex":792.*************,"ey":392.75,"center":{"x":492.75000000000006,"y":390.25}},{"id":"29323c22","name":"line","lineName":"line","x":444.*************,"y":310.25,"type":1,"toArrow":"line","lineWidth":1,"length":75.18518803594223,"ex":488,"ey":371.75,"width":43.249999999999886,"height":61.5,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"5083a561","x":0,"y":0,"penId":"29323c22","connectTo":"28aaa06f","anchorId":"52d5607c","start":true,"lineLength":75.18518803594223},{"x":1,"y":1,"id":"d5f2f4","penId":"29323c22","connectTo":"63feb73","anchorId":"23db7269"}],"rotate":0,"center":{"x":318.75000000000006,"y":350.25},"lastConnected":{"63feb73":[{"lineId":"29323c22","lineAnchor":"d5f2f4","anchor":"0ecc036"}]}}],"origin":{"x":-2.2500000000001137,"y":-57.*************14},"center":{"x":490,"y":439},"paths":{},"template":"0f0305f","component":false,"autoSizeinPc":true,"rule":true,"ruleColor":"#888888","gridColor":"#e2e2e2","gridSize":20,"color":"#222222","activeColor":"#278df8","toArrow":"line","simulationConfig":{"name":"","number":24,"stepSize":"1h","mode":"daily","hour":0,"sec":0,"voltageBases":[0.22,0.38,10],"defaultBaseFrequency":60},"version":"1.0.68","dataPoints":[]}, 更新后脚本: ﻿clear

new Circuit.circuit phases=3 bus=node_1
~ basekv=0 pu=1


set defaultbasefrequency=60
set mode=daily stepsize=1h number=24 hour=0 sec=0
set voltagebases=[0.22,0.38,10] 


new Transformer.Regulator_1
~ phases=3 windings=2 bank=RegControl_4_Regulator_1
~ XHL=0 %loadloss=0
~ maxtap=1.1 mintap=0.9 numtaps=32
~ wdg=1 bus=node_1 kv=12.47 kva=0 conn=WYE %R=0 tap=0
~ wdg=2 bus=node_3 kv=12.47 kva=0 conn=WYE %R=0 tap=0

new RegControl.RegControl_4_Regulator_1
~ transformer=Regulator_1 winding=1
~ vreg=120 band=3 ptratio=60
~ enabled=true















new LineCode.LGJ25
~ nphases=3 basefreq=50 units=km R1=1.37 X1=0.38

new LineCode.JKLYJ35
~ nphases=3 basefreq=50 units=km R1=0.3238 X1=0.303

new LineCode.JKLYJ70
~ nphases=3 basefreq=50 units=km R1=0.46 X1=0.398




new Line.63feb73__0 phases=3 bus1=node_3 bus2=node_2
~ lineCode= length=undefined
new Line.63feb73__1 phases=3 bus1=node_3 bus2=node_4
~ lineCode= length=undefined


//负荷相关


//光伏相关


//储能相关


//关键参数监控


calcv
solve

2025-06-23 10:28:00.933 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:28:00.934 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:28:00.936 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:28:00.936 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:28:00.937 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:28:00.937 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:28:21.418 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:28:21.419 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:28:21.421 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:28:21.421 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:28:21.424 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:28:21.424 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:28:23.392 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 10:28:23.392 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 10:28:23.393 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 10:28:23.393 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 10:28:23.393 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 10:28:23.393 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 10:28:27.815 - INFO - [SimulationController.java:102] : 接收到更新仿真任务请求: simulationId=25, simulationModel=, simulationDraft={"x":216,"y":-139,"scale":0.****************,"pens":[{"width":60.00000000000001,"height":60.00000000000001,"ratio":true,"name":"image","deviceType":"battery","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_163)'%3e%3crect%20x='3.5'%20y='0.5'%20width='17'%20height='23'%20stroke='black'/%3e%3cpath%20d='M1%208V16'%20stroke='black'/%3e%3cpath%20d='M23%208V16'%20stroke='black'/%3e%3cpath%20d='M10.3333%207H15.7879L12.4545%2011.2H17L9.72727%2019L11.2424%2013.3H7L10.3333%207Z'%20stroke='black'%20stroke-linejoin='round'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_163'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"728b4c55","x":0.5,"y":1,"penId":"27c9ecb8"}],"simulationConfig":{"phases":3,"baseKV":0,"pu":1,"customScript":""},"id":"27c9ecb8","children":[],"x":118,"y":246,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0},{"width":60.00000000000001,"height":60.00000000000001,"ratio":true,"name":"image","deviceType":"load","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_157)'%3e%3ccircle%20cx='12'%20cy='11.5'%20r='11'%20stroke='black'/%3e%3cpath%20d='M4%204L20%2019'%20stroke='black'/%3e%3cpath%20d='M4%2019L20%204'%20stroke='black'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_157'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"10e1ca8","x":0.5,"y":0,"penId":"2ecc49f9"}],"simulationConfig":{"name":"","buses":[],"phases":3,"phasePositions":[],"kw":1,"kvar":0,"kv":12.47,"model":1,"customScript":"","outputMode":["VI","PQ"]},"loadShapeConfig":{"name":"","mult":[],"interval":1,"divisor":1},"id":"2ecc49f9","children":[],"x":111,"y":394,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0}],"origin":{"x":32.14999999999998,"y":115.44999999999985},"center":{"x":608,"y":250},"paths":{},"template":"0f0305f","component":false,"autoSizeinPc":true,"rule":true,"ruleColor":"#888888","gridColor":"#e2e2e2","gridSize":20,"color":"#222222","activeColor":"#278df8","toArrow":"line","simulationConfig":{"name":"","number":24,"stepSize":"1h","mode":"daily","hour":0,"sec":0,"voltageBases":[0.22,0.38,10],"defaultBaseFrequency":60},"version":"1.0.68","dataPoints":[]}, simulationScript=, nodes=, controlStrategy={}
2025-06-23 10:28:27.852 - INFO - [SimulationServiceImpl.java:336] : 仿真任务draft更新: 25
2025-06-23 10:28:28.016 - INFO - [SimulationServiceImpl.java:380] : 仿真任务更新成功: ID=25
2025-06-23 10:28:28.017 - INFO - [SimulationController.java:108] : 仿真任务更新成功，ID: 25, 更新后模型: {"x":140,"y":21,"scale":1,"pens":[{"width":100,"height":100,"ratio":true,"name":"image","deviceType":"battery","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_163)'%3e%3crect%20x='3.5'%20y='0.5'%20width='17'%20height='23'%20stroke='black'/%3e%3cpath%20d='M1%208V16'%20stroke='black'/%3e%3cpath%20d='M23%208V16'%20stroke='black'/%3e%3cpath%20d='M10.3333%207H15.7879L12.4545%2011.2H17L9.72727%2019L11.2424%2013.3H7L10.3333%207Z'%20stroke='black'%20stroke-linejoin='round'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_163'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"8b5e05f","x":0.5,"y":1,"penId":"9db96ca"}],"simulationConfig":{"phases":3,"baseKV":0,"pu":1,"customScript":""},"id":"9db96ca","children":[],"x":384.*************,"y":39.25,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"75ce3d2","lineAnchor":"dffe2d2","anchor":"8b5e05f"}],"ex":484.*************,"ey":139.25,"center":{"x":434.*************,"y":89.25}},{"width":100,"height":100,"ratio":true,"name":"image","deviceType":"regulator","image":"data:image/svg+xml,%3csvg%20width='25'%20height='25'%20viewBox='0%200%2025%2025'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='12.1406'%20cy='15.1758'%20r='5.47461'%20stroke='black'/%3e%3ccircle%20cx='12.1406'%20cy='10.0381'%20r='5.47461'%20transform='rotate(-180%2012.1406%2010.0381)'%20stroke='black'/%3e%3cpath%20d='M12.1406%2023.7734V21.2559'%20stroke='black'%20stroke-linecap='square'/%3e%3cpath%20d='M12.1406%201.44043L12.1406%203.95801'%20stroke='black'%20stroke-linecap='square'/%3e%3cpath%20d='M1.00276%2019.1217L0.593417%2019.4098L1.15728%2020.2342L1.56662%2019.9461L1.00276%2019.1217ZM23.481%204.33965C23.5307%204.06614%2023.3522%203.80515%2023.0823%203.75671L18.683%202.9674C18.4131%202.91897%2018.1539%203.10143%2018.1041%203.37494C18.0544%203.64846%2018.2329%203.90945%2018.5029%203.95788L22.4133%204.65949L21.6926%208.62141C21.6428%208.89492%2021.8213%209.15591%2022.0913%209.20435C22.3612%209.25278%2022.6204%209.07032%2022.6702%208.79681L23.481%204.33965ZM1.28469%2019.5339L1.56662%2019.9461L23.2741%204.66415L22.9922%204.25195L22.7103%203.83975L1.00276%2019.1217L1.28469%2019.5339Z'%20fill='black'/%3e%3c/svg%3e","anchors":[{"id":"7e38071d","x":0.5,"y":0,"background":"blue","flag":"wind1","penId":"28aaa06f"},{"id":"52d5607c","x":0.5,"y":1,"background":"red","flag":"wind2","penId":"28aaa06f"}],"simulationConfig":{"name":"","buses":[],"phases":3,"windings":2,"XHL":0,"customScript":"","bank":"","maxTap":1.1,"minTap":0.9,"numTaps":32,"percentLoadloss":0,"wdgs":[{"wdg":1,"buses":"","conn":"WYE","kv":12.47,"kva":0,"percentR":0,"tap":0,"node":{"name":"node_1"}},{"wdg":2,"buses":"","conn":"WYE","kv":12.47,"kva":0,"percentR":0,"tap":0,"node":{"name":"node_3"}}],"control":{"name":"","transformer":"","winding":1,"vreg":120,"band":3,"ptratio":60,"enabled":true}},"id":"28aaa06f","children":[],"x":394.*************,"y":210.25,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"75ce3d2","lineAnchor":"05cb015","anchor":"7e38071d"},{"lineId":"29323c22","lineAnchor":"5083a561","anchor":"52d5607c"}],"textTop":60,"text":"Regulator_1","ex":494.*************,"ey":310.25,"center":{"x":444.*************,"y":260.25}},{"id":"75ce3d2","name":"line","lineName":"line","x":434.75,"y":139.25,"type":1,"toArrow":"line","lineWidth":1,"length":71.*************,"ex":444.75,"ey":210.25,"width":10,"height":71,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"dffe2d2","x":0,"y":0,"penId":"75ce3d2","connectTo":"9db96ca","anchorId":"8b5e05f","start":true,"lineLength":71.*************},{"x":1,"y":1,"id":"05cb015","penId":"75ce3d2","connectTo":"28aaa06f","anchorId":"7e38071d"}],"rotate":0,"center":{"x":439.75,"y":174.75}},{"anchors":[{"id":"0ecc036","x":0,"y":0.5,"background":"blue","penId":"63feb73"},{"id":"643ce97","x":1,"y":0.5,"penId":"63feb73"},{"id":"23db7269","penId":"63feb73","x":0.5437499999999998,"y":0}],"width":600.0000000000001,"height":5,"name":"rectangle","lineWidth":0,"background":"#222222","deviceType":"motherLine","simulationConfig":{"phases":3},"id":"63feb73","children":[],"x":161.75,"y":371.75,"fontSize":24,"lineHeight":1.5,"rotate":0,"connectedLines":[{"lineId":"29323c22","lineAnchor":"d5f2f4","anchor":"23db7269"}],"ex":792.*************,"ey":392.75,"center":{"x":492.75000000000006,"y":390.25}},{"id":"29323c22","name":"line","lineName":"line","x":444.*************,"y":310.25,"type":1,"toArrow":"line","lineWidth":1,"length":75.18518803594223,"ex":488,"ey":371.75,"width":43.249999999999886,"height":61.5,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"5083a561","x":0,"y":0,"penId":"29323c22","connectTo":"28aaa06f","anchorId":"52d5607c","start":true,"lineLength":75.18518803594223},{"x":1,"y":1,"id":"d5f2f4","penId":"29323c22","connectTo":"63feb73","anchorId":"23db7269"}],"rotate":0,"center":{"x":318.75000000000006,"y":350.25},"lastConnected":{"63feb73":[{"lineId":"29323c22","lineAnchor":"d5f2f4","anchor":"0ecc036"}]}}],"origin":{"x":-2.2500000000001137,"y":-57.*************14},"center":{"x":490,"y":439},"paths":{},"template":"0f0305f","component":false,"autoSizeinPc":true,"rule":true,"ruleColor":"#888888","gridColor":"#e2e2e2","gridSize":20,"color":"#222222","activeColor":"#278df8","toArrow":"line","simulationConfig":{"name":"","number":24,"stepSize":"1h","mode":"daily","hour":0,"sec":0,"voltageBases":[0.22,0.38,10],"defaultBaseFrequency":60},"version":"1.0.68","dataPoints":[]}, 更新后脚本: ﻿clear

new Circuit.circuit phases=3 bus=node_1
~ basekv=0 pu=1


set defaultbasefrequency=60
set mode=daily stepsize=1h number=24 hour=0 sec=0
set voltagebases=[0.22,0.38,10] 


new Transformer.Regulator_1
~ phases=3 windings=2 bank=RegControl_4_Regulator_1
~ XHL=0 %loadloss=0
~ maxtap=1.1 mintap=0.9 numtaps=32
~ wdg=1 bus=node_1 kv=12.47 kva=0 conn=WYE %R=0 tap=0
~ wdg=2 bus=node_3 kv=12.47 kva=0 conn=WYE %R=0 tap=0

new RegControl.RegControl_4_Regulator_1
~ transformer=Regulator_1 winding=1
~ vreg=120 band=3 ptratio=60
~ enabled=true















new LineCode.LGJ25
~ nphases=3 basefreq=50 units=km R1=1.37 X1=0.38

new LineCode.JKLYJ35
~ nphases=3 basefreq=50 units=km R1=0.3238 X1=0.303

new LineCode.JKLYJ70
~ nphases=3 basefreq=50 units=km R1=0.46 X1=0.398




new Line.63feb73__0 phases=3 bus1=node_3 bus2=node_2
~ lineCode= length=undefined
new Line.63feb73__1 phases=3 bus1=node_3 bus2=node_4
~ lineCode= length=undefined


//负荷相关


//光伏相关


//储能相关


//关键参数监控


calcv
solve

2025-06-23 10:28:35.502 - INFO - [SimulationController.java:102] : 接收到更新仿真任务请求: simulationId=25, simulationModel=, simulationDraft={"x":216,"y":-139,"scale":0.****************,"pens":[{"width":60.00000000000001,"height":60.00000000000001,"ratio":true,"name":"image","deviceType":"battery","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_163)'%3e%3crect%20x='3.5'%20y='0.5'%20width='17'%20height='23'%20stroke='black'/%3e%3cpath%20d='M1%208V16'%20stroke='black'/%3e%3cpath%20d='M23%208V16'%20stroke='black'/%3e%3cpath%20d='M10.3333%207H15.7879L12.4545%2011.2H17L9.72727%2019L11.2424%2013.3H7L10.3333%207Z'%20stroke='black'%20stroke-linejoin='round'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_163'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"728b4c55","x":0.5,"y":1,"penId":"27c9ecb8"}],"simulationConfig":{"phases":3,"baseKV":0,"pu":1,"customScript":""},"id":"27c9ecb8","children":[],"x":118,"y":246,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0},{"width":60.00000000000001,"height":60.00000000000001,"ratio":true,"name":"image","deviceType":"load","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_157)'%3e%3ccircle%20cx='12'%20cy='11.5'%20r='11'%20stroke='black'/%3e%3cpath%20d='M4%204L20%2019'%20stroke='black'/%3e%3cpath%20d='M4%2019L20%204'%20stroke='black'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_157'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"10e1ca8","x":0.5,"y":0,"penId":"2ecc49f9"}],"simulationConfig":{"name":"","buses":[],"phases":3,"phasePositions":[],"kw":1,"kvar":0,"kv":12.47,"model":1,"customScript":"","outputMode":["VI","PQ"]},"loadShapeConfig":{"name":"","mult":[],"interval":1,"divisor":1},"id":"2ecc49f9","children":[],"x":111,"y":394,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0}],"origin":{"x":32.14999999999998,"y":115.44999999999985},"center":{"x":608,"y":250},"paths":{},"template":"0f0305f","component":false,"autoSizeinPc":true,"rule":true,"ruleColor":"#888888","gridColor":"#e2e2e2","gridSize":20,"color":"#222222","activeColor":"#278df8","toArrow":"line","simulationConfig":{"name":"","number":24,"stepSize":"1h","mode":"daily","hour":0,"sec":0,"voltageBases":[0.22,0.38,10],"defaultBaseFrequency":60},"version":"1.0.68","dataPoints":[]}, simulationScript=, nodes=, controlStrategy={}
2025-06-23 10:28:35.504 - INFO - [SimulationServiceImpl.java:364] : 仿真任务没有变更，无需更新: 25
2025-06-23 10:28:35.505 - INFO - [SimulationController.java:108] : 仿真任务更新成功，ID: 25, 更新后模型: {"x":140,"y":21,"scale":1,"pens":[{"width":100,"height":100,"ratio":true,"name":"image","deviceType":"battery","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_163)'%3e%3crect%20x='3.5'%20y='0.5'%20width='17'%20height='23'%20stroke='black'/%3e%3cpath%20d='M1%208V16'%20stroke='black'/%3e%3cpath%20d='M23%208V16'%20stroke='black'/%3e%3cpath%20d='M10.3333%207H15.7879L12.4545%2011.2H17L9.72727%2019L11.2424%2013.3H7L10.3333%207Z'%20stroke='black'%20stroke-linejoin='round'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_163'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"8b5e05f","x":0.5,"y":1,"penId":"9db96ca"}],"simulationConfig":{"phases":3,"baseKV":0,"pu":1,"customScript":""},"id":"9db96ca","children":[],"x":384.*************,"y":39.25,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"75ce3d2","lineAnchor":"dffe2d2","anchor":"8b5e05f"}],"ex":484.*************,"ey":139.25,"center":{"x":434.*************,"y":89.25}},{"width":100,"height":100,"ratio":true,"name":"image","deviceType":"regulator","image":"data:image/svg+xml,%3csvg%20width='25'%20height='25'%20viewBox='0%200%2025%2025'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='12.1406'%20cy='15.1758'%20r='5.47461'%20stroke='black'/%3e%3ccircle%20cx='12.1406'%20cy='10.0381'%20r='5.47461'%20transform='rotate(-180%2012.1406%2010.0381)'%20stroke='black'/%3e%3cpath%20d='M12.1406%2023.7734V21.2559'%20stroke='black'%20stroke-linecap='square'/%3e%3cpath%20d='M12.1406%201.44043L12.1406%203.95801'%20stroke='black'%20stroke-linecap='square'/%3e%3cpath%20d='M1.00276%2019.1217L0.593417%2019.4098L1.15728%2020.2342L1.56662%2019.9461L1.00276%2019.1217ZM23.481%204.33965C23.5307%204.06614%2023.3522%203.80515%2023.0823%203.75671L18.683%202.9674C18.4131%202.91897%2018.1539%203.10143%2018.1041%203.37494C18.0544%203.64846%2018.2329%203.90945%2018.5029%203.95788L22.4133%204.65949L21.6926%208.62141C21.6428%208.89492%2021.8213%209.15591%2022.0913%209.20435C22.3612%209.25278%2022.6204%209.07032%2022.6702%208.79681L23.481%204.33965ZM1.28469%2019.5339L1.56662%2019.9461L23.2741%204.66415L22.9922%204.25195L22.7103%203.83975L1.00276%2019.1217L1.28469%2019.5339Z'%20fill='black'/%3e%3c/svg%3e","anchors":[{"id":"7e38071d","x":0.5,"y":0,"background":"blue","flag":"wind1","penId":"28aaa06f"},{"id":"52d5607c","x":0.5,"y":1,"background":"red","flag":"wind2","penId":"28aaa06f"}],"simulationConfig":{"name":"","buses":[],"phases":3,"windings":2,"XHL":0,"customScript":"","bank":"","maxTap":1.1,"minTap":0.9,"numTaps":32,"percentLoadloss":0,"wdgs":[{"wdg":1,"buses":"","conn":"WYE","kv":12.47,"kva":0,"percentR":0,"tap":0,"node":{"name":"node_1"}},{"wdg":2,"buses":"","conn":"WYE","kv":12.47,"kva":0,"percentR":0,"tap":0,"node":{"name":"node_3"}}],"control":{"name":"","transformer":"","winding":1,"vreg":120,"band":3,"ptratio":60,"enabled":true}},"id":"28aaa06f","children":[],"x":394.*************,"y":210.25,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0,"connectedLines":[{"lineId":"75ce3d2","lineAnchor":"05cb015","anchor":"7e38071d"},{"lineId":"29323c22","lineAnchor":"5083a561","anchor":"52d5607c"}],"textTop":60,"text":"Regulator_1","ex":494.*************,"ey":310.25,"center":{"x":444.*************,"y":260.25}},{"id":"75ce3d2","name":"line","lineName":"line","x":434.75,"y":139.25,"type":1,"toArrow":"line","lineWidth":1,"length":71.*************,"ex":444.75,"ey":210.25,"width":10,"height":71,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"dffe2d2","x":0,"y":0,"penId":"75ce3d2","connectTo":"9db96ca","anchorId":"8b5e05f","start":true,"lineLength":71.*************},{"x":1,"y":1,"id":"05cb015","penId":"75ce3d2","connectTo":"28aaa06f","anchorId":"7e38071d"}],"rotate":0,"center":{"x":439.75,"y":174.75}},{"anchors":[{"id":"0ecc036","x":0,"y":0.5,"background":"blue","penId":"63feb73"},{"id":"643ce97","x":1,"y":0.5,"penId":"63feb73"},{"id":"23db7269","penId":"63feb73","x":0.5437499999999998,"y":0}],"width":600.0000000000001,"height":5,"name":"rectangle","lineWidth":0,"background":"#222222","deviceType":"motherLine","simulationConfig":{"phases":3},"id":"63feb73","children":[],"x":161.75,"y":371.75,"fontSize":24,"lineHeight":1.5,"rotate":0,"connectedLines":[{"lineId":"29323c22","lineAnchor":"d5f2f4","anchor":"23db7269"}],"ex":792.*************,"ey":392.75,"center":{"x":492.75000000000006,"y":390.25}},{"id":"29323c22","name":"line","lineName":"line","x":444.*************,"y":310.25,"type":1,"toArrow":"line","lineWidth":1,"length":75.18518803594223,"ex":488,"ey":371.75,"width":43.249999999999886,"height":61.5,"fontSize":24,"lineHeight":1.5,"anchors":[{"id":"5083a561","x":0,"y":0,"penId":"29323c22","connectTo":"28aaa06f","anchorId":"52d5607c","start":true,"lineLength":75.18518803594223},{"x":1,"y":1,"id":"d5f2f4","penId":"29323c22","connectTo":"63feb73","anchorId":"23db7269"}],"rotate":0,"center":{"x":318.75000000000006,"y":350.25},"lastConnected":{"63feb73":[{"lineId":"29323c22","lineAnchor":"d5f2f4","anchor":"0ecc036"}]}}],"origin":{"x":-2.2500000000001137,"y":-57.*************14},"center":{"x":490,"y":439},"paths":{},"template":"0f0305f","component":false,"autoSizeinPc":true,"rule":true,"ruleColor":"#888888","gridColor":"#e2e2e2","gridSize":20,"color":"#222222","activeColor":"#278df8","toArrow":"line","simulationConfig":{"name":"","number":24,"stepSize":"1h","mode":"daily","hour":0,"sec":0,"voltageBases":[0.22,0.38,10],"defaultBaseFrequency":60},"version":"1.0.68","dataPoints":[]}, 更新后脚本: ﻿clear

new Circuit.circuit phases=3 bus=node_1
~ basekv=0 pu=1


set defaultbasefrequency=60
set mode=daily stepsize=1h number=24 hour=0 sec=0
set voltagebases=[0.22,0.38,10] 


new Transformer.Regulator_1
~ phases=3 windings=2 bank=RegControl_4_Regulator_1
~ XHL=0 %loadloss=0
~ maxtap=1.1 mintap=0.9 numtaps=32
~ wdg=1 bus=node_1 kv=12.47 kva=0 conn=WYE %R=0 tap=0
~ wdg=2 bus=node_3 kv=12.47 kva=0 conn=WYE %R=0 tap=0

new RegControl.RegControl_4_Regulator_1
~ transformer=Regulator_1 winding=1
~ vreg=120 band=3 ptratio=60
~ enabled=true















new LineCode.LGJ25
~ nphases=3 basefreq=50 units=km R1=1.37 X1=0.38

new LineCode.JKLYJ35
~ nphases=3 basefreq=50 units=km R1=0.3238 X1=0.303

new LineCode.JKLYJ70
~ nphases=3 basefreq=50 units=km R1=0.46 X1=0.398




new Line.63feb73__0 phases=3 bus1=node_3 bus2=node_2
~ lineCode= length=undefined
new Line.63feb73__1 phases=3 bus1=node_3 bus2=node_4
~ lineCode= length=undefined


//负荷相关


//光伏相关


//储能相关


//关键参数监控


calcv
solve

2025-06-23 10:29:00.533 - INFO - [SimulationController.java:102] : 接收到更新仿真任务请求: simulationId=30, simulationModel=, simulationDraft={"x":0,"y":0,"scale":1,"pens":[{"width":100,"height":100,"ratio":true,"name":"image","deviceType":"battery","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_163)'%3e%3crect%20x='3.5'%20y='0.5'%20width='17'%20height='23'%20stroke='black'/%3e%3cpath%20d='M1%208V16'%20stroke='black'/%3e%3cpath%20d='M23%208V16'%20stroke='black'/%3e%3cpath%20d='M10.3333%207H15.7879L12.4545%2011.2H17L9.72727%2019L11.2424%2013.3H7L10.3333%207Z'%20stroke='black'%20stroke-linejoin='round'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_163'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"1666a7d","x":0.5,"y":1,"penId":"5cef529"}],"simulationConfig":{"phases":3,"baseKV":0,"pu":1,"customScript":""},"id":"5cef529","children":[],"x":468,"y":143,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0},{"width":100,"height":100,"ratio":true,"name":"image","deviceType":"load","image":"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_634_157)'%3e%3ccircle%20cx='12'%20cy='11.5'%20r='11'%20stroke='black'/%3e%3cpath%20d='M4%204L20%2019'%20stroke='black'/%3e%3cpath%20d='M4%2019L20%204'%20stroke='black'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_634_157'%3e%3crect%20width='24'%20height='24'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e","anchors":[{"id":"b9e3706","x":0.5,"y":0,"penId":"1ee2a52a"}],"simulationConfig":{"name":"","buses":[],"phases":3,"phasePositions":[],"kw":1,"kvar":0,"kv":12.47,"model":1,"customScript":"","outputMode":["VI","PQ"]},"loadShapeConfig":{"name":"","mult":[],"interval":1,"divisor":1},"id":"1ee2a52a","children":[],"x":433,"y":286,"lineWidth":1,"fontSize":24,"lineHeight":1.5,"canvasLayer":4,"rotate":0}],"origin":{"x":0,"y":0},"center":{"x":0,"y":0},"paths":{},"template":"454b508d","component":false,"autoSizeinPc":true,"rule":true,"ruleColor":"#888888","gridColor":"#e2e2e2","gridSize":20,"color":"#222222","activeColor":"#278df8","toArrow":"line","simulationConfig":{"name":"","number":24,"stepSize":"1h","mode":"daily","hour":0,"sec":0,"voltageBases":[0.22,0.38,10],"defaultBaseFrequency":60},"version":"1.0.68","dataPoints":[]}, simulationScript=, nodes=, controlStrategy={}
2025-06-23 10:29:00.535 - INFO - [SimulationServiceImpl.java:364] : 仿真任务没有变更，无需更新: 30
2025-06-23 10:29:00.535 - INFO - [SimulationController.java:108] : 仿真任务更新成功，ID: 30, 更新后模型: null, 更新后脚本: null
2025-06-23 11:02:15.798 - INFO - [ProjectController.java:71] : 接收到分页查询项目请求: ProjectQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectType=null, keyword=, startTime=1735660800000, endTime=1767196800000)
2025-06-23 11:02:15.799 - INFO - [ProjectServiceImpl.java:122] : 开始查询项目列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectType: null, keyword: 
2025-06-23 11:02:15.801 - INFO - [ProjectServiceImpl.java:160] : 查询项目列表成功，查询到 10 条数据
2025-06-23 11:02:15.802 - INFO - [ProjectServiceImpl.java:176] : 开始查询项目总数, projectType: null, keyword: , startTime: 1735660800000, endTime: 1767196800000
2025-06-23 11:02:15.803 - INFO - [ProjectServiceImpl.java:180] : 查询项目总数完成，共 10 个项目
2025-06-23 11:02:15.803 - INFO - [ProjectController.java:83] : 项目查询成功，总数量: 10
2025-06-23 11:02:15.896 - INFO - [ProjectController.java:59] : 接收到获取项目场景列表请求
2025-06-23 11:02:15.897 - INFO - [ProjectServiceImpl.java:93] : 开始获取项目场景列表
2025-06-23 11:02:15.897 - INFO - [ProjectServiceImpl.java:102] : 项目场景列表获取成功，总数量: 2
2025-06-23 11:02:15.897 - INFO - [ProjectController.java:64] : 项目场景列表获取成功，总数量: 2
2025-06-23 11:02:17.987 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=11, keyword=, startTime=null, endTime=null)
2025-06-23 11:02:17.987 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 11, keyword: , startTime: null, endTime: null
2025-06-23 11:02:18.042 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-23 11:02:18.043 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-23 11:02:21.838 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=2
2025-06-23 11:02:21.839 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 2
2025-06-23 11:02:21.840 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:02:21.875 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script, 共 8 个子指标
2025-06-23 11:02:21.876 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/11/2/measured_data/太坪03柱上变压器.xlsx
2025-06-23 11:02:22.093 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 太坪03柱上变压器.xlsx 的电压指标，共 1 个指标
2025-06-23 11:02:22.093 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-23 11:02:22.093 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 2, 指标数量: 2
2025-06-23 11:02:22.131 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=2
2025-06-23 11:02:22.131 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 2
2025-06-23 11:02:22.132 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:02:22.132 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:02:22.132 - INFO - [CsvUtils.java:375] : 找到 8 个CSV文件，开始处理
2025-06-23 11:02:22.136 - INFO - [CsvUtils.java:389] : 完成电压数据提取，处理文件数: 8/8, 总数据点: 504, 合格数据点: 288, 合格率: 57.14%
2025-06-23 11:02:22.137 - INFO - [SimulationServiceImpl.java:1914] : 仿真任务 2 电压合格率计算完成：总数据点=504, 合格数据点=288, 合格率=57.14%, 处理时间=4ms
2025-06-23 11:02:22.137 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 2, 总数据点: 504, 合格数据点: 288, 合格率: 57.14%
2025-06-23 11:03:46.084 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=2, queryDTO=BatchMetricQueryDTO(metricIds=[circuit_Mon_monitor_load_1_vi_1.V1], startTimestamp=null)
2025-06-23 11:03:46.084 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 2, metricIds: [circuit_Mon_monitor_load_1_vi_1.V1]
2025-06-23 11:03:46.084 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 2, metricId: circuit_Mon_monitor_load_1_vi_1.V1
2025-06-23 11:03:46.086 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script/circuit_Mon_monitor_load_1_vi_1.csv, 指标ID: circuit_Mon_monitor_load_1_vi_1.V1
2025-06-23 11:03:46.088 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script/circuit_Mon_monitor_load_1_vi_1.csv, 指标: circuit_Mon_monitor_load_1_vi_1.V1, 数据点数: 24
2025-06-23 11:03:46.098 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 24
2025-06-23 11:03:46.099 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 2, 指标数量: 1
2025-06-23 11:03:52.112 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=2, queryDTO=BatchMetricQueryDTO(metricIds=[circuit_Mon_monitor_load_1_vi_1.V1, circuit_Mon_monitor_load_1_vi_1.V2], startTimestamp=null)
2025-06-23 11:03:52.112 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 2, metricIds: [circuit_Mon_monitor_load_1_vi_1.V1, circuit_Mon_monitor_load_1_vi_1.V2]
2025-06-23 11:03:52.112 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 2, metricId: circuit_Mon_monitor_load_1_vi_1.V1
2025-06-23 11:03:52.113 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script/circuit_Mon_monitor_load_1_vi_1.csv, 指标ID: circuit_Mon_monitor_load_1_vi_1.V1
2025-06-23 11:03:52.114 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script/circuit_Mon_monitor_load_1_vi_1.csv, 指标: circuit_Mon_monitor_load_1_vi_1.V1, 数据点数: 24
2025-06-23 11:03:52.114 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 2, metricId: circuit_Mon_monitor_load_1_vi_1.V2
2025-06-23 11:03:52.115 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script/circuit_Mon_monitor_load_1_vi_1.csv, 指标ID: circuit_Mon_monitor_load_1_vi_1.V2
2025-06-23 11:03:52.116 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script/circuit_Mon_monitor_load_1_vi_1.csv, 指标: circuit_Mon_monitor_load_1_vi_1.V2, 数据点数: 24
2025-06-23 11:03:52.116 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 2, 失败: 0, 总共获取数据点: 48
2025-06-23 11:03:52.117 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 2, 指标数量: 2
2025-06-23 11:03:54.120 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=2, queryDTO=BatchMetricQueryDTO(metricIds=[circuit_Mon_monitor_load_1_vi_1.V1, circuit_Mon_monitor_load_1_vi_1.V2, circuit_Mon_monitor_load_1_vi_1.V3], startTimestamp=null)
2025-06-23 11:03:54.121 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 2, metricIds: [circuit_Mon_monitor_load_1_vi_1.V1, circuit_Mon_monitor_load_1_vi_1.V2, circuit_Mon_monitor_load_1_vi_1.V3]
2025-06-23 11:03:54.121 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 2, metricId: circuit_Mon_monitor_load_1_vi_1.V1
2025-06-23 11:03:54.123 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script/circuit_Mon_monitor_load_1_vi_1.csv, 指标ID: circuit_Mon_monitor_load_1_vi_1.V1
2025-06-23 11:03:54.125 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script/circuit_Mon_monitor_load_1_vi_1.csv, 指标: circuit_Mon_monitor_load_1_vi_1.V1, 数据点数: 24
2025-06-23 11:03:54.125 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 2, metricId: circuit_Mon_monitor_load_1_vi_1.V2
2025-06-23 11:03:54.126 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script/circuit_Mon_monitor_load_1_vi_1.csv, 指标ID: circuit_Mon_monitor_load_1_vi_1.V2
2025-06-23 11:03:54.126 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script/circuit_Mon_monitor_load_1_vi_1.csv, 指标: circuit_Mon_monitor_load_1_vi_1.V2, 数据点数: 24
2025-06-23 11:03:54.126 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 2, metricId: circuit_Mon_monitor_load_1_vi_1.V3
2025-06-23 11:03:54.127 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script/circuit_Mon_monitor_load_1_vi_1.csv, 指标ID: circuit_Mon_monitor_load_1_vi_1.V3
2025-06-23 11:03:54.128 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script/circuit_Mon_monitor_load_1_vi_1.csv, 指标: circuit_Mon_monitor_load_1_vi_1.V3, 数据点数: 24
2025-06-23 11:03:54.128 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 3, 失败: 0, 总共获取数据点: 72
2025-06-23 11:03:54.128 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 2, 指标数量: 3
2025-06-23 11:03:55.341 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=2, queryDTO=BatchMetricQueryDTO(metricIds=[circuit_Mon_monitor_load_1_vi_1.V1, circuit_Mon_monitor_load_1_vi_1.V2, circuit_Mon_monitor_load_1_vi_1.V3, circuit_Mon_monitor_load_1_vi_1.V4], startTimestamp=null)
2025-06-23 11:03:55.342 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 2, metricIds: [circuit_Mon_monitor_load_1_vi_1.V1, circuit_Mon_monitor_load_1_vi_1.V2, circuit_Mon_monitor_load_1_vi_1.V3, circuit_Mon_monitor_load_1_vi_1.V4]
2025-06-23 11:03:55.342 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 2, metricId: circuit_Mon_monitor_load_1_vi_1.V1
2025-06-23 11:03:55.344 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script/circuit_Mon_monitor_load_1_vi_1.csv, 指标ID: circuit_Mon_monitor_load_1_vi_1.V1
2025-06-23 11:03:55.345 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script/circuit_Mon_monitor_load_1_vi_1.csv, 指标: circuit_Mon_monitor_load_1_vi_1.V1, 数据点数: 24
2025-06-23 11:03:55.346 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 2, metricId: circuit_Mon_monitor_load_1_vi_1.V2
2025-06-23 11:03:55.346 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script/circuit_Mon_monitor_load_1_vi_1.csv, 指标ID: circuit_Mon_monitor_load_1_vi_1.V2
2025-06-23 11:03:55.347 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script/circuit_Mon_monitor_load_1_vi_1.csv, 指标: circuit_Mon_monitor_load_1_vi_1.V2, 数据点数: 24
2025-06-23 11:03:55.347 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 2, metricId: circuit_Mon_monitor_load_1_vi_1.V3
2025-06-23 11:03:55.348 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script/circuit_Mon_monitor_load_1_vi_1.csv, 指标ID: circuit_Mon_monitor_load_1_vi_1.V3
2025-06-23 11:03:55.348 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script/circuit_Mon_monitor_load_1_vi_1.csv, 指标: circuit_Mon_monitor_load_1_vi_1.V3, 数据点数: 24
2025-06-23 11:03:55.348 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 2, metricId: circuit_Mon_monitor_load_1_vi_1.V4
2025-06-23 11:03:55.349 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script/circuit_Mon_monitor_load_1_vi_1.csv, 指标ID: circuit_Mon_monitor_load_1_vi_1.V4
2025-06-23 11:03:55.350 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script/circuit_Mon_monitor_load_1_vi_1.csv, 指标: circuit_Mon_monitor_load_1_vi_1.V4, 数据点数: 24
2025-06-23 11:03:55.350 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 4, 失败: 0, 总共获取数据点: 96
2025-06-23 11:03:55.350 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 2, 指标数量: 4
2025-06-23 11:04:13.413 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=2
2025-06-23 11:04:13.413 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 2
2025-06-23 11:04:13.414 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:04:13.416 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script, 共 8 个子指标
2025-06-23 11:04:13.416 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/11/2/measured_data/太坪03柱上变压器.xlsx
2025-06-23 11:04:13.557 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 太坪03柱上变压器.xlsx 的电压指标，共 1 个指标
2025-06-23 11:04:13.557 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-23 11:04:13.557 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 2, 指标数量: 2
2025-06-23 11:04:13.581 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=2
2025-06-23 11:04:13.581 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 2
2025-06-23 11:04:13.581 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:04:13.582 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:04:13.582 - INFO - [CsvUtils.java:375] : 找到 8 个CSV文件，开始处理
2025-06-23 11:04:13.583 - INFO - [CsvUtils.java:389] : 完成电压数据提取，处理文件数: 8/8, 总数据点: 504, 合格数据点: 288, 合格率: 57.14%
2025-06-23 11:04:13.583 - INFO - [SimulationServiceImpl.java:1914] : 仿真任务 2 电压合格率计算完成：总数据点=504, 合格数据点=288, 合格率=57.14%, 处理时间=1ms
2025-06-23 11:04:13.583 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 2, 总数据点: 504, 合格数据点: 288, 合格率: 57.14%
2025-06-23 11:04:24.287 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=25, keyword=, startTime=null, endTime=null)
2025-06-23 11:04:24.288 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 25, keyword: , startTime: null, endTime: null
2025-06-23 11:04:24.290 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-23 11:04:24.291 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-23 11:04:27.397 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=21, keyword=, startTime=null, endTime=null)
2025-06-23 11:04:27.398 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 21, keyword: , startTime: null, endTime: null
2025-06-23 11:04:27.624 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 6 条数据
2025-06-23 11:04:27.625 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 6
2025-06-23 11:04:30.626 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=24
2025-06-23 11:04:30.627 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 24
2025-06-23 11:04:30.628 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script
2025-06-23 11:04:32.399 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script, 共 216 个子指标
2025-06-23 11:04:32.400 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/21/24/measured_data/工作簿1.xlsx
2025-06-23 11:04:32.400 - INFO - [ExcelUtils.java:50] : 检测到大文件(5.55MB)，使用优化处理器
2025-06-23 11:04:34.855 - INFO - [OptimizedExcelProcessor.java:94] : 指标提取完成: 文件=D:\CETWorkSpace\ngap-server/data/21/24/measured_data/工作簿1.xlsx, 耗时=2438ms, 指标数=956
2025-06-23 11:04:34.860 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 工作簿1.xlsx 的电压指标，共 1 个指标
2025-06-23 11:04:34.860 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-23 11:04:34.860 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 24, 指标数量: 2
2025-06-23 11:04:34.950 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=24
2025-06-23 11:04:34.951 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 24
2025-06-23 11:04:34.960 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script
2025-06-23 11:04:34.961 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script
2025-06-23 11:04:34.962 - INFO - [CsvUtils.java:375] : 找到 216 个CSV文件，开始处理
2025-06-23 11:04:35.087 - INFO - [CsvUtils.java:389] : 完成电压数据提取，处理文件数: 216/216, 总数据点: 23424, 合格数据点: 23161, 合格率: 98.88%
2025-06-23 11:04:35.088 - INFO - [SimulationServiceImpl.java:1914] : 仿真任务 24 电压合格率计算完成：总数据点=23424, 合格数据点=23161, 合格率=98.88%, 处理时间=127ms
2025-06-23 11:04:35.088 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 24, 总数据点: 23424, 合格数据点: 23161, 合格率: 98.88%
2025-06-23 11:04:44.970 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=24, queryDTO=BatchMetricQueryDTO(metricIds=[circuit_Mon_monitor_generator_10_vi_1.V1], startTimestamp=null)
2025-06-23 11:04:44.970 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 24, metricIds: [circuit_Mon_monitor_generator_10_vi_1.V1]
2025-06-23 11:04:44.971 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 24, metricId: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-23 11:04:44.976 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-23 11:04:44.978 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V1, 数据点数: 96
2025-06-23 11:04:44.979 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 1, 失败: 0, 总共获取数据点: 96
2025-06-23 11:04:44.979 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 24, 指标数量: 1
2025-06-23 11:04:45.621 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=24, queryDTO=BatchMetricQueryDTO(metricIds=[circuit_Mon_monitor_generator_10_vi_1.V1, circuit_Mon_monitor_generator_10_vi_1.V2], startTimestamp=null)
2025-06-23 11:04:45.622 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 24, metricIds: [circuit_Mon_monitor_generator_10_vi_1.V1, circuit_Mon_monitor_generator_10_vi_1.V2]
2025-06-23 11:04:45.622 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 24, metricId: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-23 11:04:45.625 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-23 11:04:45.626 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V1, 数据点数: 96
2025-06-23 11:04:45.626 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 24, metricId: circuit_Mon_monitor_generator_10_vi_1.V2
2025-06-23 11:04:45.630 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V2
2025-06-23 11:04:45.632 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V2, 数据点数: 96
2025-06-23 11:04:45.632 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 2, 失败: 0, 总共获取数据点: 192
2025-06-23 11:04:45.633 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 24, 指标数量: 2
2025-06-23 11:04:46.432 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=24, queryDTO=BatchMetricQueryDTO(metricIds=[circuit_Mon_monitor_generator_10_vi_1.V1, circuit_Mon_monitor_generator_10_vi_1.V2, circuit_Mon_monitor_generator_10_vi_1.V3], startTimestamp=null)
2025-06-23 11:04:46.433 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 24, metricIds: [circuit_Mon_monitor_generator_10_vi_1.V1, circuit_Mon_monitor_generator_10_vi_1.V2, circuit_Mon_monitor_generator_10_vi_1.V3]
2025-06-23 11:04:46.433 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 24, metricId: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-23 11:04:46.438 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-23 11:04:46.439 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V1, 数据点数: 96
2025-06-23 11:04:46.439 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 24, metricId: circuit_Mon_monitor_generator_10_vi_1.V2
2025-06-23 11:04:46.441 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V2
2025-06-23 11:04:46.442 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V2, 数据点数: 96
2025-06-23 11:04:46.443 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 24, metricId: circuit_Mon_monitor_generator_10_vi_1.V3
2025-06-23 11:04:46.445 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V3
2025-06-23 11:04:46.446 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V3, 数据点数: 96
2025-06-23 11:04:46.446 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 3, 失败: 0, 总共获取数据点: 288
2025-06-23 11:04:46.446 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 24, 指标数量: 3
2025-06-23 11:04:46.978 - INFO - [SimulationController.java:259] : 接收到批量获取仿真任务指标数据请求: simulationId=24, queryDTO=BatchMetricQueryDTO(metricIds=[circuit_Mon_monitor_generator_10_vi_1.V1, circuit_Mon_monitor_generator_10_vi_1.V2, circuit_Mon_monitor_generator_10_vi_1.V3, circuit_Mon_monitor_generator_10_vi_1.V4], startTimestamp=null)
2025-06-23 11:04:46.978 - INFO - [SimulationServiceImpl.java:1480] : 开始批量获取仿真指标数据, simulationId: 24, metricIds: [circuit_Mon_monitor_generator_10_vi_1.V1, circuit_Mon_monitor_generator_10_vi_1.V2, circuit_Mon_monitor_generator_10_vi_1.V3, circuit_Mon_monitor_generator_10_vi_1.V4]
2025-06-23 11:04:46.979 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 24, metricId: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-23 11:04:46.984 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V1
2025-06-23 11:04:46.985 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V1, 数据点数: 96
2025-06-23 11:04:46.986 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 24, metricId: circuit_Mon_monitor_generator_10_vi_1.V2
2025-06-23 11:04:46.989 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V2
2025-06-23 11:04:46.990 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V2, 数据点数: 96
2025-06-23 11:04:46.991 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 24, metricId: circuit_Mon_monitor_generator_10_vi_1.V3
2025-06-23 11:04:46.994 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V3
2025-06-23 11:04:46.996 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V3, 数据点数: 96
2025-06-23 11:04:46.997 - INFO - [SimulationServiceImpl.java:1350] : 开始获取仿真指标数据, simulationId: 24, metricId: circuit_Mon_monitor_generator_10_vi_1.V4
2025-06-23 11:04:47.001 - INFO - [CsvUtils.java:138] : 开始从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标ID: circuit_Mon_monitor_generator_10_vi_1.V4
2025-06-23 11:04:47.003 - INFO - [CsvUtils.java:159] : 成功从CSV文件获取指标数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script/circuit_Mon_monitor_generator_10_vi_1.csv, 指标: circuit_Mon_monitor_generator_10_vi_1.V4, 数据点数: 96
2025-06-23 11:04:47.004 - INFO - [SimulationServiceImpl.java:1528] : 批量获取仿真指标数据完成, 成功: 4, 失败: 0, 总共获取数据点: 384
2025-06-23 11:04:47.004 - INFO - [SimulationController.java:265] : 批量获取仿真任务指标数据成功，任务ID: 24, 指标数量: 4
2025-06-23 11:04:49.832 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=18
2025-06-23 11:04:49.832 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 18
2025-06-23 11:04:49.836 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script
2025-06-23 11:04:51.199 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script, 共 214 个子指标
2025-06-23 11:04:51.200 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/21/18/measured_data/五天实测数据.xlsx
2025-06-23 11:04:51.553 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 五天实测数据.xlsx 的电压指标，共 1 个指标
2025-06-23 11:04:51.553 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-23 11:04:51.553 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 18, 指标数量: 2
2025-06-23 11:04:51.628 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=18
2025-06-23 11:04:51.628 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 18
2025-06-23 11:04:51.632 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script
2025-06-23 11:04:51.632 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/21/18/simulation_script
2025-06-23 11:04:51.632 - INFO - [CsvUtils.java:375] : 找到 214 个CSV文件，开始处理
2025-06-23 11:04:51.716 - INFO - [CsvUtils.java:389] : 完成电压数据提取，处理文件数: 214/214, 总数据点: 23136, 合格数据点: 22880, 合格率: 98.89%
2025-06-23 11:04:51.717 - INFO - [SimulationServiceImpl.java:1914] : 仿真任务 18 电压合格率计算完成：总数据点=23136, 合格数据点=22880, 合格率=98.89%, 处理时间=85ms
2025-06-23 11:04:51.717 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 18, 总数据点: 23136, 合格数据点: 22880, 合格率: 98.89%
2025-06-23 11:05:12.301 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=11, keyword=, startTime=null, endTime=null)
2025-06-23 11:05:12.302 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 11, keyword: , startTime: null, endTime: null
2025-06-23 11:05:12.304 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-23 11:05:12.305 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-23 11:05:13.924 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=2
2025-06-23 11:05:13.924 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 2
2025-06-23 11:05:13.926 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:05:13.927 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script, 共 8 个子指标
2025-06-23 11:05:13.928 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/11/2/measured_data/太坪03柱上变压器.xlsx
2025-06-23 11:05:14.041 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 太坪03柱上变压器.xlsx 的电压指标，共 1 个指标
2025-06-23 11:05:14.042 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-23 11:05:14.042 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 2, 指标数量: 2
2025-06-23 11:05:14.064 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=2
2025-06-23 11:05:14.064 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 2
2025-06-23 11:05:14.065 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:05:14.065 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:05:14.065 - INFO - [CsvUtils.java:375] : 找到 8 个CSV文件，开始处理
2025-06-23 11:05:14.067 - INFO - [CsvUtils.java:389] : 完成电压数据提取，处理文件数: 8/8, 总数据点: 504, 合格数据点: 288, 合格率: 57.14%
2025-06-23 11:05:14.067 - INFO - [SimulationServiceImpl.java:1914] : 仿真任务 2 电压合格率计算完成：总数据点=504, 合格数据点=288, 合格率=57.14%, 处理时间=2ms
2025-06-23 11:05:14.067 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 2, 总数据点: 504, 合格数据点: 288, 合格率: 57.14%
2025-06-23 11:05:59.764 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=2
2025-06-23 11:05:59.764 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 2
2025-06-23 11:05:59.765 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:05:59.766 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:05:59.766 - INFO - [CsvUtils.java:375] : 找到 8 个CSV文件，开始处理
2025-06-23 11:05:59.769 - INFO - [CsvUtils.java:389] : 完成电压数据提取，处理文件数: 8/8, 总数据点: 504, 合格数据点: 288, 合格率: 57.14%
2025-06-23 11:05:59.769 - INFO - [SimulationServiceImpl.java:1914] : 仿真任务 2 电压合格率计算完成：总数据点=504, 合格数据点=288, 合格率=57.14%, 处理时间=3ms
2025-06-23 11:05:59.769 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 2, 总数据点: 504, 合格数据点: 288, 合格率: 57.14%
2025-06-23 11:08:43.087 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 25664 (D:\CETWorkSpace\ngap-server\ngap-server.jar started by dell in D:\CETWorkSpace\ngap-server)
2025-06-23 11:08:43.089 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-06-23 11:08:44.487 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-06-23 11:08:44.494 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-06-23 11:08:44.494 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-06-23 11:08:44.495 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-23 11:08:44.540 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-06-23 11:08:44.540 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 1421 ms
2025-06-23 11:08:45.519 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-23 11:08:45.873 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-06-23 11:08:45.935 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-06-23 11:08:46.024 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-06-23 11:08:46.216 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-06-23 11:08:46.235 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-06-23 11:08:46.282 - WARN - [AbstractApplicationContext.java:559] : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 28080 is already in use
2025-06-23 11:08:46.283 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-06-23 11:08:46.283 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-23 11:08:46.289 - INFO - [DirectJDKLog.java:173] : Pausing ProtocolHandler ["http-nio-28080"]
2025-06-23 11:08:46.289 - INFO - [DirectJDKLog.java:173] : Stopping service [Tomcat]
2025-06-23 11:08:46.297 - INFO - [DirectJDKLog.java:173] : Stopping ProtocolHandler ["http-nio-28080"]
2025-06-23 11:08:46.298 - INFO - [DirectJDKLog.java:173] : Destroying ProtocolHandler ["http-nio-28080"]
2025-06-23 11:08:46.304 - INFO - [ConditionEvaluationReportLoggingListener.java:136] : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-23 11:08:46.305 -ERROR - [LoggingFailureAnalysisReporter.java:40] : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 28080 was already in use.

Action:

Identify and stop the process that's listening on port 28080 or configure this application to listen on another port.

2025-06-23 11:09:00.325 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 16736 (D:\CETWorkSpace\ngap-server\ngap-server.jar started by dell in D:\CETWorkSpace\ngap-server)
2025-06-23 11:09:00.326 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-06-23 11:09:01.532 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-06-23 11:09:01.542 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-06-23 11:09:01.542 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-06-23 11:09:01.543 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-23 11:09:01.586 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-06-23 11:09:01.586 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 1233 ms
2025-06-23 11:09:02.042 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-23 11:09:02.357 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-06-23 11:09:02.418 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-06-23 11:09:02.476 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-06-23 11:09:02.653 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-06-23 11:09:02.674 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-06-23 11:09:02.707 - WARN - [AbstractApplicationContext.java:559] : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 28080 is already in use
2025-06-23 11:09:02.708 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-06-23 11:09:02.709 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-23 11:09:02.713 - INFO - [DirectJDKLog.java:173] : Pausing ProtocolHandler ["http-nio-28080"]
2025-06-23 11:09:02.714 - INFO - [DirectJDKLog.java:173] : Stopping service [Tomcat]
2025-06-23 11:09:02.717 - INFO - [DirectJDKLog.java:173] : Stopping ProtocolHandler ["http-nio-28080"]
2025-06-23 11:09:02.718 - INFO - [DirectJDKLog.java:173] : Destroying ProtocolHandler ["http-nio-28080"]
2025-06-23 11:09:02.725 - INFO - [ConditionEvaluationReportLoggingListener.java:136] : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-23 11:09:02.726 -ERROR - [LoggingFailureAnalysisReporter.java:40] : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 28080 was already in use.

Action:

Identify and stop the process that's listening on port 28080 or configure this application to listen on another port.

2025-06-23 11:17:43.170 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 23680 (D:\CETWorkSpace\ngap-server\target\classes started by dell in D:\CETWorkSpace\ngap-server)
2025-06-23 11:17:43.172 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-06-23 11:17:43.196 - INFO - [DeferredLog.java:225] : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-23 11:17:43.197 - INFO - [DeferredLog.java:225] : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-23 11:17:45.805 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-06-23 11:17:45.810 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-06-23 11:17:45.811 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-06-23 11:17:45.811 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-23 11:17:45.861 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-06-23 11:17:45.861 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 2664 ms
2025-06-23 11:17:46.224 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-23 11:17:46.457 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-06-23 11:17:46.508 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-06-23 11:17:46.547 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-06-23 11:17:46.570 - INFO - [OptionalLiveReloadServer.java:58] : LiveReload server is running on port 35729
2025-06-23 11:17:46.767 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-06-23 11:17:46.790 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-06-23 11:17:46.807 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-06-23 11:17:46.808 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-06-23 11:17:46.832 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-06-23 11:17:46.853 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-06-23 11:17:46.986 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-06-23 11:17:47.003 - INFO - [TaskSchedule.java:38] : 开始删除
2025-06-23 11:17:47.003 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-06-23 11:17:47.006 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 4.546 seconds (JVM running for 5.949)
2025-06-23 11:17:47.009 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-23 11:17:47.009 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-23 11:17:47.009 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-06-23 11:17:47.009 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-06-23 11:17:47.015 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-06-23 11:17:47.085 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-06-23 11:17:47.085 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-23 11:17:47.091 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-06-23 11:17:47.111 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-06-23 11:17:47.111 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-06-23 11:17:47.141 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-06-23 11:17:47.141 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-06-23 11:17:47.717 - INFO - [DirectJDKLog.java:173] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 11:17:47.718 - INFO - [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-06-23 11:17:47.721 - INFO - [FrameworkServlet.java:547] : Completed initialization in 3 ms
2025-06-23 11:18:04.110 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=2
2025-06-23 11:18:04.110 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 2
2025-06-23 11:18:04.115 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:18:04.147 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script, 共 8 个子指标
2025-06-23 11:18:04.148 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/11/2/measured_data/太坪03柱上变压器.xlsx
2025-06-23 11:18:04.150 - INFO - [ExcelCacheManager.java:51] : Excel缓存管理器初始化完成，过期时间: 1800000ms
2025-06-23 11:18:04.150 - INFO - [VoltageObjectPool.java:46] : 电压对象池初始化完成
2025-06-23 11:18:04.151 - INFO - [VoltagePerformanceMonitor.java:28] : 电压性能监控器初始化完成
2025-06-23 11:18:04.660 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 太坪03柱上变压器.xlsx 的电压指标，共 1 个指标
2025-06-23 11:18:04.660 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-23 11:18:04.660 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 2, 指标数量: 2
2025-06-23 11:18:04.771 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=2
2025-06-23 11:18:04.772 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 2
2025-06-23 11:18:04.772 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:18:04.772 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:18:04.773 - INFO - [CsvUtils.java:375] : 找到 8 个CSV文件，开始处理
2025-06-23 11:18:04.774 - INFO - [CsvUtils.java:463] : 处理文件: circuit_Mon_monitor_load_1_pq_1.csv, 数据点: 72, 合格数据点: 0, 合格率: 0.00%
2025-06-23 11:18:04.775 - INFO - [CsvUtils.java:463] : 处理文件: circuit_Mon_monitor_load_1_vi_1.csv, 数据点: 72, 合格数据点: 72, 合格率: 100.00%
2025-06-23 11:18:04.775 - INFO - [CsvUtils.java:463] : 处理文件: circuit_Mon_monitor_load_2_pq_1.csv, 数据点: 72, 合格数据点: 0, 合格率: 0.00%
2025-06-23 11:18:04.776 - INFO - [CsvUtils.java:463] : 处理文件: circuit_Mon_monitor_load_2_vi_1.csv, 数据点: 72, 合格数据点: 72, 合格率: 100.00%
2025-06-23 11:18:04.777 - INFO - [CsvUtils.java:463] : 处理文件: circuit_Mon_monitor_pvsystem_1_pq_1.csv, 数据点: 72, 合格数据点: 0, 合格率: 0.00%
2025-06-23 11:18:04.777 - INFO - [CsvUtils.java:463] : 处理文件: circuit_Mon_monitor_pvsystem_1_vi_1.csv, 数据点: 72, 合格数据点: 72, 合格率: 100.00%
2025-06-23 11:18:04.778 - INFO - [CsvUtils.java:463] : 处理文件: circuit_Mon_monitor_storage_1_vi_1.csv, 数据点: 72, 合格数据点: 72, 合格率: 100.00%
2025-06-23 11:18:04.778 - INFO - [CsvUtils.java:389] : 完成电压数据提取，处理文件数: 8/8, 总数据点: 504, 合格数据点: 288, 合格率: 57.14%
2025-06-23 11:18:04.778 - INFO - [SimulationServiceImpl.java:1914] : 仿真任务 2 电压合格率计算完成：总数据点=504, 合格数据点=288, 合格率=57.14%, 处理时间=6ms
2025-06-23 11:18:04.778 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 2, 总数据点: 504, 合格数据点: 288, 合格率: 57.14%
2025-06-23 11:25:19.082 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-06-23 11:25:19.084 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-23 11:25:19.085 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-06-23 11:25:19.087 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-06-23 11:25:22.794 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 28880 (D:\CETWorkSpace\ngap-server\target\classes started by dell in D:\CETWorkSpace\ngap-server)
2025-06-23 11:25:22.795 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-06-23 11:25:22.811 - INFO - [DeferredLog.java:225] : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-23 11:25:22.811 - INFO - [DeferredLog.java:225] : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-23 11:25:23.912 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-06-23 11:25:23.918 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-06-23 11:25:23.919 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-06-23 11:25:23.919 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-23 11:25:23.977 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-06-23 11:25:23.977 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 1165 ms
2025-06-23 11:25:24.237 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-23 11:25:24.433 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-06-23 11:25:24.473 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-06-23 11:25:24.516 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-06-23 11:25:24.534 - INFO - [OptionalLiveReloadServer.java:58] : LiveReload server is running on port 35729
2025-06-23 11:25:24.602 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-06-23 11:25:24.622 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-06-23 11:25:24.634 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-06-23 11:25:24.634 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-06-23 11:25:24.645 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-06-23 11:25:24.664 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-06-23 11:25:24.804 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-06-23 11:25:24.847 - INFO - [TaskSchedule.java:38] : 开始删除
2025-06-23 11:25:24.847 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-06-23 11:25:24.849 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 2.336 seconds (JVM running for 2.684)
2025-06-23 11:25:24.851 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-23 11:25:24.851 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-23 11:25:24.851 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-06-23 11:25:24.851 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-06-23 11:25:24.866 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-06-23 11:25:25.033 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-06-23 11:25:25.033 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-23 11:25:25.035 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-06-23 11:25:25.055 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-06-23 11:25:25.055 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-06-23 11:25:25.085 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-06-23 11:25:25.085 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-06-23 11:25:25.345 - INFO - [DirectJDKLog.java:173] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 11:25:25.345 - INFO - [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-06-23 11:25:25.349 - INFO - [FrameworkServlet.java:547] : Completed initialization in 3 ms
2025-06-23 11:25:40.983 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=2
2025-06-23 11:25:40.984 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 2
2025-06-23 11:25:40.986 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:25:41.020 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script, 共 8 个子指标
2025-06-23 11:25:41.021 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/11/2/measured_data/太坪03柱上变压器.xlsx
2025-06-23 11:25:41.024 - INFO - [ExcelCacheManager.java:51] : Excel缓存管理器初始化完成，过期时间: 1800000ms
2025-06-23 11:25:41.025 - INFO - [VoltageObjectPool.java:46] : 电压对象池初始化完成
2025-06-23 11:25:41.026 - INFO - [VoltagePerformanceMonitor.java:28] : 电压性能监控器初始化完成
2025-06-23 11:25:41.695 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 太坪03柱上变压器.xlsx 的电压指标，共 1 个指标
2025-06-23 11:25:41.696 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-23 11:25:41.696 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 2, 指标数量: 2
2025-06-23 11:25:41.736 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=2
2025-06-23 11:25:41.737 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 2
2025-06-23 11:25:41.737 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:25:41.738 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:25:41.738 - INFO - [CsvUtils.java:375] : 找到 8 个CSV文件，开始处理
2025-06-23 11:25:41.740 - INFO - [CsvUtils.java:474] : 处理文件: circuit_Mon_monitor_load_1_pq_1.csv, 统计列: [S1 (kVA), S2 (kVA), S3 (kVA)], 数据点: 72, 合格数据点: 0, 合格率: 0.00%
2025-06-23 11:25:41.741 - INFO - [CsvUtils.java:474] : 处理文件: circuit_Mon_monitor_load_1_vi_1.csv, 统计列: [V1, V2, V3], 数据点: 72, 合格数据点: 72, 合格率: 100.00%
2025-06-23 11:25:41.742 - INFO - [CsvUtils.java:474] : 处理文件: circuit_Mon_monitor_load_2_pq_1.csv, 统计列: [S1 (kVA), S2 (kVA), S3 (kVA)], 数据点: 72, 合格数据点: 0, 合格率: 0.00%
2025-06-23 11:25:41.742 - INFO - [CsvUtils.java:474] : 处理文件: circuit_Mon_monitor_load_2_vi_1.csv, 统计列: [V1, V2, V3], 数据点: 72, 合格数据点: 72, 合格率: 100.00%
2025-06-23 11:25:41.743 - INFO - [CsvUtils.java:474] : 处理文件: circuit_Mon_monitor_pvsystem_1_pq_1.csv, 统计列: [S1 (kVA), S2 (kVA), S3 (kVA)], 数据点: 72, 合格数据点: 0, 合格率: 0.00%
2025-06-23 11:25:41.743 - INFO - [CsvUtils.java:474] : 处理文件: circuit_Mon_monitor_pvsystem_1_vi_1.csv, 统计列: [V1, V2, V3], 数据点: 72, 合格数据点: 72, 合格率: 100.00%
2025-06-23 11:25:41.744 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_storage_1_pq_1.csv, 未找到有效电压列
2025-06-23 11:25:41.745 - INFO - [CsvUtils.java:474] : 处理文件: circuit_Mon_monitor_storage_1_vi_1.csv, 统计列: [V1, V2, V3], 数据点: 72, 合格数据点: 72, 合格率: 100.00%
2025-06-23 11:25:41.745 - INFO - [CsvUtils.java:389] : 完成电压数据提取，处理文件数: 8/8, 总数据点: 504, 合格数据点: 288, 合格率: 57.14%
2025-06-23 11:25:41.746 - INFO - [SimulationServiceImpl.java:1914] : 仿真任务 2 电压合格率计算完成：总数据点=504, 合格数据点=288, 合格率=57.14%, 处理时间=8ms
2025-06-23 11:25:41.746 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 2, 总数据点: 504, 合格数据点: 288, 合格率: 57.14%
2025-06-23 11:28:54.664 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-06-23 11:28:54.666 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-23 11:28:54.668 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-06-23 11:28:54.668 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-06-23 11:28:58.754 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 7100 (D:\CETWorkSpace\ngap-server\target\classes started by dell in D:\CETWorkSpace\ngap-server)
2025-06-23 11:28:58.755 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-06-23 11:28:58.774 - INFO - [DeferredLog.java:225] : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-23 11:28:58.774 - INFO - [DeferredLog.java:225] : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-23 11:28:59.868 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-06-23 11:28:59.885 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-06-23 11:28:59.885 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-06-23 11:28:59.886 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-23 11:28:59.956 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-06-23 11:28:59.956 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 1182 ms
2025-06-23 11:29:00.219 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-23 11:29:00.444 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-06-23 11:29:00.475 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-06-23 11:29:00.513 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-06-23 11:29:00.531 - INFO - [OptionalLiveReloadServer.java:58] : LiveReload server is running on port 35729
2025-06-23 11:29:00.593 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-06-23 11:29:00.613 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-06-23 11:29:00.623 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-06-23 11:29:00.624 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-06-23 11:29:00.633 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-06-23 11:29:00.650 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-06-23 11:29:00.788 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-06-23 11:29:00.802 - INFO - [TaskSchedule.java:38] : 开始删除
2025-06-23 11:29:00.803 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-06-23 11:29:00.804 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 2.317 seconds (JVM running for 2.665)
2025-06-23 11:29:00.808 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-23 11:29:00.808 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-23 11:29:00.809 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-06-23 11:29:00.809 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-06-23 11:29:00.823 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-06-23 11:29:01.017 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-06-23 11:29:01.017 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-23 11:29:01.018 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-06-23 11:29:01.037 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-06-23 11:29:01.038 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-06-23 11:29:01.060 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-06-23 11:29:01.060 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-06-23 11:29:01.231 - INFO - [DirectJDKLog.java:173] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 11:29:01.231 - INFO - [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-06-23 11:29:01.235 - INFO - [FrameworkServlet.java:547] : Completed initialization in 4 ms
2025-06-23 11:29:07.701 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=2
2025-06-23 11:29:07.702 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 2
2025-06-23 11:29:07.705 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:29:07.736 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script, 共 8 个子指标
2025-06-23 11:29:07.737 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/11/2/measured_data/太坪03柱上变压器.xlsx
2025-06-23 11:29:07.739 - INFO - [ExcelCacheManager.java:51] : Excel缓存管理器初始化完成，过期时间: 1800000ms
2025-06-23 11:29:07.740 - INFO - [VoltageObjectPool.java:46] : 电压对象池初始化完成
2025-06-23 11:29:07.741 - INFO - [VoltagePerformanceMonitor.java:28] : 电压性能监控器初始化完成
2025-06-23 11:29:08.364 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 太坪03柱上变压器.xlsx 的电压指标，共 1 个指标
2025-06-23 11:29:08.364 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-23 11:29:08.364 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 2, 指标数量: 2
2025-06-23 11:29:08.403 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=2
2025-06-23 11:29:08.404 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 2
2025-06-23 11:29:08.404 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:29:08.405 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:29:08.405 - INFO - [CsvUtils.java:375] : 找到 8 个CSV文件，开始处理
2025-06-23 11:29:08.406 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_1_pq_1.csv, 未找到有效电压列
2025-06-23 11:29:08.407 - INFO - [CsvUtils.java:474] : 处理文件: circuit_Mon_monitor_load_1_vi_1.csv, 统计列: [V1, V2, V3], 数据点: 72, 合格数据点: 72, 合格率: 100.00%
2025-06-23 11:29:08.408 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_2_pq_1.csv, 未找到有效电压列
2025-06-23 11:29:08.409 - INFO - [CsvUtils.java:474] : 处理文件: circuit_Mon_monitor_load_2_vi_1.csv, 统计列: [V1, V2, V3], 数据点: 72, 合格数据点: 72, 合格率: 100.00%
2025-06-23 11:29:08.409 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_pvsystem_1_pq_1.csv, 未找到有效电压列
2025-06-23 11:29:08.409 - INFO - [CsvUtils.java:474] : 处理文件: circuit_Mon_monitor_pvsystem_1_vi_1.csv, 统计列: [V1, V2, V3], 数据点: 72, 合格数据点: 72, 合格率: 100.00%
2025-06-23 11:29:08.409 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_storage_1_pq_1.csv, 未找到有效电压列
2025-06-23 11:29:08.410 - INFO - [CsvUtils.java:474] : 处理文件: circuit_Mon_monitor_storage_1_vi_1.csv, 统计列: [V1, V2, V3], 数据点: 72, 合格数据点: 72, 合格率: 100.00%
2025-06-23 11:29:08.410 - INFO - [CsvUtils.java:389] : 完成电压数据提取，处理文件数: 8/8, 总数据点: 288, 合格数据点: 288, 合格率: 100.00%
2025-06-23 11:29:08.410 - INFO - [SimulationServiceImpl.java:1914] : 仿真任务 2 电压合格率计算完成：总数据点=288, 合格数据点=288, 合格率=100.00%, 处理时间=5ms
2025-06-23 11:29:08.410 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 2, 总数据点: 288, 合格数据点: 288, 合格率: 100.00%
2025-06-23 11:32:10.555 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-06-23 11:32:10.562 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-23 11:32:10.564 - INFO - [HikariDataSource.java:350] : HikariPool-1 - Shutdown initiated...
2025-06-23 11:32:10.565 - INFO - [HikariDataSource.java:352] : HikariPool-1 - Shutdown completed.
2025-06-23 11:32:10.776 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 7100 (D:\CETWorkSpace\ngap-server\target\classes started by dell in D:\CETWorkSpace\ngap-server)
2025-06-23 11:32:10.776 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-06-23 11:32:11.593 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-06-23 11:32:11.594 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-06-23 11:32:11.594 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-06-23 11:32:11.594 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-23 11:32:11.602 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-06-23 11:32:11.602 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 824 ms
2025-06-23 11:32:11.698 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-23 11:32:11.771 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-06-23 11:32:11.785 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-06-23 11:32:11.801 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-06-23 11:32:11.805 - INFO - [OptionalLiveReloadServer.java:58] : LiveReload server is running on port 35729
2025-06-23 11:32:11.833 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-06-23 11:32:11.845 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-06-23 11:32:11.847 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-06-23 11:32:11.848 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-06-23 11:32:11.849 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-06-23 11:32:11.852 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-06-23 11:32:11.884 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-06-23 11:32:11.891 - INFO - [TaskSchedule.java:38] : 开始删除
2025-06-23 11:32:11.891 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 1.197 seconds (JVM running for 193.752)
2025-06-23 11:32:11.892 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-06-23 11:32:11.892 - INFO - [HikariDataSource.java:110] : HikariPool-2 - Starting...
2025-06-23 11:32:11.892 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-23 11:32:11.893 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-23 11:32:11.893 - INFO - [HikariDataSource.java:123] : HikariPool-2 - Start completed.
2025-06-23 11:32:11.893 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-06-23 11:32:11.893 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-06-23 11:32:11.894 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-06-23 11:32:11.905 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-23 11:32:11.905 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-06-23 11:32:11.905 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-06-23 11:32:11.906 - INFO - [ConditionEvaluationDeltaLoggingListener.java:63] : Condition evaluation unchanged
2025-06-23 11:32:11.943 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-06-23 11:32:11.943 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-06-23 11:32:21.566 - INFO - [DirectJDKLog.java:173] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 11:32:21.567 - INFO - [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-06-23 11:32:21.569 - INFO - [FrameworkServlet.java:547] : Completed initialization in 2 ms
2025-06-23 11:32:21.571 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=2
2025-06-23 11:32:21.572 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 2
2025-06-23 11:32:21.574 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:32:21.603 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script, 共 8 个子指标
2025-06-23 11:32:21.603 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/11/2/measured_data/太坪03柱上变压器.xlsx
2025-06-23 11:32:21.605 - INFO - [ExcelCacheManager.java:51] : Excel缓存管理器初始化完成，过期时间: 1800000ms
2025-06-23 11:32:21.606 - INFO - [VoltageObjectPool.java:46] : 电压对象池初始化完成
2025-06-23 11:32:21.607 - INFO - [VoltagePerformanceMonitor.java:28] : 电压性能监控器初始化完成
2025-06-23 11:32:21.717 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 太坪03柱上变压器.xlsx 的电压指标，共 1 个指标
2025-06-23 11:32:21.718 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-23 11:32:21.718 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 2, 指标数量: 2
2025-06-23 11:32:21.743 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=2
2025-06-23 11:32:21.744 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 2
2025-06-23 11:32:21.745 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:32:21.745 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 11:32:21.746 - INFO - [CsvUtils.java:375] : 找到 8 个CSV文件，开始处理
2025-06-23 11:32:21.746 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_1_pq_1.csv, 未找到有效电压列
2025-06-23 11:32:21.747 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_2_pq_1.csv, 未找到有效电压列
2025-06-23 11:32:21.749 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_pvsystem_1_pq_1.csv, 未找到有效电压列
2025-06-23 11:32:21.750 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_storage_1_pq_1.csv, 未找到有效电压列
2025-06-23 11:32:21.750 - INFO - [CsvUtils.java:389] : 完成电压数据提取，处理文件数: 8/8, 总数据点: 288, 合格数据点: 288, 合格率: 100.00%
2025-06-23 11:32:21.751 - INFO - [SimulationServiceImpl.java:1914] : 仿真任务 2 电压合格率计算完成：总数据点=288, 合格数据点=288, 合格率=100.00%, 处理时间=5ms
2025-06-23 11:32:21.751 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 2, 总数据点: 288, 合格数据点: 288, 合格率: 100.00%
2025-06-23 14:50:08.014 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 14:50:08.015 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 14:50:08.071 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 14:50:08.071 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 14:50:08.072 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 14:50:08.072 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:03:09.586 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:03:09.586 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:03:09.588 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:03:09.588 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:03:09.589 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:03:09.590 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:03:17.372 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:03:17.373 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:03:17.373 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:03:17.373 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:03:17.374 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:03:17.374 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:04:08.188 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:04:08.189 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:04:08.190 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:04:08.191 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:04:08.191 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:04:08.191 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:04:15.690 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:04:15.691 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:04:15.692 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:04:15.693 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:04:15.694 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:04:15.694 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:07:20.929 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:07:20.949 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:07:20.955 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:07:20.955 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:07:20.956 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:07:20.956 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:07:35.733 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:07:35.734 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:07:35.735 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:07:35.735 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:07:35.735 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:07:35.735 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:07:37.422 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:07:37.423 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:07:37.424 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:07:37.424 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:07:37.425 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:07:37.425 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:07:45.616 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:07:45.616 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:07:45.618 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:07:45.618 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:07:45.618 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:07:45.618 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:15:15.029 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:15:15.030 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:15:15.031 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:15:15.031 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:15:15.031 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:15:15.031 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:18:02.369 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:18:02.370 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:18:02.372 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:18:02.372 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:18:02.372 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:18:02.372 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:25:05.148 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:25:05.149 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:25:05.151 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:25:05.152 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:25:05.153 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:25:05.153 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:25:20.327 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:25:20.328 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:25:20.329 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:25:20.332 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:25:20.332 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:25:20.333 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:25:38.432 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:25:38.432 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:25:38.434 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:25:38.435 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:25:38.436 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:25:38.436 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:27:19.774 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:27:19.775 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:27:19.775 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:27:19.776 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:27:19.776 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:27:19.776 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:27:24.322 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:27:24.334 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:27:24.335 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:27:24.336 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:27:24.336 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:27:24.336 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:27:34.955 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:27:34.955 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:27:34.956 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:27:34.956 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:27:34.956 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:27:34.956 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:32:30.381 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:32:30.382 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:32:30.383 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:32:30.384 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:32:30.384 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:32:30.384 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:32:38.865 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:32:38.866 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:32:38.867 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:32:38.867 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:32:38.868 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:32:38.868 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:32:45.160 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:32:45.161 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:32:45.162 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:32:45.163 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:32:45.163 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:32:45.164 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:34:57.825 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:34:57.826 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:34:57.826 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:34:57.826 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:34:57.826 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:34:57.826 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:35:15.677 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:35:15.678 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:35:15.678 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:35:15.679 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:35:15.679 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:35:15.679 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:35:24.270 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:35:24.270 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:35:24.272 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:35:24.272 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:35:24.273 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:35:24.273 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:36:08.358 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:36:08.358 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:36:08.360 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:36:08.360 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:36:08.361 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:36:08.362 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:36:37.780 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:36:37.781 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:36:37.783 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:36:37.783 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:36:37.785 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:36:37.785 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:36:48.882 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:36:48.883 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:36:48.884 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:36:48.884 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:36:48.885 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:36:48.885 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:36:56.476 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:36:56.476 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:36:56.478 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:36:56.478 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:36:56.478 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:36:56.478 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:37:44.987 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:37:44.987 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:37:44.988 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:37:44.989 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:37:44.989 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:37:44.990 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:37:45.792 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:37:45.793 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:37:45.795 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:37:45.796 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:37:45.796 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:37:45.797 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:50:58.481 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:50:58.482 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:50:58.482 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:50:58.482 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:50:58.483 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:50:58.483 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:52:05.711 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:52:05.711 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:52:05.713 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:52:05.713 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:52:05.714 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:52:05.715 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:52:24.854 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:52:24.855 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:52:24.857 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:52:24.857 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:52:24.858 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:52:24.858 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:52:51.982 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:52:51.983 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:52:51.984 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:52:51.985 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:52:51.985 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:52:51.986 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:53:11.161 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:53:11.162 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:53:11.164 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:53:11.164 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:53:11.165 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:53:11.165 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:53:14.556 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:53:14.556 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:53:14.558 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:53:14.558 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:53:14.559 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:53:14.559 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:54:59.117 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:54:59.117 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:54:59.118 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:54:59.119 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:54:59.119 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:54:59.120 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:55:25.352 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:55:25.353 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:55:25.354 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:55:25.355 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:55:25.355 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:55:25.356 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:55:43.121 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:55:43.122 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:55:43.123 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:55:43.124 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:55:43.124 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:55:43.124 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:55:52.326 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:55:52.327 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:55:52.328 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:55:52.328 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:55:52.329 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:55:52.329 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 15:57:11.454 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 15:57:11.455 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 15:57:11.456 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 15:57:11.456 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 15:57:11.456 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 15:57:11.456 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:00:19.949 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:00:19.950 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:00:19.952 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:00:19.952 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:00:19.952 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:00:19.952 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:01:07.064 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:01:07.064 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:01:07.066 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:01:07.066 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:01:07.067 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:01:07.067 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:01:42.877 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:01:42.878 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:01:42.881 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:01:42.881 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:01:42.881 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:01:42.882 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:01:53.882 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:01:53.882 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:01:53.884 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:01:53.884 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:01:53.885 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:01:53.885 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:02:03.615 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:02:03.615 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:02:03.615 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:02:03.615 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:02:03.616 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:02:03.616 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:02:29.678 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:02:29.679 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:02:29.680 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:02:29.680 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:02:29.681 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:02:29.681 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:02:37.885 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:02:37.885 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:02:37.887 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:02:37.887 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:02:37.887 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:02:37.888 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:08:12.661 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:08:12.662 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:08:12.663 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:08:12.663 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:08:12.663 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:08:12.663 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:08:46.447 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:08:46.448 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:08:46.449 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:08:46.449 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:08:46.450 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:08:46.450 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:08:53.511 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:08:53.511 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:08:53.513 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:08:53.513 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:08:53.514 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:08:53.514 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:09:07.035 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:09:07.036 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:09:07.037 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:09:07.037 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:09:07.038 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:09:07.038 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:10:19.343 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:10:19.344 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:10:19.345 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:10:19.346 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:10:19.346 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:10:19.346 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:10:34.182 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:10:34.182 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:10:34.183 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:10:34.184 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:10:34.184 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:10:34.185 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:10:43.682 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:10:43.682 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:10:43.684 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:10:43.684 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:10:43.685 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:10:43.685 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:11:13.802 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:11:13.804 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:11:13.804 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:11:13.804 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:11:13.804 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:11:13.804 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:16:00.675 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:16:00.676 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:16:00.677 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:16:00.677 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:16:00.678 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:16:00.678 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:16:37.320 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:16:37.321 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:16:37.322 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:16:37.322 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:16:37.323 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:16:37.323 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:17:05.732 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:17:05.732 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:17:05.732 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:17:05.732 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:17:05.734 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:17:05.734 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:18:40.782 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:18:40.782 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:18:40.784 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:18:40.784 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:18:40.785 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:18:40.785 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:19:19.764 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:19:19.764 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:19:19.765 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:19:19.765 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:19:19.767 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:19:19.767 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:19:37.137 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:19:37.137 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:19:37.137 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:19:37.138 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:19:37.138 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:19:37.138 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:22:18.777 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:22:18.777 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:22:18.779 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:22:18.780 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:22:18.780 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:22:18.781 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:24:28.697 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:24:28.697 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:24:28.698 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:24:28.698 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:24:28.698 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:24:28.698 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:31:37.278 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:31:37.278 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:31:37.280 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:31:37.280 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:31:37.280 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:31:37.280 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:33:42.302 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:33:42.303 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:33:42.304 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:33:42.305 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:33:42.305 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:33:42.305 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:35:32.265 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:35:32.266 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:35:32.268 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:35:32.269 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:35:32.269 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:35:32.269 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:42:13.333 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:42:13.333 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:42:13.334 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:42:13.334 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:42:13.334 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:42:13.334 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:42:36.468 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:42:36.469 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:42:36.469 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:42:36.470 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:42:36.470 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:42:36.470 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:44:44.352 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:44:44.353 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:44:44.354 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:44:44.355 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:44:44.355 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:44:44.356 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:46:53.037 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=2
2025-06-23 16:46:53.037 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 2
2025-06-23 16:46:53.045 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 16:46:53.138 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script, 共 8 个子指标
2025-06-23 16:46:53.138 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/11/2/measured_data/太坪03柱上变压器.xlsx
2025-06-23 16:46:53.253 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 太坪03柱上变压器.xlsx 的电压指标，共 1 个指标
2025-06-23 16:46:53.253 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-23 16:46:53.253 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 2, 指标数量: 2
2025-06-23 16:46:53.344 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=2
2025-06-23 16:46:53.344 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 2
2025-06-23 16:46:53.345 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 16:46:53.345 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/11/2/simulation_script
2025-06-23 16:46:53.345 - INFO - [CsvUtils.java:375] : 找到 8 个CSV文件，开始处理
2025-06-23 16:46:53.345 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_1_pq_1.csv, 未找到有效电压列
2025-06-23 16:46:53.346 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_2_pq_1.csv, 未找到有效电压列
2025-06-23 16:46:53.346 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_pvsystem_1_pq_1.csv, 未找到有效电压列
2025-06-23 16:46:53.347 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_storage_1_pq_1.csv, 未找到有效电压列
2025-06-23 16:46:53.347 - INFO - [CsvUtils.java:389] : 完成电压数据提取，处理文件数: 8/8, 总数据点: 288, 合格数据点: 288, 合格率: 100.00%
2025-06-23 16:46:53.347 - INFO - [SimulationServiceImpl.java:1914] : 仿真任务 2 电压合格率计算完成：总数据点=288, 合格数据点=288, 合格率=100.00%, 处理时间=2ms
2025-06-23 16:46:53.347 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 2, 总数据点: 288, 合格数据点: 288, 合格率: 100.00%
2025-06-23 16:47:00.516 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=25, keyword=, startTime=null, endTime=null)
2025-06-23 16:47:00.516 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 25, keyword: , startTime: null, endTime: null
2025-06-23 16:47:00.694 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 1 条数据
2025-06-23 16:47:00.695 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 1
2025-06-23 16:47:06.617 - INFO - [SimulationController.java:34] : 接收到分页查询仿真任务请求: SimulationQueryDTO(page=1, size=50, sortBy=created_at, sortOrder=desc, projectId=21, keyword=, startTime=null, endTime=null)
2025-06-23 16:47:06.618 - INFO - [SimulationServiceImpl.java:80] : 开始查询仿真任务列表, page: 1, size: 50, sortBy: created_at, sortOrder: desc, projectId: 21, keyword: , startTime: null, endTime: null
2025-06-23 16:47:06.734 - INFO - [SimulationServiceImpl.java:119] : 查询仿真任务列表成功，查询到 6 条数据
2025-06-23 16:47:06.734 - INFO - [SimulationController.java:50] : 仿真任务查询成功，总数量: 6
2025-06-23 16:47:08.965 - INFO - [SimulationController.java:231] : 接收到获取仿真任务指标列表请求: simulationId=24
2025-06-23 16:47:08.965 - INFO - [SimulationServiceImpl.java:1244] : 开始获取仿真指标列表, simulationId: 24
2025-06-23 16:47:08.970 - INFO - [CsvUtils.java:34] : 开始从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script
2025-06-23 16:47:10.718 - INFO - [CsvUtils.java:59] : 成功从目录提取CSV指标: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script, 共 216 个子指标
2025-06-23 16:47:10.719 - INFO - [SimulationServiceImpl.java:1316] : 开始处理电压数据文件: D:\CETWorkSpace\ngap-server/data/21/24/measured_data/工作簿1.xlsx
2025-06-23 16:47:10.720 - INFO - [ExcelUtils.java:50] : 检测到大文件(5.55MB)，使用优化处理器
2025-06-23 16:47:14.201 - INFO - [OptimizedExcelProcessor.java:94] : 指标提取完成: 文件=D:\CETWorkSpace\ngap-server/data/21/24/measured_data/工作簿1.xlsx, 耗时=3460ms, 指标数=956
2025-06-23 16:47:14.212 - INFO - [SimulationServiceImpl.java:1321] : 成功提取文件 工作簿1.xlsx 的电压指标，共 1 个指标
2025-06-23 16:47:14.213 - INFO - [SimulationServiceImpl.java:1270] : 成功获取仿真指标列表，共找到 2 个分类
2025-06-23 16:47:14.213 - INFO - [SimulationController.java:235] : 获取仿真任务指标列表成功，任务ID: 24, 指标数量: 2
2025-06-23 16:47:14.313 - INFO - [SimulationController.java:327] : 接收到计算仿真电压合格率请求: simulationId=24
2025-06-23 16:47:14.314 - INFO - [SimulationServiceImpl.java:1854] : 开始计算仿真任务电压合格率，仿真ID: 24
2025-06-23 16:47:14.317 - INFO - [SimulationServiceImpl.java:1875] : 仿真输出目录: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script
2025-06-23 16:47:14.318 - INFO - [CsvUtils.java:353] : 开始从目录提取电压数据: D:\CETWorkSpace\ngap-server/data/21/24/simulation_script
2025-06-23 16:47:14.319 - INFO - [CsvUtils.java:375] : 找到 216 个CSV文件，开始处理
2025-06-23 16:47:14.320 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_generator_10_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.321 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_generator_1_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.322 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_generator_2_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.324 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_generator_3_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.326 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_generator_4_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.327 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_generator_5_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.330 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_generator_6_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.331 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_generator_7_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.332 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_generator_8_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.333 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_generator_9_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.334 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_10_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.334 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_11_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.335 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_12_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.336 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_13_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.336 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_14_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.337 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_15_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.337 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_16_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.338 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_17_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.338 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_18_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.339 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_19_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.340 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_1_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.340 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_20_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.341 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_21_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.341 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_22_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.342 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_23_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.342 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_24_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.343 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_25_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.344 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_26_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.344 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_27_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.345 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_28_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.345 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_29_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.346 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_2_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.347 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_30_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.347 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_31_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.348 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_32_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.348 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_33_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.349 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_34_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.349 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_35_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.350 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_36_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.351 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_37_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.352 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_38_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.353 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_39_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.353 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_3_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.354 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_40_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.356 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_41_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.358 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_42_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.359 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_43_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.360 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_44_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.361 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_45_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.362 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_46_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.363 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_47_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.364 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_48_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.364 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_49_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.365 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_4_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.366 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_50_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.367 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_51_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.368 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_52_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.371 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_53_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.372 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_54_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.375 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_55_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.376 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_56_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.377 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_57_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.378 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_58_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.378 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_59_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.379 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_5_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.380 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_60_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.381 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_61_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.381 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_62_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.382 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_63_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.384 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_64_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.384 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_65_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.385 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_66_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.388 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_67_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.390 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_68_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.392 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_69_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.392 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_6_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.393 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_70_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.394 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_71_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.395 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_72_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.395 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_73_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.396 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_74_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.397 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_75_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.400 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_76_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.402 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_77_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.404 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_78_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.407 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_79_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.409 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_7_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.411 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_80_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.412 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_81_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.414 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_82_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.415 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_83_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.416 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_84_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.418 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_85_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.419 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_86_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.420 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_87_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.421 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_88_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.421 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_89_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.422 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_8_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.422 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_90_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.423 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_91_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.423 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_92_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.423 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_93_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.424 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_94_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.424 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_95_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.425 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_96_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.425 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_97_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.426 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_load_9_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.426 - INFO - [CsvUtils.java:423] : 处理文件: circuit_Mon_monitor_storage_1_pq_1.csv, 未找到有效电压列
2025-06-23 16:47:14.426 - INFO - [CsvUtils.java:389] : 完成电压数据提取，处理文件数: 216/216, 总数据点: 23424, 合格数据点: 23161, 合格率: 98.88%
2025-06-23 16:47:14.426 - INFO - [SimulationServiceImpl.java:1914] : 仿真任务 24 电压合格率计算完成：总数据点=23424, 合格数据点=23161, 合格率=98.88%, 处理时间=108ms
2025-06-23 16:47:14.426 - INFO - [SimulationController.java:333] : 计算仿真电压合格率成功，仿真任务ID: 24, 总数据点: 23424, 合格数据点: 23161, 合格率: 98.88%
2025-06-23 16:48:54.247 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:48:54.248 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:48:54.248 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:48:54.248 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:48:54.249 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:48:54.249 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 16:50:19.328 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-06-23 16:50:19.328 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-23 16:50:19.329 - INFO - [HikariDataSource.java:350] : HikariPool-2 - Shutdown initiated...
2025-06-23 16:50:19.329 - INFO - [HikariDataSource.java:352] : HikariPool-2 - Shutdown completed.
2025-06-23 16:50:19.559 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 7100 (D:\CETWorkSpace\ngap-server\target\classes started by dell in D:\CETWorkSpace\ngap-server)
2025-06-23 16:50:19.559 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-06-23 16:50:20.530 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-06-23 16:50:20.530 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-06-23 16:50:20.530 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-06-23 16:50:20.530 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-23 16:50:20.551 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-06-23 16:50:20.551 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 990 ms
2025-06-23 16:50:20.697 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-23 16:50:20.765 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-06-23 16:50:20.779 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-06-23 16:50:20.797 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-06-23 16:50:20.802 - INFO - [OptionalLiveReloadServer.java:58] : LiveReload server is running on port 35729
2025-06-23 16:50:20.831 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-06-23 16:50:20.843 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-06-23 16:50:20.845 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-06-23 16:50:20.845 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-06-23 16:50:20.846 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-06-23 16:50:20.849 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-06-23 16:50:20.877 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-06-23 16:50:20.884 - INFO - [TaskSchedule.java:38] : 开始删除
2025-06-23 16:50:20.884 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 1.374 seconds (JVM running for 19282.744)
2025-06-23 16:50:20.884 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-06-23 16:50:20.884 - INFO - [HikariDataSource.java:110] : HikariPool-3 - Starting...
2025-06-23 16:50:20.885 - INFO - [HikariDataSource.java:123] : HikariPool-3 - Start completed.
2025-06-23 16:50:20.885 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-23 16:50:20.886 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-23 16:50:20.886 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-06-23 16:50:20.886 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-06-23 16:50:20.886 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-06-23 16:50:20.894 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-23 16:50:20.894 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-06-23 16:50:20.894 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-06-23 16:50:20.895 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-06-23 16:50:20.895 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-06-23 16:50:20.895 - INFO - [ConditionEvaluationDeltaLoggingListener.java:63] : Condition evaluation unchanged
2025-06-23 16:56:20.997 - INFO - [DirectJDKLog.java:173] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 16:56:20.998 - INFO - [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-06-23 16:56:21.007 - INFO - [FrameworkServlet.java:547] : Completed initialization in 9 ms
2025-06-23 16:56:21.013 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 16:56:21.014 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 16:56:21.030 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 16:56:21.031 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 16:56:21.032 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 16:56:21.032 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 17:02:07.499 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 17:02:07.499 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 17:02:07.501 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 17:02:07.501 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 17:02:07.502 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 17:02:07.502 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 17:08:39.099 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'taskScheduler'
2025-06-23 17:08:39.099 - INFO - [ExecutorConfigurationSupport.java:218] : Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-23 17:08:39.100 - INFO - [HikariDataSource.java:350] : HikariPool-3 - Shutdown initiated...
2025-06-23 17:08:39.101 - INFO - [HikariDataSource.java:352] : HikariPool-3 - Shutdown completed.
2025-06-23 17:08:41.132 - INFO - [StartupInfoLogger.java:55] : Starting NgapServerApplication on DESKTOP-9LFKQ75 with PID 32664 (D:\CETWorkSpace\ngap-server\target\classes started by dell in D:\CETWorkSpace\ngap-server)
2025-06-23 17:08:41.133 - INFO - [SpringApplication.java:651] : No active profile set, falling back to default profiles: default
2025-06-23 17:08:41.151 - INFO - [DeferredLog.java:225] : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-23 17:08:41.151 - INFO - [DeferredLog.java:225] : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-23 17:08:42.293 - INFO - [TomcatWebServer.java:108] : Tomcat initialized with port(s): 28080 (http)
2025-06-23 17:08:42.298 - INFO - [DirectJDKLog.java:173] : Initializing ProtocolHandler ["http-nio-28080"]
2025-06-23 17:08:42.298 - INFO - [DirectJDKLog.java:173] : Starting service [Tomcat]
2025-06-23 17:08:42.298 - INFO - [DirectJDKLog.java:173] : Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-23 17:08:42.347 - INFO - [DirectJDKLog.java:173] : Initializing Spring embedded WebApplicationContext
2025-06-23 17:08:42.348 - INFO - [ServletWebServerApplicationContext.java:285] : Root WebApplicationContext: initialization completed in 1197 ms
2025-06-23 17:08:42.605 - INFO - [DssUtils.java:20] : DssUtils初始化完成，已设置自定义错误处理器
2025-06-23 17:08:42.804 - INFO - [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-06-23 17:08:42.848 - INFO - [PropertySourcedRequestMappingHandlerMapping.java:69] : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-06-23 17:08:42.889 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'applicationTaskExecutor'
2025-06-23 17:08:42.907 - INFO - [OptionalLiveReloadServer.java:58] : LiveReload server is running on port 35729
2025-06-23 17:08:42.971 - INFO - [ExecutorConfigurationSupport.java:181] : Initializing ExecutorService 'taskScheduler'
2025-06-23 17:08:42.992 - INFO - [DirectJDKLog.java:173] : Starting ProtocolHandler ["http-nio-28080"]
2025-06-23 17:08:43.024 - INFO - [TomcatWebServer.java:220] : Tomcat started on port(s): 28080 (http) with context path ''
2025-06-23 17:08:43.025 - INFO - [DocumentationPluginsBootstrapper.java:160] : Context refreshed
2025-06-23 17:08:43.037 - INFO - [DocumentationPluginsBootstrapper.java:163] : Found 1 custom documentation plugin(s)
2025-06-23 17:08:43.056 - INFO - [ApiListingReferenceScanner.java:41] : Scanning for api listing references
2025-06-23 17:08:43.175 - INFO - [CachingOperationNameGenerator.java:40] : Generating unique operation named: importProjectUsingPOST_1
2025-06-23 17:08:43.189 - INFO - [TaskSchedule.java:38] : 开始删除
2025-06-23 17:08:43.189 - INFO - [TaskSchedule.java:44] : 开始删除项目
2025-06-23 17:08:43.191 - INFO - [StartupInfoLogger.java:61] : Started NgapServerApplication in 2.384 seconds (JVM running for 2.913)
2025-06-23 17:08:43.193 - INFO - [DatabaseInitializer.java:36] : 开始数据库初始化检查...
2025-06-23 17:08:43.194 - INFO - [DatabaseInitializer.java:41] : 数据库驱动加载成功: org.sqlite.JDBC
2025-06-23 17:08:43.194 - INFO - [DatabaseInitializer.java:48] : 数据库文件已存在: *****************************
2025-06-23 17:08:43.194 - INFO - [DatabaseInitializer.java:93] : 开始验证数据库表结构...
2025-06-23 17:08:43.209 - INFO - [HikariDataSource.java:110] : HikariPool-1 - Starting...
2025-06-23 17:08:43.311 - INFO - [DatabaseInitializer.java:113] : 数据库表结构验证通过
2025-06-23 17:08:43.311 - INFO - [DatabaseInitializer.java:58] : 数据库初始化检查完成
2025-06-23 17:08:43.315 - INFO - [HikariDataSource.java:123] : HikariPool-1 - Start completed.
2025-06-23 17:08:43.327 - INFO - [TaskSchedule.java:50] : 没有需要清理的已删除项目记录
2025-06-23 17:08:43.327 - INFO - [TaskSchedule.java:96] : 开始删除仿真任务
2025-06-23 17:08:43.361 - INFO - [TaskSchedule.java:102] : 没有需要清理的已删除仿真任务
2025-06-23 17:08:43.362 - INFO - [TaskSchedule.java:41] : 删除完毕
2025-06-23 17:08:43.905 - INFO - [DirectJDKLog.java:173] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 17:08:43.905 - INFO - [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
2025-06-23 17:08:43.908 - INFO - [FrameworkServlet.java:547] : Completed initialization in 3 ms
2025-06-23 17:29:50.520 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 17:29:50.520 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 17:29:50.546 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 17:29:50.547 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 17:29:50.548 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 17:29:50.548 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 17:31:34.119 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 17:31:34.119 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 17:31:34.121 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 17:31:34.121 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 17:31:34.122 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 17:31:34.122 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 17:43:38.562 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 17:43:38.563 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 17:43:38.566 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 17:43:38.567 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 17:43:38.568 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 17:43:38.569 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 17:43:54.165 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 17:43:54.165 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 17:43:54.166 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 17:43:54.168 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 17:43:54.168 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 17:43:54.168 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 17:45:59.765 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 17:45:59.766 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 17:45:59.769 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 17:45:59.769 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 17:45:59.771 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 17:45:59.771 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 17:48:14.722 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 17:48:14.723 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 17:48:14.727 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 17:48:14.727 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 17:48:14.728 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 17:48:14.729 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 17:49:28.190 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 17:49:28.191 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 17:49:28.194 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 17:49:28.195 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 17:49:28.196 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 17:49:28.196 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
2025-06-23 17:49:31.977 - INFO - [LineCodeController.java:78] : 接收到分页查询线路代码请求: LineCodeQueryDTO [page=1, size=99, sortBy=lineCodeId, sortOrder=desc, keyword=null]
2025-06-23 17:49:31.978 - INFO - [LineCodeServiceImpl.java:53] : 开始查询线路代码列表, page: 1, size: 99, sortBy: lineCodeId, sortOrder: desc, keyword: null
2025-06-23 17:49:31.979 - INFO - [LineCodeServiceImpl.java:83] : 查询线路代码列表成功，查询到 3 条数据
2025-06-23 17:49:31.979 - INFO - [LineCodeServiceImpl.java:90] : 开始查询线路代码总数, keyword: null
2025-06-23 17:49:31.981 - INFO - [LineCodeServiceImpl.java:95] : 查询线路代码总数完成，共 3 条记录
2025-06-23 17:49:31.981 - INFO - [LineCodeController.java:90] : 线路代码查询成功，总数量: 3
