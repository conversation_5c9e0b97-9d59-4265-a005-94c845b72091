package com.cet.electric.ngapserver.web.controller;

import com.cet.electric.ngapserver.dto.*;
import com.cet.electric.ngapserver.entity.*;
import com.cet.electric.ngapserver.enums.RunStatus;
import com.cet.electric.ngapserver.service.SimulationService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@RunWith(SpringRunner.class)
public class SimulationControllerTest {

    @InjectMocks
    private SimulationController simulationController;

    @Mock
    private SimulationService simulationService;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(simulationController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    public void testGetSimulations_Success() throws Exception {
        // 准备测试数据
        List<Simulation> mockSimulations = Arrays.asList(
                Simulation.builder().simulationId(1L).simulationName("仿真1").runStatus(RunStatus.READY).build(),
                Simulation.builder().simulationId(2L).simulationName("仿真2").runStatus(RunStatus.RUNNING).build()
        );

        SimulationQueryDTO queryDTO = new SimulationQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setSize(10);
        queryDTO.setSortBy("created_at");
        queryDTO.setSortOrder("desc");
        queryDTO.setProjectId(1L);

        // Mock 服务层行为
        when(simulationService.getSimulations(anyInt(), anyInt(), anyString(), anyString(), 
                anyLong(), anyString(), any(), any())).thenReturn(mockSimulations);
        when(simulationService.countSimulations(anyLong(), anyString(), any(), any())).thenReturn(2L);

        // 执行测试
        mockMvc.perform(post("/ngap-server/api/simulations/query")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(queryDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("查询成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(0))
                .andExpect(jsonPath("$.total").value(0));

        // 验证服务层调用
        verify(simulationService).getSimulations(1, 10, "created_at", "desc", 1L, null, null, null);
        verify(simulationService).countSimulations(1L, null, null, null);
    }

    @Test
    public void testCreateSimulation_Success() throws Exception {
        // 准备测试数据
        Simulation mockSimulation = Simulation.builder()
                .simulationId(1L)
                .projectId(100L)
                .simulationName("测试仿真")
                .runStatus(RunStatus.READY)
                .createdAt(System.currentTimeMillis())
                .updatedAt(System.currentTimeMillis())
                .isDeleted(0)
                .build();

        // Mock 服务层行为
        when(simulationService.createSimulation(any(Simulation.class))).thenReturn(mockSimulation);

        // 执行测试
        mockMvc.perform(post("/ngap-server/api/simulations")
                        .param("projectId", "100")
                        .param("simulationName", "测试仿真")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("仿真任务创建成功"))
                .andExpect(jsonPath("$.data.simulationId").value(1))
                .andExpect(jsonPath("$.data.simulationName").value("测试仿真"))
                .andExpect(jsonPath("$.data.projectId").value(100));

        // 验证服务层调用
        verify(simulationService).createSimulation(any(Simulation.class));
    }

    @Test
    public void testRenameSimulation_Success() throws Exception {
        // 准备测试数据
        Simulation renamedSimulation = Simulation.builder()
                .simulationId(1L)
                .simulationName("新仿真名称")
                .runStatus(RunStatus.READY)
                .build();

        // Mock 服务层行为
        when(simulationService.renameSimulation(1L, "新仿真名称")).thenReturn(renamedSimulation);

        // 执行测试
        mockMvc.perform(put("/ngap-server/api/simulations/1/rename")
                        .param("newName", "新仿真名称")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("仿真任务重命名成功"))
                .andExpect(jsonPath("$.data.simulationName").value("新仿真名称"));

        // 验证服务层调用
        verify(simulationService).renameSimulation(1L, "新仿真名称");
    }

    @Test
    public void testUpdateSimulation_Success() throws Exception {
        // 准备测试数据
        SimulationUpdateDTO updateDTO = new SimulationUpdateDTO();
        updateDTO.setSimulationModel("新模型");
        updateDTO.setSimulationDraft("新草稿");
        updateDTO.setControlStrategy("新控制策略");

        SimulationScript simulationScript = SimulationScript.builder()
                .simulationScript("新脚本")
                .nodes("新节点")
                .build();
        updateDTO.setSimulationScript(simulationScript);

        Simulation updatedSimulation = Simulation.builder()
                .simulationId(1L)
                .simulationModel("新模型")
                .simulationDraft("新草稿")
                .simulationScript("新脚本")
                .nodes("新节点")
                .controlStrategy("新控制策略")
                .build();

        // Mock 服务层行为
        when(simulationService.updateSimulation(eq(1L), eq("新模型"), eq("新草稿"), 
                eq("新脚本"), eq("新节点"), eq("新控制策略"))).thenReturn(updatedSimulation);

        // 执行测试
        mockMvc.perform(put("/ngap-server/api/simulations/1/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("仿真任务更新成功"))
                .andExpect(jsonPath("$.data.simulationModel").value("新模型"));

        // 验证服务层调用
        verify(simulationService).updateSimulation(1L, "新模型", "新草稿", "新脚本", "新节点", "新控制策略");
    }

    @Test
    public void testDeleteSimulation_Success() throws Exception {
        // Mock 服务层行为
        doNothing().when(simulationService).deleteSimulation(1L);

        // 执行测试
        mockMvc.perform(delete("/ngap-server/api/simulations/1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("仿真任务删除成功"))
                .andExpect(jsonPath("$.data").isEmpty());

        // 验证服务层调用
        verify(simulationService).deleteSimulation(1L);
    }

    @Test
    public void testGetSimulation_Success() throws Exception {
        // 准备测试数据
        Simulation simulation = Simulation.builder()
                .simulationId(1L)
                .simulationName("测试仿真")
                .runStatus(RunStatus.READY)
                .build();

        // Mock 服务层行为
        when(simulationService.getSimulationById(1L)).thenReturn(simulation);

        // 执行测试
        mockMvc.perform(get("/ngap-server/api/simulations/1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("获取仿真任务详情成功"))
                .andExpect(jsonPath("$.data.simulationId").value(1))
                .andExpect(jsonPath("$.data.simulationName").value("测试仿真"));

        // 验证服务层调用
        verify(simulationService).getSimulationById(1L);
    }

    @Test
    public void testCopySimulation_Success() throws Exception {
        // 准备测试数据
        Simulation copiedSimulation = Simulation.builder()
                .simulationId(2L)
                .simulationName("测试仿真_副本")
                .runStatus(RunStatus.READY)
                .build();

        // Mock 服务层行为
        when(simulationService.copySimulation(1L)).thenReturn(copiedSimulation);

        // 执行测试
        mockMvc.perform(post("/ngap-server/api/simulations/1/copy")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("仿真任务复制成功"))
                .andExpect(jsonPath("$.data.simulationId").value(2))
                .andExpect(jsonPath("$.data.simulationName").value("测试仿真_副本"));

        // 验证服务层调用
        verify(simulationService).copySimulation(1L);
    }

    @Test
    public void testUploadFile_Success() throws Exception {
        // 准备测试数据
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.dss",
                "application/octet-stream",
                "test file content".getBytes()
        );

        // Mock 服务层行为
        when(simulationService.uploadFile(1L, file, "dss")).thenReturn("/path/to/uploaded/file");

        // 执行测试
        mockMvc.perform(multipart("/ngap-server/api/simulations/1/upload")
                        .file(file)
                        .param("type", "dss"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("文件上传成功"))
                .andExpect(jsonPath("$.data").value("/path/to/uploaded/file"));

        // 验证服务层调用
        verify(simulationService).uploadFile(eq(1L), any(), eq("dss"));
    }

    @Test
    public void testRunSimulation_Success() throws Exception {
        // Mock 服务层行为
        doNothing().when(simulationService).runSimulation(1L);

        // 执行测试
        mockMvc.perform(post("/ngap-server/api/simulations/1/run")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("仿真任务运行成功"))
                .andExpect(jsonPath("$.data").isEmpty());

        // 验证服务层调用
        verify(simulationService).runSimulation(1L);
    }

    @Test
    public void testGetSimulationStrategies_Success() throws Exception {
        // 准备测试数据
        Map<String, String> strategies = new LinkedHashMap<>();
        strategies.put("REACTIVE_POWER_VOLTAGE_CONTROL", "无功-电压控制策略");
        strategies.put("ACTIVE_POWER_VOLTAGE_CONTROL", "有功-电压控制策略");

        // Mock 服务层行为
        when(simulationService.getSimulationStrategies()).thenReturn(strategies);

        // 执行测试
        mockMvc.perform(get("/ngap-server/api/simulations/strategies")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("获取仿真策略成功"))
                .andExpect(jsonPath("$.data.REACTIVE_POWER_VOLTAGE_CONTROL").value("无功-电压控制策略"))
                .andExpect(jsonPath("$.data.ACTIVE_POWER_VOLTAGE_CONTROL").value("有功-电压控制策略"));

        // 验证服务层调用
        verify(simulationService).getSimulationStrategies();
    }

    @Test
    public void testExportSimulation_Success() throws Exception {
        // Mock 服务层行为
        doNothing().when(simulationService).exportSimulation(eq(1L), any(HttpServletResponse.class));

        // 执行测试
        mockMvc.perform(get("/ngap-server/api/simulations/1/export")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        // 验证服务层调用
        verify(simulationService).exportSimulation(eq(1L), any(HttpServletResponse.class));
    }

    @Test
    public void testImportSimulation_Success() throws Exception {
        // 准备测试数据
        Simulation importedSimulation = Simulation.builder()
                .simulationId(1L)
                .projectId(100L)
                .simulationName("导入的仿真")
                .runStatus(RunStatus.READY)
                .build();

        MockMultipartFile file = new MockMultipartFile(
                "file",
                "simulation.json",
                "application/json",
                "{\"simulationName\":\"导入的仿真\"}".getBytes()
        );

        // Mock 服务层行为
        when(simulationService.importSimulation(eq(100L), any())).thenReturn(importedSimulation);

        // 执行测试
        mockMvc.perform(multipart("/ngap-server/api/simulations/import")
                        .file(file)
                        .param("projectId", "100"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("仿真任务导入成功"))
                .andExpect(jsonPath("$.data.simulationName").value("导入的仿真"));

        // 验证服务层调用
        verify(simulationService).importSimulation(eq(100L), any());
    }

    @Test
    public void testGetSimulationOutputPath_Success() throws Exception {
        // Mock 服务层行为
        when(simulationService.getSimulationOutputPath(1L)).thenReturn("/output/path/simulation_1");

        // 执行测试
        mockMvc.perform(get("/ngap-server/api/simulations/1/path")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("获取仿真任务输出路径成功"))
                .andExpect(jsonPath("$.data").value("/output/path/simulation_1"));

        // 验证服务层调用
        verify(simulationService).getSimulationOutputPath(1L);
    }

    @Test
    public void testGetSimulationMetrics_Success() throws Exception {
        // 准备测试数据
        List<Metric> metrics = Arrays.asList(
                Metric.builder().metricId("voltage").metricName("电压").build(),
                Metric.builder().metricId("current").metricName("电流").build()
        );

        // Mock 服务层行为
        when(simulationService.getSimulationMetrics(1L)).thenReturn(metrics);

        // 执行测试
        mockMvc.perform(get("/ngap-server/api/simulations/1/metrics")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("获取仿真任务指标列表成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2));

        // 验证服务层调用
        verify(simulationService).getSimulationMetrics(1L);
    }

    @Test
    public void testGetSimulationMetricData_Success() throws Exception {
        // 准备测试数据
        List<MetricData> metricData = Arrays.asList(
                MetricData.builder()
                        .metric(Metric.builder().metricId("voltage").metricName("电压").build())
                        .dataPoints(Arrays.asList(
                                DataPoint.builder().timestamp("2023-01-01 00:00").value(1.0).build(),
                                DataPoint.builder().timestamp("2023-01-01 01:00").value(2.0).build(),
                                DataPoint.builder().timestamp("2023-01-01 02:00").value(3.0).build()
                        ))
                        .build()
        );

        // Mock 服务层行为
        when(simulationService.getSimulationMetricData(1L, "voltage", null)).thenReturn(metricData);

        // 执行测试
        mockMvc.perform(get("/ngap-server/api/simulations/1/metrics/voltage")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("获取仿真任务指标数据成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1));

        // 验证服务层调用
        verify(simulationService).getSimulationMetricData(1L, "voltage", null);
    }

    @Test
    public void testBatchGetSimulationMetricData_Success() throws Exception {
        // 准备测试数据
        BatchMetricQueryDTO queryDTO = new BatchMetricQueryDTO();
        queryDTO.setMetricIds(Arrays.asList("voltage", "current"));
        queryDTO.setStartTimestamp(1000L);

        List<MetricData> metricData = Arrays.asList(
                MetricData.builder()
                        .metric(Metric.builder().metricId("voltage").metricName("电压").build())
                        .dataPoints(Arrays.asList(
                                DataPoint.builder().timestamp("2023-01-01 00:00").value(1.0).build(),
                                DataPoint.builder().timestamp("2023-01-01 01:00").value(2.0).build()
                        ))
                        .build(),
                MetricData.builder()
                        .metric(Metric.builder().metricId("current").metricName("电流").build())
                        .dataPoints(Arrays.asList(
                                DataPoint.builder().timestamp("2023-01-01 00:00").value(3.0).build(),
                                DataPoint.builder().timestamp("2023-01-01 01:00").value(4.0).build()
                        ))
                        .build()
        );

        // Mock 服务层行为
        when(simulationService.getMultipleSimulationMetricData(1L, Arrays.asList("voltage", "current"), 1000L))
                .thenReturn(metricData);

        // 执行测试
        mockMvc.perform(post("/ngap-server/api/simulations/1/metrics/batch")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(queryDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("批量获取仿真任务指标数据成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2));

        // 验证服务层调用
        verify(simulationService).getMultipleSimulationMetricData(1L, Arrays.asList("voltage", "current"), 1000L);
    }

    @Test
    public void testExportSimulationMetricData_Success() throws Exception {
        // 准备测试数据
        BatchMetricExportDTO exportDTO = new BatchMetricExportDTO();
        exportDTO.setPic("chart");
        exportDTO.setMetricIds(Arrays.asList("voltage", "current"));
        exportDTO.setStartTimestamp(1000L);

        // Mock 服务层行为
        doNothing().when(simulationService).exportMultipleSimulationMetricData(
                eq(1L), eq("chart"), eq(Arrays.asList("voltage", "current")), eq(1000L), any(HttpServletResponse.class));

        // 执行测试
        mockMvc.perform(post("/ngap-server/api/simulations/1/metrics/export")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(exportDTO)))
                .andExpect(status().isOk());

        // 验证服务层调用
        verify(simulationService).exportMultipleSimulationMetricData(
                eq(1L), eq("chart"), eq(Arrays.asList("voltage", "current")), eq(1000L), any(HttpServletResponse.class));
    }

    @Test
    public void testGetCourtsVoltageReport_Success() throws Exception {
        // 准备测试数据
        CourtsStatistics voltageReport = CourtsStatistics.builder()
                .totalUsers(100)
                .qualifiedRate(95.5)
                .build();

        // Mock 服务层行为
        when(simulationService.getCourtsVoltageReport(1L)).thenReturn(voltageReport);

        // 执行测试
        mockMvc.perform(get("/ngap-server/api/simulations/1/report")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("获取台区电压统计报表成功"))
                .andExpect(jsonPath("$.data.totalUsers").value(100))
                .andExpect(jsonPath("$.data.qualifiedRate").value(95.5));

        // 验证服务层调用
        verify(simulationService).getCourtsVoltageReport(1L);
    }

    @Test
    public void testGetUserVoltageReport_Success() throws Exception {
        // 准备测试数据
        UserVoltageReportQueryDTO queryDTO = new UserVoltageReportQueryDTO();
        queryDTO.setKeyword("用户");
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);

        List<UserStatistics> userReport = Arrays.asList(
                UserStatistics.builder().userName("user1").qualifiedRate(98.0).build(),
                UserStatistics.builder().userName("user2").qualifiedRate(96.5).build()
        );

        // Mock 服务层行为
        when(simulationService.getUserVoltageReport(1L, "用户", 1, 10)).thenReturn(userReport);
        when(simulationService.countUserVoltageReport(1L, "用户")).thenReturn(2L);

        // 执行测试
        mockMvc.perform(post("/ngap-server/api/simulations/1/user-report")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(queryDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("获取用户电压合格率统计报表成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.total").value(2));

        // 验证服务层调用
        verify(simulationService).getUserVoltageReport(1L, "用户", 1, 10);
        verify(simulationService).countUserVoltageReport(1L, "用户");
    }

    @Test
    public void testCreateSimulation_WithTrimmedName() throws Exception {
        // 准备测试数据
        Simulation mockSimulation = Simulation.builder()
                .simulationId(1L)
                .projectId(100L)
                .simulationName("测试仿真")
                .build();

        // Mock 服务层行为
        when(simulationService.createSimulation(any(Simulation.class))).thenReturn(mockSimulation);

        // 执行测试 - 仿真名称包含前后空格
        mockMvc.perform(post("/ngap-server/api/simulations")
                        .param("projectId", "100")
                        .param("simulationName", "  测试仿真  ")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0));

        // 验证服务层调用时参数已被trim
        verify(simulationService).createSimulation(argThat(simulation ->
                "测试仿真".equals(simulation.getSimulationName())));
    }

    // ==================== 电压质量计算测试方法 ====================

    @Test
    public void testCalculateSimulationVoltageQualityRate_Success() throws Exception {
        // 准备测试数据
        CourtsStatistics mockResult = CourtsStatistics.builder()
                .totalUsers(0)
                .qualifiedRate(85.5)
                .aboveMaxRate(10.2)
                .belowMinRate(4.3)
                .maxVoltage(250.5)
                .minVoltage(190.2)
                .totalReadings(10000)
                .qualifiedReadings(8550)
                .aboveMaxReadings(1020)
                .belowMinReadings(430)
                .build();

        // Mock服务层
        when(simulationService.calculateSimulationVoltageQualityRate(1L)).thenReturn(mockResult);

        // 执行测试
        mockMvc.perform(get("/ngap-server/api/simulations/1/simulation-report")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("计算仿真电压合格率成功"))
                .andExpect(jsonPath("$.data.totalUsers").value(0))
                .andExpect(jsonPath("$.data.qualifiedRate").value(85.5))
                .andExpect(jsonPath("$.data.aboveMaxRate").value(10.2))
                .andExpect(jsonPath("$.data.belowMinRate").value(4.3))
                .andExpect(jsonPath("$.data.maxVoltage").value(250.5))
                .andExpect(jsonPath("$.data.minVoltage").value(190.2))
                .andExpect(jsonPath("$.data.totalReadings").value(10000))
                .andExpect(jsonPath("$.data.qualifiedReadings").value(8550))
                .andExpect(jsonPath("$.data.aboveMaxReadings").value(1020))
                .andExpect(jsonPath("$.data.belowMinReadings").value(430));
    }

    @Test
    public void testCalculateSimulationVoltageQualityRate_NoVoltageData() throws Exception {
        // 准备测试数据 - 无电压数据的情况
        CourtsStatistics mockResult = CourtsStatistics.builder()
                .totalUsers(0)
                .qualifiedRate(0.0)
                .aboveMaxRate(0.0)
                .belowMinRate(0.0)
                .maxVoltage(0.0)
                .minVoltage(0.0)
                .totalReadings(0)
                .qualifiedReadings(0)
                .aboveMaxReadings(0)
                .belowMinReadings(0)
                .build();

        // Mock服务层
        when(simulationService.calculateSimulationVoltageQualityRate(2L)).thenReturn(mockResult);

        // 执行测试
        mockMvc.perform(get("/ngap-server/api/simulations/2/simulation-report")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("计算仿真电压合格率成功"))
                .andExpect(jsonPath("$.data.totalUsers").value(0))
                .andExpect(jsonPath("$.data.qualifiedRate").value(0.0))
                .andExpect(jsonPath("$.data.aboveMaxRate").value(0.0))
                .andExpect(jsonPath("$.data.belowMinRate").value(0.0))
                .andExpect(jsonPath("$.data.totalReadings").value(0))
                .andExpect(jsonPath("$.data.qualifiedReadings").value(0))
                .andExpect(jsonPath("$.data.aboveMaxReadings").value(0))
                .andExpect(jsonPath("$.data.belowMinReadings").value(0));
    }

    @Test
    public void testCalculateSimulationVoltageQualityRate_SimulationNotFound() throws Exception {
        // Mock服务层抛出异常
        when(simulationService.calculateSimulationVoltageQualityRate(999L))
                .thenThrow(new ErrorMsg(-1, "仿真任务不存在"));

        // 执行测试
        mockMvc.perform(get("/ngap-server/api/simulations/999/simulation-report")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(-1))
                .andExpect(jsonPath("$.msg").value("仿真任务不存在"))
                .andExpect(jsonPath("$.data").isEmpty());
    }

    @Test
    public void testCalculateSimulationVoltageQualityRate_InvalidSimulationId() throws Exception {
        // Mock服务层抛出异常
        when(simulationService.calculateSimulationVoltageQualityRate(anyLong()))
                .thenThrow(new ErrorMsg(-1, "仿真ID不能为空或无效"));

        // 执行测试
        mockMvc.perform(get("/ngap-server/api/simulations/0/simulation-report")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(-1))
                .andExpect(jsonPath("$.msg").value("仿真ID不能为空或无效"))
                .andExpect(jsonPath("$.data").isEmpty());
    }

    @Test
    public void testCalculateSimulationVoltageQualityRate_PartialVoltageData() throws Exception {
        // 准备测试数据 - 部分电压数据的情况
        CourtsStatistics mockResult = CourtsStatistics.builder()
                .totalUsers(0)
                .qualifiedRate(75.0)
                .aboveMaxRate(15.0)
                .belowMinRate(10.0)
                .maxVoltage(260.0)
                .minVoltage(180.0)
                .totalReadings(100)
                .qualifiedReadings(75)
                .aboveMaxReadings(15)
                .belowMinReadings(10)
                .build();

        // Mock服务层
        when(simulationService.calculateSimulationVoltageQualityRate(3L)).thenReturn(mockResult);

        // 执行测试
        mockMvc.perform(get("/ngap-server/api/simulations/3/simulation-report")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("计算仿真电压合格率成功"))
                .andExpect(jsonPath("$.data.qualifiedRate").value(75.0))
                .andExpect(jsonPath("$.data.aboveMaxRate").value(15.0))
                .andExpect(jsonPath("$.data.belowMinRate").value(10.0))
                .andExpect(jsonPath("$.data.totalReadings").value(100))
                .andExpect(jsonPath("$.data.qualifiedReadings").value(75))
                .andExpect(jsonPath("$.data.aboveMaxReadings").value(15))
                .andExpect(jsonPath("$.data.belowMinReadings").value(10));
    }

    @Test
    public void testCalculateSimulationVoltageQualityRate_HighQualityData() throws Exception {
        // 准备测试数据 - 高质量电压数据的情况
        CourtsStatistics mockResult = CourtsStatistics.builder()
                .totalUsers(0)
                .qualifiedRate(98.5)
                .aboveMaxRate(1.0)
                .belowMinRate(0.5)
                .maxVoltage(241.0)
                .minVoltage(199.0)
                .totalReadings(1000)
                .qualifiedReadings(985)
                .aboveMaxReadings(10)
                .belowMinReadings(5)
                .build();

        // Mock服务层
        when(simulationService.calculateSimulationVoltageQualityRate(4L)).thenReturn(mockResult);

        // 执行测试
        mockMvc.perform(get("/ngap-server/api/simulations/4/simulation-report")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("计算仿真电压合格率成功"))
                .andExpect(jsonPath("$.data.qualifiedRate").value(98.5))
                .andExpect(jsonPath("$.data.aboveMaxRate").value(1.0))
                .andExpect(jsonPath("$.data.belowMinRate").value(0.5))
                .andExpect(jsonPath("$.data.totalReadings").value(1000))
                .andExpect(jsonPath("$.data.qualifiedReadings").value(985));
    }
}
