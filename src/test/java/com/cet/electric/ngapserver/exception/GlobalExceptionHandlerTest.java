package com.cet.electric.ngapserver.exception;

import com.cet.electric.ngapserver.dto.ErrorMsg;
import com.cet.electric.ngapserver.dto.ResponseDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.servlet.http.HttpServletRequest;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class GlobalExceptionHandlerTest {

    @InjectMocks
    private GlobalExceptionHandler globalExceptionHandler;

    @Mock
    private HttpServletRequest mockRequest;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testExceptionHandler_ErrorMsgException() {
        // 准备测试数据
        ErrorMsg errorMsg = new ErrorMsg(-1, "测试错误消息");
        when(mockRequest.getRequestURI()).thenReturn("/test/api");

        // 执行测试
        ResponseDTO<?> response = globalExceptionHandler.exceptionHandler(mockRequest, errorMsg);

        // 验证结果
        assertNotNull(response);
        assertEquals(-1, response.getCode());
        assertEquals("测试错误消息", response.getMsg());
        assertNull(response.getData());
    }

    @Test
    public void testExceptionHandler_GeneralException() {
        // 准备测试数据
        RuntimeException exception = new RuntimeException("一般异常");
        when(mockRequest.getRequestURI()).thenReturn("/test/api");

        // 执行测试
        ResponseDTO<?> response = globalExceptionHandler.exceptionHandler(mockRequest, exception);

        // 验证结果
        assertNotNull(response);
        assertEquals(-1, response.getCode());
        assertEquals("未知异常", response.getMsg());
        assertNull(response.getData());
    }

    @Test
    public void testExceptionHandler_NullPointerException() {
        // 准备测试数据
        NullPointerException exception = new NullPointerException("空指针异常");
        when(mockRequest.getRequestURI()).thenReturn("/test/api");

        // 执行测试
        ResponseDTO<?> response = globalExceptionHandler.exceptionHandler(mockRequest, exception);

        // 验证结果
        assertNotNull(response);
        assertEquals(-1, response.getCode());
        assertEquals("未知异常", response.getMsg());
        assertNull(response.getData());
    }

    @Test
    public void testExceptionHandler_IllegalArgumentException() {
        // 准备测试数据
        IllegalArgumentException exception = new IllegalArgumentException("非法参数异常");
        when(mockRequest.getRequestURI()).thenReturn("/test/api");

        // 执行测试
        ResponseDTO<?> response = globalExceptionHandler.exceptionHandler(mockRequest, exception);

        // 验证结果
        assertNotNull(response);
        assertEquals(-1, response.getCode());
        assertEquals("未知异常", response.getMsg());
        assertNull(response.getData());
    }

    @Test
    public void testExceptionHandler_ErrorMsgWithDifferentCode() {
        // 准备测试数据
        ErrorMsg errorMsg = new ErrorMsg(500, "服务器内部错误");
        when(mockRequest.getRequestURI()).thenReturn("/test/api");

        // 执行测试
        ResponseDTO<?> response = globalExceptionHandler.exceptionHandler(mockRequest, errorMsg);

        // 验证结果
        assertNotNull(response);
        assertEquals(500, response.getCode());
        assertEquals("服务器内部错误", response.getMsg());
        assertNull(response.getData());
    }

    @Test
    public void testExceptionHandler_ErrorMsgWithEmptyMessage() {
        // 准备测试数据
        ErrorMsg errorMsg = new ErrorMsg(-1, "");
        when(mockRequest.getRequestURI()).thenReturn("/test/api");

        // 执行测试
        ResponseDTO<?> response = globalExceptionHandler.exceptionHandler(mockRequest, errorMsg);

        // 验证结果
        assertNotNull(response);
        assertEquals(-1, response.getCode());
        assertEquals("", response.getMsg());
        assertNull(response.getData());
    }

    @Test
    public void testExceptionHandler_ErrorMsgWithNullMessage() {
        // 准备测试数据
        ErrorMsg errorMsg = new ErrorMsg(-1, null);
        when(mockRequest.getRequestURI()).thenReturn("/test/api");

        // 执行测试
        ResponseDTO<?> response = globalExceptionHandler.exceptionHandler(mockRequest, errorMsg);

        // 验证结果
        assertNotNull(response);
        assertEquals(-1, response.getCode());
        assertNull(response.getMsg());
        assertNull(response.getData());
    }

    @Test
    public void testExceptionHandler_ExceptionWithCause() {
        // 准备测试数据
        Exception cause = new Exception("根本原因");
        RuntimeException exception = new RuntimeException("包装异常", cause);
        when(mockRequest.getRequestURI()).thenReturn("/test/api");

        // 执行测试
        ResponseDTO<?> response = globalExceptionHandler.exceptionHandler(mockRequest, exception);

        // 验证结果
        assertNotNull(response);
        assertEquals(-1, response.getCode());
        assertEquals("未知异常", response.getMsg());
        assertNull(response.getData());
    }

    @Test
    public void testExceptionHandler_ExceptionWithNullMessage() {
        // 准备测试数据
        RuntimeException exception = new RuntimeException((String) null);
        when(mockRequest.getRequestURI()).thenReturn("/test/api");

        // 执行测试
        ResponseDTO<?> response = globalExceptionHandler.exceptionHandler(mockRequest, exception);

        // 验证结果
        assertNotNull(response);
        assertEquals(-1, response.getCode());
        assertEquals("未知异常", response.getMsg());
        assertNull(response.getData());
    }

    @Test
    public void testExceptionHandler_MultipleErrorMsgExceptions() {
        // 测试多个不同的ErrorMsg异常
        ErrorMsg[] errorMsgs = {
                new ErrorMsg(400, "请求参数错误"),
                new ErrorMsg(401, "未授权访问"),
                new ErrorMsg(403, "禁止访问"),
                new ErrorMsg(404, "资源未找到"),
                new ErrorMsg(500, "服务器内部错误")
        };

        when(mockRequest.getRequestURI()).thenReturn("/test/api");

        for (ErrorMsg errorMsg : errorMsgs) {
            // 执行测试
            ResponseDTO<?> response = globalExceptionHandler.exceptionHandler(mockRequest, errorMsg);

            // 验证结果
            assertNotNull(response);
            assertEquals(errorMsg.getCode(), response.getCode());
            assertEquals(errorMsg.getMessage(), response.getMsg());
            assertNull(response.getData());
        }
    }

    @Test
    public void testExceptionHandler_ResponseDTOStructure() {
        // 准备测试数据
        ErrorMsg errorMsg = new ErrorMsg(-1, "测试响应结构");
        when(mockRequest.getRequestURI()).thenReturn("/test/api");

        // 执行测试
        ResponseDTO<?> response = globalExceptionHandler.exceptionHandler(mockRequest, errorMsg);

        // 验证ResponseDTO的结构
        assertNotNull(response);
        assertTrue(response instanceof ResponseDTO);
        
        // 验证字段类型
        assertNotNull(response.getMsg());
        assertTrue(response.getMsg() instanceof String);

        // 验证具体值
        assertEquals(-1, response.getCode());
        assertEquals("测试响应结构", response.getMsg());
        assertNull(response.getData());
        assertNull(response.getTotal());
    }
}
