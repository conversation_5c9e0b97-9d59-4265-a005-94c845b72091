/*
package com.cet.electric.ngapserver.util;

import com.cet.electric.ngapserver.entity.Metric;
import com.cet.electric.ngapserver.entity.MetricData;
import com.cet.electric.ngapserver.util.excel.ExcelCacheManager;
import com.cet.electric.ngapserver.util.voltage.VoltagePerformanceMonitor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.TemporaryFolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

import static org.junit.Assert.*;

*/
/**
 * ExcelUtils性能测试
 * 验证指标提取和数据获取的优化效果
 * 
 * <AUTHOR>
 *//*

public class ExcelUtilsPerformanceTest {
    
    private static final Logger log = LoggerFactory.getLogger(ExcelUtilsPerformanceTest.class);
    
    @Rule
    public TemporaryFolder tempFolder = new TemporaryFolder();
    
    private File smallTestFile;
    private File mediumTestFile;
    private File largeTestFile;
    
    @Before
    public void setUp() throws IOException {
        // 创建不同大小的测试文件
        smallTestFile = createTestFile("small_metrics.xlsx", 5, 50, 10);
        mediumTestFile = createTestFile("medium_metrics.xlsx", 20, 200, 50);
        largeTestFile = createTestFile("large_metrics.xlsx", 50, 1000, 100);
        
        // 清空缓存和性能统计
        ExcelCacheManager.getInstance().clear();
        VoltagePerformanceMonitor.getInstance().reset();
        
        log.info("性能测试环境准备完成");
    }
    
    @After
    public void tearDown() {
        // 输出性能报告
        VoltagePerformanceMonitor.PerformanceReport report = 
            VoltagePerformanceMonitor.getInstance().getPerformanceReport();
        log.info("性能测试报告:\n{}", report);
        
        // 清理
        ExcelCacheManager.getInstance().clear();
        VoltagePerformanceMonitor.getInstance().reset();
    }
    
    @Test
    public void testExtractMetricsPerformance() {
        log.info("=== 指标提取性能测试 ===");
        
        // 测试小文件
        long startTime = System.currentTimeMillis();
        List<Metric> smallMetrics = ExcelUtils.extractMetrics(smallTestFile.getAbsolutePath());
        long smallDuration = System.currentTimeMillis() - startTime;
        
        assertNotNull("小文件指标不应为空", smallMetrics);
        assertTrue("小文件应该有指标", smallMetrics.size() > 0);
        log.info("小文件指标提取: {}ms, 指标数: {}", smallDuration, countTotalMetrics(smallMetrics));
        
        // 测试中等文件
        startTime = System.currentTimeMillis();
        List<Metric> mediumMetrics = ExcelUtils.extractMetrics(mediumTestFile.getAbsolutePath());
        long mediumDuration = System.currentTimeMillis() - startTime;
        
        assertNotNull("中等文件指标不应为空", mediumMetrics);
        assertTrue("中等文件应该有指标", mediumMetrics.size() > 0);
        log.info("中等文件指标提取: {}ms, 指标数: {}", mediumDuration, countTotalMetrics(mediumMetrics));
        
        // 测试大文件
        startTime = System.currentTimeMillis();
        List<Metric> largeMetrics = ExcelUtils.extractMetrics(largeTestFile.getAbsolutePath());
        long largeDuration = System.currentTimeMillis() - startTime;
        
        assertNotNull("大文件指标不应为空", largeMetrics);
        assertTrue("大文件应该有指标", largeMetrics.size() > 0);
        log.info("大文件指标提取: {}ms, 指标数: {}", largeDuration, countTotalMetrics(largeMetrics));
        
        // 验证性能合理性
        assertTrue("大文件处理时间应该合理", largeDuration < 60000); // 60秒内
    }
    
    @Test
    public void testGetMetricDataPerformance() {
        log.info("=== 指标数据获取性能测试 ===");
        
        // 先提取指标
        List<Metric> metrics = ExcelUtils.extractMetrics(mediumTestFile.getAbsolutePath());
        assertNotNull("指标列表不应为空", metrics);
        
        // 找到一个叶子指标进行测试
        Metric testMetric = findLeafMetric(metrics.get(0));
        assertNotNull("应该找到测试指标", testMetric);
        
        // 第一次获取数据（无缓存）
        long startTime1 = System.currentTimeMillis();
        MetricData metricData1 = ExcelUtils.getMetricData(mediumTestFile.getAbsolutePath(), testMetric);
        long duration1 = System.currentTimeMillis() - startTime1;
        
        // 第二次获取数据（有缓存）
        long startTime2 = System.currentTimeMillis();
        MetricData metricData2 = ExcelUtils.getMetricData(mediumTestFile.getAbsolutePath(), testMetric);
        long duration2 = System.currentTimeMillis() - startTime2;
        
        log.info("指标数据获取 - 第一次: {}ms, 第二次: {}ms", duration1, duration2);
        
        // 验证缓存效果
        assertTrue("缓存应该提升性能", duration2 <= duration1);
        
        // 验证缓存统计
        ExcelCacheManager.CacheStats cacheStats = ExcelCacheManager.getInstance().getStats();
        assertTrue("应该有缓存命中", cacheStats.hits > 0);
        log.info("缓存统计: {}", cacheStats);
    }
    
    @Test
    public void testConcurrentAccess() {
        log.info("=== 并发访问性能测试 ===");
        
        String filePath = mediumTestFile.getAbsolutePath();
        
        // 并发提取指标
        Thread[] threads = new Thread[5];
        long[] durations = new long[5];
        
        for (int i = 0; i < threads.length; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                long startTime = System.currentTimeMillis();
                List<Metric> metrics = ExcelUtils.extractMetrics(filePath);
                durations[index] = System.currentTimeMillis() - startTime;
                
                assertNotNull("并发提取的指标不应为空", metrics);
                assertTrue("并发提取应该有指标", metrics.size() > 0);
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                fail("线程被中断");
            }
        }
        
        // 分析并发性能
        long totalDuration = 0;
        for (long duration : durations) {
            totalDuration += duration;
            log.info("并发线程耗时: {}ms", duration);
        }
        
        double avgDuration = totalDuration / (double) durations.length;
        log.info("并发平均耗时: {:.2f}ms", avgDuration);
        
        // 验证缓存效果
        ExcelCacheManager.CacheStats cacheStats = ExcelCacheManager.getInstance().getStats();
        assertTrue("并发访问应该有缓存命中", cacheStats.hits > 0);
        assertTrue("缓存命中率应该合理", cacheStats.hitRate > 0.5);
    }
    
    @Test
    public void testMemoryUsage() {
        log.info("=== 内存使用测试 ===");
        
        Runtime runtime = Runtime.getRuntime();
        
        // 记录初始内存
        runtime.gc();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 处理大文件
        List<Metric> metrics = ExcelUtils.extractMetrics(largeTestFile.getAbsolutePath());
        
        // 记录处理后内存
        runtime.gc();
        long afterProcessingMemory = runtime.totalMemory() - runtime.freeMemory();
        
        long memoryUsed = afterProcessingMemory - initialMemory;
        
        log.info("内存使用: 初始={}MB, 处理后={}MB, 增加={}MB", 
                initialMemory / 1024 / 1024, 
                afterProcessingMemory / 1024 / 1024, 
                memoryUsed / 1024 / 1024);
        
        assertNotNull("指标不应为空", metrics);
        assertTrue("内存使用应该合理", memoryUsed < 200 * 1024 * 1024); // 200MB
    }
    
    */
/**
     * 创建测试文件
     *//*

    private File createTestFile(String fileName, int sheetCount, int rowCount, int columnCount) throws IOException {
        File file = tempFolder.newFile(fileName);
        
        try (Workbook workbook = new XSSFWorkbook()) {
            for (int sheetIndex = 0; sheetIndex < sheetCount; sheetIndex++) {
                Sheet sheet = workbook.createSheet("Sheet" + (sheetIndex + 1));
                
                // 创建表头
                Row headerRow = sheet.createRow(0);
                headerRow.createCell(0).setCellValue("日期");
                headerRow.createCell(1).setCellValue("设备");
                headerRow.createCell(2).setCellValue("位置");
                
                for (int i = 3; i < columnCount; i++) {
                    if (i % 2 == 0) {
                        headerRow.createCell(i).setCellValue("u" + (i - 2));
                    } else {
                        headerRow.createCell(i).setCellValue("p" + (i - 2));
                    }
                }
                
                // 创建数据行
                for (int rowIndex = 1; rowIndex <= rowCount; rowIndex++) {
                    Row row = sheet.createRow(rowIndex);
                    row.createCell(0).setCellValue("2024010" + (rowIndex % 9 + 1));
                    row.createCell(1).setCellValue("设备" + (rowIndex % 5 + 1));
                    row.createCell(2).setCellValue("位置" + (rowIndex % 3 + 1));
                    
                    for (int colIndex = 3; colIndex < columnCount; colIndex++) {
                        if (colIndex % 2 == 0) {
                            // 电压值
                            row.createCell(colIndex).setCellValue(200 + Math.random() * 40);
                        } else {
                            // 功率值
                            row.createCell(colIndex).setCellValue(Math.random() * 1000);
                        }
                    }
                }
            }
            
            try (FileOutputStream fos = new FileOutputStream(file)) {
                workbook.write(fos);
            }
        }
        
        log.info("创建测试文件: {} - 工作表数: {}, 行数: {}, 列数: {}, 文件大小: {}KB", 
                fileName, sheetCount, rowCount, columnCount, file.length() / 1024);
        
        return file;
    }
    
    */
/**
     * 计算指标总数
     *//*

    private int countTotalMetrics(List<Metric> metrics) {
        return metrics.stream()
                .mapToInt(this::countMetricsRecursive)
                .sum();
    }
    
    private int countMetricsRecursive(Metric metric) {
        int count = 1;
        if (metric.getChildren() != null) {
            count += metric.getChildren().stream()
                    .mapToInt(this::countMetricsRecursive)
                    .sum();
        }
        return count;
    }
    
    */
/**
     * 查找叶子指标
     *//*

    private Metric findLeafMetric(Metric metric) {
        if (metric.getChildren() == null || metric.getChildren().isEmpty()) {
            return metric;
        }
        
        for (Metric child : metric.getChildren()) {
            Metric leaf = findLeafMetric(child);
            if (leaf != null) {
                return leaf;
            }
        }
        
        return null;
    }
}
*/
