package com.cet.electric.ngapserver.util;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

import static org.junit.Assert.*;

/**
 * DatabaseUtils 单元测试
 * 
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class DatabaseUtilsTest {
    
    private static final String TEST_DB_PATH = "./test_data/test.db";
    private static final String TEST_JDBC_URL = "jdbc:sqlite:" + TEST_DB_PATH;
    private File testDbFile;
    private File testDataDir;
    
    @Before
    public void setUp() {
        // 创建测试目录和文件引用
        testDataDir = new File("./test_data");
        testDbFile = new File(TEST_DB_PATH);
        
        // 清理可能存在的测试文件
        cleanupTestFiles();
    }
    
    @After
    public void tearDown() {
        // 清理测试文件
        cleanupTestFiles();
    }
    
    private void cleanupTestFiles() {
        if (testDbFile.exists()) {
            testDbFile.delete();
        }
        if (testDataDir.exists()) {
            testDataDir.delete();
        }
    }
    
    @Test
    public void testIsDatabaseExists_WhenFileExists() throws SQLException {
        // 创建测试数据库文件
        testDataDir.mkdirs();
        try (Connection conn = DriverManager.getConnection(TEST_JDBC_URL)) {
            // 文件已创建
        }
        
        // 测试
        boolean exists = DatabaseUtils.isDatabaseExists(TEST_JDBC_URL);
        
        // 验证
        assertTrue("数据库文件应该存在", exists);
    }
    
    @Test
    public void testIsDatabaseExists_WhenFileNotExists() {
        // 确保文件不存在
        assertFalse("测试文件不应该存在", testDbFile.exists());
        
        // 测试
        boolean exists = DatabaseUtils.isDatabaseExists(TEST_JDBC_URL);
        
        // 验证
        assertFalse("数据库文件不应该存在", exists);
    }
    
    @Test
    public void testIsDatabaseExists_WithNullPath() {
        // 测试
        boolean exists = DatabaseUtils.isDatabaseExists(null);
        
        // 验证
        assertFalse("空路径应该返回false", exists);
    }
    
    @Test
    public void testIsDatabaseExists_WithEmptyPath() {
        // 测试
        boolean exists = DatabaseUtils.isDatabaseExists("");
        
        // 验证
        assertFalse("空字符串路径应该返回false", exists);
    }
    
    @Test
    public void testIsDatabaseExists_WithInvalidJdbcUrl() {
        // 测试
        boolean exists = DatabaseUtils.isDatabaseExists("invalid:url");
        
        // 验证
        assertFalse("无效的JDBC URL应该返回false", exists);
    }
    
    @Test
    public void testCreateDatabaseDirectory_WhenDirectoryNotExists() {
        // 确保目录不存在
        assertFalse("测试目录不应该存在", testDataDir.exists());
        
        // 测试
        boolean created = DatabaseUtils.createDatabaseDirectory(TEST_JDBC_URL);
        
        // 验证
        assertTrue("目录创建应该成功", created);
        assertTrue("目录应该存在", testDataDir.exists());
    }
    
    @Test
    public void testCreateDatabaseDirectory_WhenDirectoryExists() {
        // 先创建目录
        testDataDir.mkdirs();
        assertTrue("目录应该已存在", testDataDir.exists());
        
        // 测试
        boolean created = DatabaseUtils.createDatabaseDirectory(TEST_JDBC_URL);
        
        // 验证
        assertTrue("已存在的目录应该返回true", created);
    }
    
    @Test
    public void testTestConnection_WithValidUrl() throws SQLException {
        // 创建测试数据库
        testDataDir.mkdirs();
        try (Connection conn = DriverManager.getConnection(TEST_JDBC_URL)) {
            // 数据库已创建
        }
        
        // 测试
        boolean connected = DatabaseUtils.testConnection(TEST_JDBC_URL);
        
        // 验证
        assertTrue("有效的数据库连接应该成功", connected);
    }
    
    @Test
    public void testTestConnection_WithInvalidUrl() {
        // 测试
        boolean connected = DatabaseUtils.testConnection("jdbc:invalid:url");
        
        // 验证
        assertFalse("无效的数据库连接应该失败", connected);
    }
    
    @Test
    public void testIsTableExists_WhenTableExists() throws SQLException {
        // 创建测试数据库和表
        testDataDir.mkdirs();
        try (Connection conn = DriverManager.getConnection(TEST_JDBC_URL)) {
            conn.createStatement().execute("CREATE TABLE test_table (id INTEGER)");
            
            // 测试
            boolean exists = DatabaseUtils.isTableExists(conn, "test_table");
            
            // 验证
            assertTrue("表应该存在", exists);
        }
    }
    
    @Test
    public void testIsTableExists_WhenTableNotExists() throws SQLException {
        // 创建测试数据库但不创建表
        testDataDir.mkdirs();
        try (Connection conn = DriverManager.getConnection(TEST_JDBC_URL)) {
            
            // 测试
            boolean exists = DatabaseUtils.isTableExists(conn, "non_existent_table");
            
            // 验证
            assertFalse("不存在的表应该返回false", exists);
        }
    }
    
    @Test
    public void testExecuteSqlScript_WithValidScript() throws SQLException, IOException {
        // 创建测试数据库
        testDataDir.mkdirs();
        try (Connection conn = DriverManager.getConnection(TEST_JDBC_URL)) {
            
            // 测试执行SQL脚本（使用现有的init.sql）
            DatabaseUtils.executeSqlScript(conn, "db/init.sql");
            
            // 验证表是否创建成功
            assertTrue("project表应该存在", DatabaseUtils.isTableExists(conn, "project"));
            assertTrue("simulation表应该存在", DatabaseUtils.isTableExists(conn, "simulation"));
            assertTrue("device表应该存在", DatabaseUtils.isTableExists(conn, "device"));
        }
    }
    
    @Test(expected = IOException.class)
    public void testExecuteSqlScript_WithNonExistentScript() throws SQLException, IOException {
        // 创建测试数据库
        testDataDir.mkdirs();
        try (Connection conn = DriverManager.getConnection(TEST_JDBC_URL)) {
            
            // 测试执行不存在的SQL脚本
            DatabaseUtils.executeSqlScript(conn, "db/non_existent.sql");
        }
    }
}
