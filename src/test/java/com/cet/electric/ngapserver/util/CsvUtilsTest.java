package com.cet.electric.ngapserver.util;

import com.cet.electric.ngapserver.entity.DataPoint;
import com.cet.electric.ngapserver.entity.Metric;
import com.cet.electric.ngapserver.entity.MetricData;
import com.cet.electric.ngapserver.entity.MetricStatistics;
import com.cet.electric.ngapserver.util.voltage.VoltageStatisticsCalculator;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.TemporaryFolder;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.*;

/**
 * CsvUtils工具类单元测试
 * 
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CsvUtils.class, ExcelUtils.class})
public class CsvUtilsTest {

    @Rule
    public TemporaryFolder tempFolder = new TemporaryFolder();

    private File testCsvFile;
    private File testDirectory;

    @Before
    public void setUp() throws IOException {
        MockitoAnnotations.initMocks(this);
        
        // 创建测试目录
        testDirectory = tempFolder.newFolder("csv_test");
        
        // 创建测试CSV文件
        testCsvFile = new File(testDirectory, "test_data.csv");
        createTestCsvFile(testCsvFile);
    }

    /**
     * 创建测试用的CSV文件
     */
    private void createTestCsvFile(File csvFile) throws IOException {
        try (PrintWriter writer = new PrintWriter(new FileWriter(csvFile))) {
            writer.println("hour,t(sec),voltage(V),current(A),power(W)");
            writer.println("0,0,220.5,10.2,2249.1");
            writer.println("0,3600,221.0,10.5,2320.5");
            writer.println("1,0,219.8,9.8,2154.04");
            writer.println("1,3600,220.2,10.1,2224.02");
        }
    }

    @Test
    public void testExtractMetrics_Success() {
        // 准备测试数据
        Metric rootMetric = Metric.builder()
                .metricId("root")
                .metricName("根指标")
                .children(new ArrayList<>())
                .build();

        // 执行测试
        CsvUtils.extractMetrics(testDirectory.getAbsolutePath(), rootMetric);

        // 验证结果
        assertNotNull(rootMetric.getChildren());
        assertEquals(1, rootMetric.getChildren().size());
        
        Metric fileMetric = rootMetric.getChildren().get(0);
        assertEquals("test_data", fileMetric.getMetricId());
        assertEquals("test_data", fileMetric.getMetricName());
        assertNotNull(fileMetric.getChildren());
        assertEquals(3, fileMetric.getChildren().size()); // voltage, current, power
        
        // 验证子指标
        Metric voltageMetric = fileMetric.getChildren().get(0);
        assertEquals("test_data.voltage(V)", voltageMetric.getMetricId());
        assertEquals("voltage(V)", voltageMetric.getMetricName());
        assertNull(voltageMetric.getChildren());
    }

    @Test
    public void testExtractMetrics_DirectoryNotExists() {
        // 准备测试数据
        Metric rootMetric = Metric.builder()
                .metricId("root")
                .metricName("根指标")
                .children(new ArrayList<>())
                .build();

        // 执行测试 - 目录不存在
        CsvUtils.extractMetrics("/non/existing/directory", rootMetric);

        // 验证结果 - 应该没有添加任何子指标
        assertNotNull(rootMetric.getChildren());
        assertEquals(0, rootMetric.getChildren().size());
    }

    @Test
    public void testExtractMetrics_EmptyDirectory() throws IOException {
        // 创建空目录
        File emptyDir = tempFolder.newFolder("empty_dir");
        
        Metric rootMetric = Metric.builder()
                .metricId("root")
                .metricName("根指标")
                .children(new ArrayList<>())
                .build();

        // 执行测试
        CsvUtils.extractMetrics(emptyDir.getAbsolutePath(), rootMetric);

        // 验证结果
        assertNotNull(rootMetric.getChildren());
        assertEquals(0, rootMetric.getChildren().size());
    }

    @Test
    public void testExtractMetrics_NoCSVFiles() throws IOException {
        // 创建包含非CSV文件的目录
        File dirWithNonCsv = tempFolder.newFolder("non_csv_dir");
        File txtFile = new File(dirWithNonCsv, "test.txt");
        txtFile.createNewFile();
        
        Metric rootMetric = Metric.builder()
                .metricId("root")
                .metricName("根指标")
                .children(new ArrayList<>())
                .build();

        // 执行测试
        CsvUtils.extractMetrics(dirWithNonCsv.getAbsolutePath(), rootMetric);

        // 验证结果
        assertNotNull(rootMetric.getChildren());
        assertEquals(0, rootMetric.getChildren().size());
    }

    @Test
    public void testExtractMetrics_CSVWithQuotedHeaders() throws IOException {
        // 创建包含引号的CSV文件
        File quotedCsvFile = new File(testDirectory, "quoted_headers.csv");
        try (PrintWriter writer = new PrintWriter(new FileWriter(quotedCsvFile))) {
            writer.println("hour,t(sec),\"voltage(V)\",\"current(A)\"");
            writer.println("0,0,220.5,10.2");
        }
        
        Metric rootMetric = Metric.builder()
                .metricId("root")
                .metricName("根指标")
                .children(new ArrayList<>())
                .build();

        // 执行测试
        CsvUtils.extractMetrics(testDirectory.getAbsolutePath(), rootMetric);

        // 验证结果
        assertNotNull(rootMetric.getChildren());
        assertEquals(2, rootMetric.getChildren().size()); // test_data + quoted_headers
        
        // 找到quoted_headers文件的指标
        Metric quotedFileMetric = rootMetric.getChildren().stream()
                .filter(m -> "quoted_headers".equals(m.getMetricId()))
                .findFirst()
                .orElse(null);
        
        assertNotNull(quotedFileMetric);
        assertEquals(2, quotedFileMetric.getChildren().size());
        
        // 验证引号被正确移除
        Metric voltageMetric = quotedFileMetric.getChildren().get(0);
        assertEquals("voltage(V)", voltageMetric.getMetricName());
    }

    @Test
    public void testExtractMetrics_CSVReadError() throws IOException {
        // 创建一个无法读取的CSV文件（通过创建目录同名）
        File unreadableDir = new File(testDirectory, "unreadable.csv");
        unreadableDir.mkdir();
        
        Metric rootMetric = Metric.builder()
                .metricId("root")
                .metricName("根指标")
                .children(new ArrayList<>())
                .build();

        // 执行测试
        CsvUtils.extractMetrics(testDirectory.getAbsolutePath(), rootMetric);

        // 验证结果 - 应该只有原来的test_data文件
        assertNotNull(rootMetric.getChildren());
        assertEquals(1, rootMetric.getChildren().size());
        assertEquals("test_data", rootMetric.getChildren().get(0).getMetricId());
    }

    @Test
    public void testGetMetricData_Success() throws Exception {
        // Mock ExcelUtils.calculateStatistics
        MetricStatistics mockStatistics = MetricStatistics.builder()
                .min(219.8)
                .max(221.0)
                .rate(100.0)
                .build();
        mockStatic(ExcelUtils.class);
        when(ExcelUtils.calculateStatistics(any(), any())).thenReturn(mockStatistics);

        // 执行测试
        List<MetricData> result = CsvUtils.getMetricData(
                testCsvFile.getAbsolutePath(), 
                "test_data.voltage(V)", 
                System.currentTimeMillis()
        );

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        MetricData metricData = result.get(0);
        assertNotNull(metricData.getMetric());
        assertEquals("test_data.voltage(V)", metricData.getMetric().getMetricId());
        assertEquals("voltage(V)", metricData.getMetric().getMetricName());
        assertEquals("V", metricData.getYUnit());
        assertNull(metricData.getXUnit());
        
        assertNotNull(metricData.getDataPoints());
        assertEquals(4, metricData.getDataPoints().size());
        
        // 验证数据点
        DataPoint firstPoint = metricData.getDataPoints().get(0);
        assertEquals(Double.valueOf(220.5), firstPoint.getValue());
        assertNotNull(firstPoint.getTimestamp());
        
        assertNotNull(metricData.getStatistics());
        assertEquals(mockStatistics, metricData.getStatistics());
    }

    @Test
    public void testGetMetricData_InvalidMetricId() {
        // 执行测试 - 无效的指标ID格式
        List<MetricData> result = CsvUtils.getMetricData(
                testCsvFile.getAbsolutePath(), 
                "invalid_metric_id", 
                System.currentTimeMillis()
        );

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetMetricData_FileNotExists() {
        // 执行测试 - 文件不存在
        List<MetricData> result = CsvUtils.getMetricData(
                "/non/existing/file.csv", 
                "test.voltage", 
                System.currentTimeMillis()
        );

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetMetricData_ColumnNotFound() throws Exception {
        // Mock ExcelUtils.calculateStatistics
        mockStatic(ExcelUtils.class);
        when(ExcelUtils.calculateStatistics(any(), any())).thenReturn(null);

        // 执行测试 - 列不存在
        List<MetricData> result = CsvUtils.getMetricData(
                testCsvFile.getAbsolutePath(), 
                "test_data.non_existing_column", 
                System.currentTimeMillis()
        );

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetMetricData_NullStartTimestamp() throws Exception {
        // Mock ExcelUtils.calculateStatistics
        MetricStatistics mockStatistics = MetricStatistics.builder()
                .min(219.8)
                .max(221.0)
                .build();
        mockStatic(ExcelUtils.class);
        when(ExcelUtils.calculateStatistics(any(), any())).thenReturn(mockStatistics);

        // 执行测试 - startTimestamp为null
        List<MetricData> result = CsvUtils.getMetricData(
                testCsvFile.getAbsolutePath(),
                "test_data.voltage(V)",
                null
        );

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());

        MetricData metricData = result.get(0);
        assertNotNull(metricData.getDataPoints());
        assertEquals(4, metricData.getDataPoints().size());

        // 验证时间戳格式
        DataPoint firstPoint = metricData.getDataPoints().get(0);
        assertNotNull(firstPoint.getTimestamp());
        assertTrue(firstPoint.getTimestamp().matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}"));
    }

    @Test
    public void testGetMetricData_EmptyFile() throws IOException {
        // 创建空CSV文件
        File emptyFile = new File(testDirectory, "empty.csv");
        emptyFile.createNewFile();

        // 执行测试
        try {
            CsvUtils.getMetricData(emptyFile.getAbsolutePath(), "test.column", System.currentTimeMillis());
            fail("应该抛出RuntimeException");
        } catch (RuntimeException e) {
            assertTrue(e.getMessage().contains("CSV文件为空"));
        }
    }

    @Test
    public void testGetMetricData_IOError() throws IOException {
        // 创建一个目录而不是文件来模拟IO错误
        File dirAsFile = new File(testDirectory, "dir_as_file.csv");
        dirAsFile.mkdir();

        // 执行测试
        List<MetricData> result = CsvUtils.getMetricData(
                dirAsFile.getAbsolutePath(),
                "test.column",
                System.currentTimeMillis()
        );

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetMetricData_WithInvalidDataRows() throws Exception {
        // 创建包含无效数据行的CSV文件
        File invalidDataFile = new File(testDirectory, "invalid_data.csv");
        try (PrintWriter writer = new PrintWriter(new FileWriter(invalidDataFile))) {
            writer.println("hour,t(sec),voltage(V)");
            writer.println("0,0,220.5");
            writer.println("invalid,data,row");
            writer.println("1,3600,221.0");
            writer.println("2,abc,222.0"); // 无效秒数
            writer.println("3,7200,invalid"); // 无效电压值
        }

        // Mock ExcelUtils.calculateStatistics
        MetricStatistics mockStatistics = MetricStatistics.builder().build();
        mockStatic(ExcelUtils.class);
        when(ExcelUtils.calculateStatistics(any(), any())).thenReturn(mockStatistics);

        // 执行测试
        List<MetricData> result = CsvUtils.getMetricData(
                invalidDataFile.getAbsolutePath(),
                "invalid_data.voltage(V)",
                System.currentTimeMillis()
        );

        // 验证结果 - 应该只有有效的数据行
        assertNotNull(result);
        assertEquals(1, result.size());

        MetricData metricData = result.get(0);
        assertNotNull(metricData.getDataPoints());
        assertEquals(2, metricData.getDataPoints().size()); // 只有2行有效数据

        // 验证有效数据点
        assertEquals(Double.valueOf(220.5), metricData.getDataPoints().get(0).getValue());
        assertEquals(Double.valueOf(221.0), metricData.getDataPoints().get(1).getValue());
    }

    @Test
    public void testParseCSVLine_SimpleCase() {
        // 执行测试 - 简单情况
        String[] result = CsvUtils.parseCSVLine("a,b,c");

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.length);
        assertEquals("a", result[0]);
        assertEquals("b", result[1]);
        assertEquals("c", result[2]);
    }

    @Test
    public void testParseCSVLine_WithQuotes() {
        // 执行测试 - 包含引号
        String[] result = CsvUtils.parseCSVLine("\"a,b\",c,\"d\"");

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.length);
        assertEquals("a,b", result[0]);
        assertEquals("c", result[1]);
        assertEquals("d", result[2]);
    }

    @Test
    public void testParseCSVLine_WithQuotedCommas() {
        // 执行测试 - 引号内包含逗号
        String[] result = CsvUtils.parseCSVLine("\"field1,with,commas\",field2,\"field3\"");

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.length);
        assertEquals("field1,with,commas", result[0]);
        assertEquals("field2", result[1]);
        assertEquals("field3", result[2]);
    }

    @Test
    public void testParseCSVLine_EmptyFields() {
        // 执行测试 - 空字段
        String[] result = CsvUtils.parseCSVLine("a,,c");

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.length);
        assertEquals("a", result[0]);
        assertEquals("", result[1]);
        assertEquals("c", result[2]);
    }

    @Test
    public void testParseCSVLine_EmptyString() {
        // 执行测试 - 空字符串
        String[] result = CsvUtils.parseCSVLine("");

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.length);
        assertEquals("", result[0]);
    }

    @Test
    public void testParseCSVLine_SingleField() {
        // 执行测试 - 单个字段
        String[] result = CsvUtils.parseCSVLine("single_field");

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.length);
        assertEquals("single_field", result[0]);
    }

    @Test
    public void testParseCSVLine_ComplexQuoting() {
        // 执行测试 - 复杂引号情况
        String[] result = CsvUtils.parseCSVLine("\"field1\",\"field,2\",field3,\"field\"\"4\"");

        // 验证结果
        assertNotNull(result);
        assertEquals(4, result.length);
        assertEquals("field1", result[0]);
        assertEquals("field,2", result[1]);
        assertEquals("field3", result[2]);
        assertEquals("field4", result[3]);
    }

    @Test
    public void testGetMetricData_YUnitExtraction() throws Exception {
        // 创建包含单位信息的CSV文件
        File unitFile = new File(testDirectory, "unit_test.csv");
        try (PrintWriter writer = new PrintWriter(new FileWriter(unitFile))) {
            writer.println("hour,t(sec),temperature(°C),pressure(Pa),speed(m/s)");
            writer.println("0,0,25.5,101325,10.2");
        }

        // Mock ExcelUtils.calculateStatistics
        MetricStatistics mockStatistics = MetricStatistics.builder().build();
        mockStatic(ExcelUtils.class);
        when(ExcelUtils.calculateStatistics(any(), any())).thenReturn(mockStatistics);

        // 测试温度单位提取
        List<MetricData> tempResult = CsvUtils.getMetricData(
                unitFile.getAbsolutePath(),
                "unit_test.temperature(°C)",
                System.currentTimeMillis()
        );
        assertEquals("°C", tempResult.get(0).getYUnit());

        // 测试压力单位提取
        List<MetricData> pressureResult = CsvUtils.getMetricData(
                unitFile.getAbsolutePath(),
                "unit_test.pressure(Pa)",
                System.currentTimeMillis()
        );
        assertEquals("Pa", pressureResult.get(0).getYUnit());

        // 测试速度单位提取
        List<MetricData> speedResult = CsvUtils.getMetricData(
                unitFile.getAbsolutePath(),
                "unit_test.speed(m/s)",
                System.currentTimeMillis()
        );
        assertEquals("m/s", speedResult.get(0).getYUnit());
    }

    @Test
    public void testGetMetricData_NoUnitInColumnName() throws Exception {
        // 创建不包含单位信息的CSV文件
        File noUnitFile = new File(testDirectory, "no_unit.csv");
        try (PrintWriter writer = new PrintWriter(new FileWriter(noUnitFile))) {
            writer.println("hour,t(sec),temperature,pressure");
            writer.println("0,0,25.5,101325");
        }

        // Mock ExcelUtils.calculateStatistics
        MetricStatistics mockStatistics = MetricStatistics.builder().build();
        mockStatic(ExcelUtils.class);
        when(ExcelUtils.calculateStatistics(any(), any())).thenReturn(mockStatistics);

        // 执行测试
        List<MetricData> result = CsvUtils.getMetricData(
                noUnitFile.getAbsolutePath(),
                "no_unit.temperature",
                System.currentTimeMillis()
        );

        // 验证结果 - yUnit应该为null
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNull(result.get(0).getYUnit());
    }

    // ==================== 电压数据提取测试方法 ====================

    @Test
    public void testExtractVoltageDataFromCSVDirectory_WithVoltageData() throws IOException {
        // 清理测试目录
        cleanTestDirectory();

        // 创建包含电压数据的CSV文件
        createCSVFile("voltage_test.csv",
                "time,voltage,current,power\n" +
                "1,220.0,10.5,2310\n" +
                "2,200.0,11.0,2200\n" +
                "3,250.0,9.8,2450\n" +
                "4,190.0,12.1,2299\n" +
                "5,230.0,10.2,2346\n");

        // 执行测试
        VoltageStatisticsCalculator calculator = CsvUtils.extractVoltageDataFromCSVDirectory(testDirectory.getAbsolutePath());

        // 验证结果
        assertNotNull(calculator);
        assertEquals(5, calculator.getTotalReadings());
        assertEquals(3, calculator.getQualifiedReadings()); // 220, 200, 230
        assertEquals(1, calculator.getAboveMaxReadings()); // 250
        assertEquals(1, calculator.getBelowMinReadings()); // 190
        assertEquals(60.0, calculator.getQualifiedRate(), 0.01);
        assertEquals(20.0, calculator.getAboveMaxRate(), 0.01);
        assertEquals(20.0, calculator.getBelowMinRate(), 0.01);
        assertEquals(250.0, calculator.getMaxVoltage(), 0.01);
        assertEquals(190.0, calculator.getMinVoltage(), 0.01);
    }

    @Test
    public void testExtractVoltageDataFromCSVDirectory_MultipleVoltageColumns() throws IOException {
        // 清理测试目录
        cleanTestDirectory();

        // 创建包含多个电压列的CSV文件
        createCSVFile("multi_voltage.csv",
                "time,V1,V2,current,U_phase\n" +
                "1,220.0,215.0,10.5,225.0\n" +
                "2,200.0,205.0,11.0,210.0\n" +
                "3,250.0,245.0,9.8,240.0\n");

        // 执行测试
        VoltageStatisticsCalculator calculator = CsvUtils.extractVoltageDataFromCSVDirectory(testDirectory.getAbsolutePath());

        // 验证结果 - 应该处理所有电压列（V1, V2, U_phase）
        assertNotNull(calculator);
        assertEquals(9, calculator.getTotalReadings()); // 3行 × 3个电压列
        assertTrue(calculator.getQualifiedReadings() > 0);
        assertTrue(calculator.getAboveMaxReadings() > 0);
    }

    @Test
    public void testExtractVoltageDataFromCSVDirectory_NoVoltageColumns() throws IOException {
        // 清理测试目录
        cleanTestDirectory();

        // 创建不包含电压列的CSV文件
        createCSVFile("no_voltage.csv",
                "time,current,power,frequency\n" +
                "1,10.5,2310,50.0\n" +
                "2,11.0,2200,50.1\n" +
                "3,9.8,2450,49.9\n");

        // 执行测试
        VoltageStatisticsCalculator calculator = CsvUtils.extractVoltageDataFromCSVDirectory(testDirectory.getAbsolutePath());

        // 验证结果 - 应该没有电压数据
        assertNotNull(calculator);
        assertEquals(0, calculator.getTotalReadings());
        assertEquals(0, calculator.getQualifiedReadings());
        assertEquals(0.0, calculator.getQualifiedRate(), 0.01);
    }

    @Test
    public void testExtractVoltageDataFromCSVDirectory_EmptyDirectory() throws IOException {
        // 创建空目录
        File emptyDir = tempFolder.newFolder("empty_voltage_dir");

        // 测试空目录
        VoltageStatisticsCalculator calculator = CsvUtils.extractVoltageDataFromCSVDirectory(emptyDir.getAbsolutePath());

        // 验证结果
        assertNotNull(calculator);
        assertEquals(0, calculator.getTotalReadings());
        assertEquals(0, calculator.getQualifiedReadings());
        assertEquals(0.0, calculator.getQualifiedRate(), 0.01);
    }

    @Test
    public void testExtractVoltageDataFromCSVDirectory_NonExistentDirectory() {
        // 测试不存在的目录
        VoltageStatisticsCalculator calculator = CsvUtils.extractVoltageDataFromCSVDirectory("/non/existent/path");

        // 验证结果
        assertNotNull(calculator);
        assertEquals(0, calculator.getTotalReadings());
        assertEquals(0, calculator.getQualifiedReadings());
        assertEquals(0.0, calculator.getQualifiedRate(), 0.01);
    }

    @Test
    public void testExtractVoltageDataFromCSVDirectory_InvalidVoltageValues() throws IOException {
        // 清理测试目录
        cleanTestDirectory();

        // 创建包含无效电压值的CSV文件
        createCSVFile("invalid_voltage.csv",
                "time,voltage,current\n" +
                "1,220.0,10.5\n" +
                "2,invalid,11.0\n" +
                "3,,9.8\n" +
                "4,230.0,10.2\n");

        // 执行测试
        VoltageStatisticsCalculator calculator = CsvUtils.extractVoltageDataFromCSVDirectory(testDirectory.getAbsolutePath());

        // 验证结果 - 应该只处理有效的电压值
        assertNotNull(calculator);
        assertEquals(2, calculator.getTotalReadings()); // 只有220.0和230.0
        assertEquals(2, calculator.getQualifiedReadings());
        assertEquals(100.0, calculator.getQualifiedRate(), 0.01);
    }

    @Test
    public void testExtractVoltageDataFromCSVDirectory_MixedFileTypes() throws IOException {
        // 清理测试目录
        cleanTestDirectory();

        // 创建CSV文件
        createCSVFile("voltage_data.csv",
                "time,voltage\n" +
                "1,220.0\n" +
                "2,230.0\n");

        // 创建非CSV文件
        createFile("data.txt", "This is not a CSV file");
        createFile("config.json", "{\"key\": \"value\"}");

        // 执行测试
        VoltageStatisticsCalculator calculator = CsvUtils.extractVoltageDataFromCSVDirectory(testDirectory.getAbsolutePath());

        // 验证结果 - 应该只处理CSV文件
        assertNotNull(calculator);
        assertEquals(2, calculator.getTotalReadings());
        assertEquals(2, calculator.getQualifiedReadings());
        assertEquals(100.0, calculator.getQualifiedRate(), 0.01);
    }

    @Test
    public void testIsVoltageColumn_VariousFormats() {
        // 测试各种电压列名格式
        assertTrue(CsvUtils.isVoltageColumn("voltage"));
        assertTrue(CsvUtils.isVoltageColumn("Voltage"));
        assertTrue(CsvUtils.isVoltageColumn("VOLTAGE"));
        assertTrue(CsvUtils.isVoltageColumn("volt"));
        assertTrue(CsvUtils.isVoltageColumn("Volt"));
        assertTrue(CsvUtils.isVoltageColumn("电压"));
        assertTrue(CsvUtils.isVoltageColumn("V"));
        assertTrue(CsvUtils.isVoltageColumn("U"));
        assertTrue(CsvUtils.isVoltageColumn("kV"));
        assertTrue(CsvUtils.isVoltageColumn("mV"));
        assertTrue(CsvUtils.isVoltageColumn("KV"));
        assertTrue(CsvUtils.isVoltageColumn("MV"));
        assertTrue(CsvUtils.isVoltageColumn("v_rms"));
        assertTrue(CsvUtils.isVoltageColumn("V_RMS"));
        assertTrue(CsvUtils.isVoltageColumn("u_phase"));
        assertTrue(CsvUtils.isVoltageColumn("U_PHASE"));
        assertTrue(CsvUtils.isVoltageColumn("line_v"));
        assertTrue(CsvUtils.isVoltageColumn("LINE_V"));
        assertTrue(CsvUtils.isVoltageColumn("phase_u"));
        assertTrue(CsvUtils.isVoltageColumn("PHASE_U"));
        assertTrue(CsvUtils.isVoltageColumn("voltage_a"));
        assertTrue(CsvUtils.isVoltageColumn("voltage_b"));
        assertTrue(CsvUtils.isVoltageColumn("voltage_c"));

        // 测试非电压列
        assertFalse(CsvUtils.isVoltageColumn("current"));
        assertFalse(CsvUtils.isVoltageColumn("power"));
        assertFalse(CsvUtils.isVoltageColumn("frequency"));
        assertFalse(CsvUtils.isVoltageColumn("time"));
        assertFalse(CsvUtils.isVoltageColumn("value"));
        assertFalse(CsvUtils.isVoltageColumn("variable"));
        assertFalse(CsvUtils.isVoltageColumn(""));
        assertFalse(CsvUtils.isVoltageColumn(null));
        assertFalse(CsvUtils.isVoltageColumn("   "));
    }

    @Test
    public void testExtractVoltageDataFromCSVDirectory_QuotedHeaders() throws IOException {
        // 清理测试目录
        cleanTestDirectory();

        // 创建包含引号的CSV文件头
        createCSVFile("quoted_headers.csv",
                "\"time\",\"voltage\",\"current\"\n" +
                "1,220.0,10.5\n" +
                "2,200.0,11.0\n");

        // 执行测试
        VoltageStatisticsCalculator calculator = CsvUtils.extractVoltageDataFromCSVDirectory(testDirectory.getAbsolutePath());

        // 验证结果
        assertNotNull(calculator);
        assertEquals(2, calculator.getTotalReadings());
        assertEquals(2, calculator.getQualifiedReadings());
        assertEquals(100.0, calculator.getQualifiedRate(), 0.01);
    }

    // ==================== 电压过滤测试方法 ====================

    @Test
    public void testIsVoltageColumn_ExcludeAngleAndPowerColumns() {
        // 测试角度列应该被排除
        assertFalse(CsvUtils.isVoltageColumn("VAngle1"));
        assertFalse(CsvUtils.isVoltageColumn("VAngle2"));
        assertFalse(CsvUtils.isVoltageColumn("IAngle1"));
        assertFalse(CsvUtils.isVoltageColumn("IAngle2"));
        assertFalse(CsvUtils.isVoltageColumn("vangle1"));
        assertFalse(CsvUtils.isVoltageColumn("iangle1"));

        // 测试功率列应该被排除
        assertFalse(CsvUtils.isVoltageColumn("P1 (kW)"));
        assertFalse(CsvUtils.isVoltageColumn("Q1 (kvar)"));
        assertFalse(CsvUtils.isVoltageColumn("P2 (kW)"));
        assertFalse(CsvUtils.isVoltageColumn("Q2 (kvar)"));
        assertFalse(CsvUtils.isVoltageColumn("P3"));
        assertFalse(CsvUtils.isVoltageColumn("Q3"));
        assertFalse(CsvUtils.isVoltageColumn("p1"));
        assertFalse(CsvUtils.isVoltageColumn("q1"));
        assertFalse(CsvUtils.isVoltageColumn("power"));
        assertFalse(CsvUtils.isVoltageColumn("功率"));

        // 测试视在功率列应该被排除
        assertFalse(CsvUtils.isVoltageColumn("S1 (kVA)"));
        assertFalse(CsvUtils.isVoltageColumn("S2 (kVA)"));
        assertFalse(CsvUtils.isVoltageColumn("S3 (kVA)"));
        assertFalse(CsvUtils.isVoltageColumn("s1"));
        assertFalse(CsvUtils.isVoltageColumn("s2"));
        assertFalse(CsvUtils.isVoltageColumn("\"S1 (kVA)\""));
        assertFalse(CsvUtils.isVoltageColumn("\"S2 (kVA)\""));
        assertFalse(CsvUtils.isVoltageColumn("\"S3 (kVA)\""));

        // 测试正常电压列应该被包含
        assertTrue(CsvUtils.isVoltageColumn("V1"));
        assertTrue(CsvUtils.isVoltageColumn("V2"));
        assertTrue(CsvUtils.isVoltageColumn("V3"));
        assertTrue(CsvUtils.isVoltageColumn("voltage"));
        assertTrue(CsvUtils.isVoltageColumn("U1"));
    }

    @Test
    public void testExtractVoltageDataFromCSVDirectory_FilterAngleAndZeroColumns() throws IOException {
        // 清理测试目录
        cleanTestDirectory();

        // 创建模拟仿真数据的CSV文件
        String csvContent = "hour,t(sec),V1,VAngle1,V2,VAngle2,V3,VAngle3,V4,VAngle4,I1,IAngle1\n" +
                           "0,900.00000,223.304,-30.057,223.326,-150.054,223.148,89.9157,0,0,0,0\n" +
                           "0,1800.00000,221.667,-30.288,221.808,-150.266,221.867,89.745,0,0,0,0\n" +
                           "0,2700.00000,222.156,-30.2153,222.194,-150.208,221.947,89.7527,0,0,0,0\n" +
                           "1,0.00000,222.994,-30.1041,223.179,-150.074,222.933,89.8855,0,0,0,0\n";

        createCSVFile("simulation_test.csv", csvContent);

        // 执行测试
        VoltageStatisticsCalculator calculator = CsvUtils.extractVoltageDataFromCSVDirectory(testDirectory.getAbsolutePath());

        // 验证结果
        assertNotNull(calculator);

        // 应该只处理V1, V2, V3列的数据（V4全为0被过滤，VAngle列被排除）
        // 4行数据 × 3个有效电压列 = 12个电压值
        assertEquals(12, calculator.getTotalReadings());

        // 所有电压值都在合格范围内
        assertEquals(12, calculator.getQualifiedReadings());
        assertEquals(100.0, calculator.getQualifiedRate(), 0.01);
    }

    @Test
    public void testExtractVoltageDataFromCSVDirectory_OnlyZeroColumns() throws IOException {
        // 清理测试目录
        cleanTestDirectory();

        // 创建只有全0电压列的CSV文件
        String csvContent = "hour,t(sec),V1,V2,current\n" +
                           "0,900.00000,0,0,10.5\n" +
                           "0,1800.00000,0,0,11.0\n" +
                           "0,2700.00000,0,0,9.8\n";

        createCSVFile("zero_voltage_test.csv", csvContent);

        // 执行测试
        VoltageStatisticsCalculator calculator = CsvUtils.extractVoltageDataFromCSVDirectory(testDirectory.getAbsolutePath());

        // 验证结果 - 应该没有电压数据被处理
        assertNotNull(calculator);
        assertEquals(0, calculator.getTotalReadings());
        assertEquals(0, calculator.getQualifiedReadings());
        assertEquals(0.0, calculator.getQualifiedRate(), 0.01);
    }

    @Test
    public void testExtractVoltageDataFromCSVDirectory_MixedValidAndZeroColumns() throws IOException {
        // 清理测试目录
        cleanTestDirectory();

        // 创建包含有效列和全0列的CSV文件
        String csvContent = "hour,t(sec),V1,V2,V3,current\n" +
                           "0,900.00000,220.0,0,230.0,10.5\n" +
                           "0,1800.00000,225.0,0,235.0,11.0\n" +
                           "0,2700.00000,215.0,0,225.0,9.8\n";

        createCSVFile("mixed_voltage_test.csv", csvContent);

        // 执行测试
        VoltageStatisticsCalculator calculator = CsvUtils.extractVoltageDataFromCSVDirectory(testDirectory.getAbsolutePath());

        // 验证结果 - 应该只处理V1和V3列（V2全为0被过滤）
        assertNotNull(calculator);
        assertEquals(6, calculator.getTotalReadings()); // 3行 × 2个有效列
        assertEquals(6, calculator.getQualifiedReadings());
        assertEquals(100.0, calculator.getQualifiedRate(), 0.01);
    }

    @Test
    public void testExtractVoltageDataFromCSVDirectory_ExcludePowerColumns() throws IOException {
        // 清理测试目录
        cleanTestDirectory();

        // 创建包含电压列和功率列的CSV文件
        String csvContent = "hour,t(sec),V1,VAngle1,V2,VAngle2,P1 (kW),Q1 (kvar),P2 (kW),Q2 (kvar)\n" +
                           "0,900.00000,220.0,-30.057,225.0,-150.054,100.5,50.2,105.3,52.1\n" +
                           "0,1800.00000,225.0,-30.288,230.0,-150.266,102.1,51.5,107.8,53.4\n" +
                           "0,2700.00000,215.0,-30.215,220.0,-150.208,98.7,49.8,103.2,51.9\n";

        createCSVFile("power_voltage_test.csv", csvContent);

        // 执行测试
        VoltageStatisticsCalculator calculator = CsvUtils.extractVoltageDataFromCSVDirectory(testDirectory.getAbsolutePath());

        // 验证结果 - 应该只处理V1和V2列（排除VAngle和功率列）
        assertNotNull(calculator);
        assertEquals(6, calculator.getTotalReadings()); // 3行 × 2个电压列
        assertEquals(6, calculator.getQualifiedReadings());
        assertEquals(100.0, calculator.getQualifiedRate(), 0.01);
    }

    // ==================== 真实数据测试方法 ====================

    @Test
    public void testExtractVoltageDataFromRealSimulationCSV() throws IOException {
        // 清理测试目录
        cleanTestDirectory();

        // 创建模拟真实仿真数据的CSV文件
        String csvContent = "hour,t(sec),V1,VAngle1,V2,VAngle2,V3,VAngle3,V4,VAngle4,I1,IAngle1,I2,IAngle2,I3,IAngle3,I4,IAngle4\n" +
                           "0, 900.00000, 223.304, -30.057, 223.326, -150.054, 223.148, 89.9157, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0\n" +
                           "0, 1800.00000, 221.667, -30.288, 221.808, -150.266, 221.867, 89.745, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0\n" +
                           "0, 2700.00000, 222.156, -30.2153, 222.194, -150.208, 221.947, 89.7527, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0\n" +
                           "1, 0.00000, 222.994, -30.1041, 223.179, -150.074, 222.933, 89.8855, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0\n" +
                           "1, 900.00000, 221.515, -30.3132, 221.87, -150.257, 221.902, 89.7508, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0\n";

        createCSVFile("circuit_Mon_monitor_generator_1_vi_1.csv", csvContent);

        // 执行测试
        VoltageStatisticsCalculator calculator = CsvUtils.extractVoltageDataFromCSVDirectory(testDirectory.getAbsolutePath());

        // 验证结果
        assertNotNull(calculator);

        // 应该只处理V1, V2, V3列的数据（V4全为0被过滤，VAngle和IAngle列被排除）
        // 5行数据 × 3个有效电压列 = 15个电压值
        assertEquals(15, calculator.getTotalReadings());

        // 所有电压值都在合格范围内（220V左右）
        assertEquals(15, calculator.getQualifiedReadings());
        assertEquals(100.0, calculator.getQualifiedRate(), 0.01);

        // 验证电压范围
        assertTrue(calculator.getMaxVoltage() > 220.0);
        assertTrue(calculator.getMinVoltage() > 220.0);
        assertTrue(calculator.getMaxVoltage() < 225.0);
    }

    @Test
    public void testExtractVoltageDataWithPowerColumns() throws IOException {
        // 清理测试目录
        cleanTestDirectory();

        // 创建包含功率列的CSV文件
        String csvContent = "hour,t(sec),V1,VAngle1,V2,VAngle2,P1 (kW),Q1 (kvar),P2 (kW),Q2 (kvar),P3 (kW),Q3 (kvar),P4 (kW),Q4 (kvar)\n" +
                           "0, 900.00000, 223.304, -30.057, 223.326, -150.054, 100.5, 50.2, 105.3, 52.1, 0, 0, 0, 0\n" +
                           "0, 1800.00000, 221.667, -30.288, 221.808, -150.266, 102.1, 51.5, 107.8, 53.4, 0, 0, 0, 0\n" +
                           "0, 2700.00000, 222.156, -30.2153, 222.194, -150.208, 98.7, 49.8, 103.2, 51.9, 0, 0, 0, 0\n";

        createCSVFile("circuit_with_power.csv", csvContent);

        // 执行测试
        VoltageStatisticsCalculator calculator = CsvUtils.extractVoltageDataFromCSVDirectory(testDirectory.getAbsolutePath());

        // 验证结果
        assertNotNull(calculator);

        // 应该只处理V1和V2列的数据（排除VAngle列和功率列）
        // 3行数据 × 2个有效电压列 = 6个电压值
        assertEquals(6, calculator.getTotalReadings());
        assertEquals(6, calculator.getQualifiedReadings());
        assertEquals(100.0, calculator.getQualifiedRate(), 0.01);
    }

    @Test
    public void testExtractVoltageDataFromCSVDirectory_MultipleFilesWithIndividualRates() throws IOException {
        // 清理测试目录
        cleanTestDirectory();

        // 创建第一个CSV文件 - 100%合格率
        createCSVFile("file1_perfect.csv",
                "time,voltage\n" +
                "1,220.0\n" +
                "2,230.0\n" +
                "3,210.0\n");

        // 创建第二个CSV文件 - 50%合格率
        createCSVFile("file2_mixed.csv",
                "time,voltage\n" +
                "1,250.0\n" +  // 超上限
                "2,220.0\n" +  // 合格
                "3,180.0\n" +  // 低于下限
                "4,230.0\n");  // 合格

        // 创建第三个CSV文件 - 0%合格率
        createCSVFile("file3_poor.csv",
                "time,voltage\n" +
                "1,260.0\n" +  // 超上限
                "2,170.0\n");  // 低于下限

        // 执行测试
        VoltageStatisticsCalculator calculator = CsvUtils.extractVoltageDataFromCSVDirectory(testDirectory.getAbsolutePath());

        // 验证总体结果
        assertNotNull(calculator);
        assertEquals(9, calculator.getTotalReadings()); // 3+4+2=9
        assertEquals(5, calculator.getQualifiedReadings()); // 3+2+0=5
        assertEquals(55.56, calculator.getQualifiedRate(), 0.1); // 5/9*100≈55.56%

        // 注意：此测试主要验证日志输出，实际的单个文件合格率会在日志中显示
        // file1_perfect.csv: 统计列: [voltage], 合格率: 100% (3/3)
        // file2_mixed.csv: 统计列: [voltage], 合格率: 50% (2/4)
        // file3_poor.csv: 统计列: [voltage], 合格率: 0% (0/2)
    }

    @Test
    public void testExtractVoltageDataFromCSVDirectory_ExcludeKVAColumns() throws IOException {
        // 清理测试目录
        cleanTestDirectory();

        // 创建包含kVA列的CSV文件（应该被排除）
        createCSVFile("apparent_power.csv",
                "hour,t(sec),\"S1 (kVA)\",Ang1,\"S2 (kVA)\",Ang2,\"S3 (kVA)\",Ang3\n" +
                "0,900.00000,100.5,-30.057,105.3,-150.054,98.7,89.9157\n" +
                "0,1800.00000,102.1,-30.288,107.8,-150.266,101.2,89.745\n");

        // 创建包含真正电压列的CSV文件
        createCSVFile("voltage_data.csv",
                "time,V1,V2,kV_line\n" +
                "1,220.0,230.0,220.5\n" +
                "2,225.0,235.0,220.8\n");

        // 执行测试
        VoltageStatisticsCalculator calculator = CsvUtils.extractVoltageDataFromCSVDirectory(testDirectory.getAbsolutePath());

        // 验证结果 - 应该只处理voltage_data.csv中的电压列
        assertNotNull(calculator);
        assertEquals(6, calculator.getTotalReadings()); // 2行 × 3个电压列(V1, V2, kV_line)
        assertEquals(6, calculator.getQualifiedReadings());
        assertEquals(100.0, calculator.getQualifiedRate(), 0.01);

        // apparent_power.csv应该显示"未找到有效电压列"
        // voltage_data.csv应该显示"统计列: [V1, V2, kV_line]"
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建CSV文件
     */
    private void createCSVFile(String fileName, String content) throws IOException {
        createFile(fileName, content);
    }

    /**
     * 创建文件
     */
    private void createFile(String fileName, String content) throws IOException {
        File file = new File(testDirectory, fileName);
        try (FileWriter writer = new FileWriter(file)) {
            writer.write(content);
        }
    }

    /**
     * 清理测试目录
     */
    private void cleanTestDirectory() {
        if (testDirectory != null && testDirectory.exists()) {
            File[] files = testDirectory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) {
                        file.delete();
                    }
                }
            }
        }
    }

}
