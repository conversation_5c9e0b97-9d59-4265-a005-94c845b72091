package com.cet.electric.ngapserver.dto;

import com.sun.istack.internal.NotNull;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("用户电压报表查询参数")
public class UserVoltageReportQueryDTO {
    @ApiModelProperty("关键字，用于筛选用户名称")
    private String keyword;

    @ApiModelProperty("当前页码（从1开始）")
    @NotNull
    private Integer pageNum = 1;

    @ApiModelProperty("每页显示条数")
    @NotNull
    private Integer pageSize = 10;
}

