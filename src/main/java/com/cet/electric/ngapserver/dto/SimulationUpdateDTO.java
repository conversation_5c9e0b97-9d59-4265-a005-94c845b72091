package com.cet.electric.ngapserver.dto;

import com.cet.electric.ngapserver.entity.SimulationScript;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "仿真任务更新参数")
public class SimulationUpdateDTO {
    @ApiModelProperty(value = "仿真接线图")
    private String simulationModel;

    @ApiModelProperty(value = "仿真接线图草稿")
    private String simulationDraft;

    @ApiModelProperty(value = "仿真控制策略")
    private String controlStrategy;

    @ApiModelProperty(value = "仿真脚本")
    private SimulationScript simulationScript;
}
