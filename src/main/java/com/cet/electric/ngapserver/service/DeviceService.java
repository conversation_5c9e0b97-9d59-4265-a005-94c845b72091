package com.cet.electric.ngapserver.service;

import com.cet.electric.ngapserver.entity.Device;

import java.util.List;

/**
 * 设备服务接口
 * <AUTHOR>
 */
public interface DeviceService {

    /**
     * 创建设备
     *
     * @param device 设备实体
     * @return 创建后的设备实体
     */
    Device createDevice(Device device);

    /**
     * 分页查询设备
     *
     * @param page      页码
     * @param size      每页大小
     * @param sortBy    排序字段
     * @param sortOrder 排序方向
     * @param keyword   搜索关键词(可选)
     * @return 分页结果
     */
    List<Device> getDevices(Integer page, Integer size, String sortBy, String sortOrder, String keyword);

    /**
     * 统计符合条件的设备总数
     *
     * @param keyword 搜索关键词(可选)
     * @return 总数
     */
    Long countDevices(String keyword);

    /**
     * 根据ID查询设备
     *
     * @param parameterId 参数ID
     * @return 设备实体
     */
    Device getDeviceById(Long parameterId);

    /**
     * 更新设备
     *
     * @param device 设备实体
     * @return 更新后的设备实体
     */
    Device updateDevice(Device device);

    /**
     * 删除设备
     *
     * @param parameterId 参数ID
     * @return 删除是否成功
     */
    boolean deleteDevice(Long parameterId);
}
