package com.cet.electric.ngapserver.config;

import com.cet.electric.ngapserver.util.DatabaseUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DriverManager;

/**
 * 数据库初始化器
 * 在Spring Boot应用启动时自动检查并初始化数据库
 * 
 * <AUTHOR>
 */
@Component
public class DatabaseInitializer implements ApplicationRunner {
    
    private static final Logger log = LoggerFactory.getLogger(DatabaseInitializer.class);
    
    @Value("${spring.datasource.url}")
    private String databaseUrl;
    
    @Value("${spring.datasource.driver-class-name}")
    private String driverClassName;
    
    /**
     * 应用启动后执行数据库初始化
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始数据库初始化检查...");
        
        try {
            // 加载数据库驱动
            Class.forName(driverClassName);
            log.info("数据库驱动加载成功: {}", driverClassName);
            
            // 检查数据库是否存在
            if (!DatabaseUtils.isDatabaseExists(databaseUrl)) {
                log.info("数据库文件不存在，开始创建数据库: {}", databaseUrl);
                initializeDatabase();
            } else {
                log.info("数据库文件已存在: {}", databaseUrl);
                // 检查表结构是否完整
                validateDatabaseSchema();
            }
            
        } catch (Exception e) {
            log.error("数据库初始化失败", e);
            throw e;
        }
        
        log.info("数据库初始化检查完成");
    }
    
    /**
     * 初始化数据库
     * 创建数据库文件和表结构
     */
    private void initializeDatabase() throws Exception {
        log.info("开始初始化数据库...");
        
        // 创建数据库目录
        if (!DatabaseUtils.createDatabaseDirectory(databaseUrl)) {
            throw new RuntimeException("无法创建数据库目录");
        }
        
        // 创建数据库连接（SQLite会自动创建文件）
        try (Connection connection = DriverManager.getConnection(databaseUrl)) {
            log.info("数据库连接创建成功，开始执行建表脚本...");
            
            // 执行初始化SQL脚本
            DatabaseUtils.executeSqlScript(connection, "db/init.sql");
            
            log.info("数据库初始化完成");
            
        } catch (Exception e) {
            log.error("数据库初始化过程中发生错误", e);
            throw e;
        }
    }
    
    /**
     * 验证数据库表结构
     * 检查必要的表是否存在
     */
    private void validateDatabaseSchema() throws Exception {
        log.info("开始验证数据库表结构...");
        
        try (Connection connection = DriverManager.getConnection(databaseUrl)) {
            
            // 检查必要的表是否存在
            String[] requiredTables = {"project", "simulation", "device"};
            boolean allTablesExist = true;
            
            for (String tableName : requiredTables) {
                if (!DatabaseUtils.isTableExists(connection, tableName)) {
                    log.warn("缺少必要的表: {}", tableName);
                    allTablesExist = false;
                }
            }
            
            if (!allTablesExist) {
                log.warn("数据库表结构不完整，重新执行初始化脚本...");
                DatabaseUtils.executeSqlScript(connection, "db/init.sql");
                log.info("数据库表结构修复完成");
            } else {
                log.info("数据库表结构验证通过");
            }
            
        } catch (Exception e) {
            log.error("数据库表结构验证失败", e);
            throw e;
        }
    }
}
