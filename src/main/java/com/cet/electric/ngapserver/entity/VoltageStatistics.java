package com.cet.electric.ngapserver.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 电压统计信息基础类
 * <AUTHOR>
 */
 @Data
 @NoArgsConstructor
 @AllArgsConstructor
 @SuperBuilder
public class VoltageStatistics {
    /**
     * 电压合格率
     */
    private Double qualifiedRate;

    /**
     * 电压超上限比率
     */
    private Double aboveMaxRate;

    /**
     * 电压低于下限比率
     */
    private Double belowMinRate;

    /**
     * 最大电压值
     */
    private Double maxVoltage;

    /**
     * 最小电压值
     */
    private Double minVoltage;

    /**
     * 总读数数量
     */
    private Integer totalReadings;

    /**
     * 合格读数数量
     */
    private Integer qualifiedReadings;

    /**
     * 超上限读数数量
     */
    private Integer aboveMaxReadings;

    /**
     * 低于下限读数数量
     */
    private Integer belowMinReadings;
}