package com.cet.electric.ngapserver.exception;

import javax.servlet.http.HttpServletRequest;

import com.cet.electric.ngapserver.dto.ErrorMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import com.cet.electric.ngapserver.dto.ResponseDTO;

/**
 * <AUTHOR>
 */
@ControllerAdvice
@ResponseBody
@Slf4j
public class GlobalExceptionHandler {
    @ExceptionHandler(value = Exception.class)
    // 该注解声明异常处理方法
    public ResponseDTO<?> exceptionHandler(HttpServletRequest request, Exception exception) {
        log.error(ErrorMsg.getStackTraceStr(exception));
        if (exception instanceof ErrorMsg) {
            ErrorMsg error = (ErrorMsg) exception;
            return new ResponseDTO<Void>(error.getCode(), error.getMessage(), null);
        }
        return new ResponseDTO<Void>(-1, "未知异常", null);
    }
}