package com.cet.electric.ngapserver.schedule;

import com.cet.electric.ngapserver.dao.ProjectDao;
import com.cet.electric.ngapserver.dao.SimulationDao;
import com.cet.electric.ngapserver.entity.Project;
import com.cet.electric.ngapserver.entity.Simulation;
import com.cet.electric.ngapserver.util.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.List;

import static com.cet.electric.ngapserver.util.FileUtils.deleteDirectory;

/**
 * <AUTHOR>
 */
@Component
@EnableAsync
public class TaskSchedule {

    private static final Logger log = LoggerFactory.getLogger(TaskSchedule.class);

    @Autowired
    private SimulationDao simulationDao;

    @Autowired
    private ProjectDao projectDao;

    @Scheduled(fixedRate = 1000 * 60 * 60 * 24)
    public void delete(){
        log.info("开始删除");
        deleteProject();
        deleteSimulation();
        log.info("删除完毕");
    }
    private void deleteProject(){
        log.info("开始删除项目");

        // 获取所有已标记为删除的项目
        List<Project> deletedProjects = projectDao.findAllDeleted();

        if (deletedProjects == null || deletedProjects.isEmpty()) {
            log.info("没有需要清理的已删除项目记录");
            return;
        }

        log.info("找到{}个已标记删除的项目记录", deletedProjects.size());

        deleteProjectDataBase(deletedProjects);

        deleteProjectFile(deletedProjects);
        log.info("项目删除完毕");
    }
    private void deleteProjectDataBase(List<Project> deletedProjects){
        // 记录项目ID，用于后续可能的文件删除操作
        for (Project project : deletedProjects) {
            log.info("准备删除项目: ID={}, 名称={}", project.getProjectId(), project.getProjectName());
        }

        // 执行数据库物理删除
        int count = projectDao.deleteAllMarkedAsDeleted();
        log.info("成功从数据库中物理删除{}个项目记录", count);
    }
    private void deleteProjectFile(List<Project> deletedProjects) {
        if (deletedProjects == null || deletedProjects.isEmpty()) {
            log.info("没有需要删除的项目");
            return;
        }
        int successCount = 0;
        for (Project project : deletedProjects) {
            // 构建项目文件夹路径
            String projectFolderPath = FileUtils.getCurrentPath() + "/data/" + project.getProjectId();
            File projectFolder = new File(projectFolderPath);
            if (projectFolder.exists()) {
                try {
                    deleteDirectory(projectFolder);
                    log.info("成功删除项目文件夹: {}", projectFolder.getAbsolutePath());
                    successCount++;
                } catch (IOException e) {
                    log.info("删除项目文件夹失败: {}", projectFolder.getAbsolutePath());
                }
            } else {
                log.info("项目文件夹不存在，无需删除: {}", projectFolder.getAbsolutePath());
            }
        }
        log.info("成功清理{}个项目的文件", successCount);
    }
    private void deleteSimulation() {
        log.info("开始删除仿真任务");

        // 获取所有已标记为删除的仿真任务
        List<Simulation> deletedSimulations = simulationDao.findAllDeleted();

        if (deletedSimulations == null || deletedSimulations.isEmpty()) {
            log.info("没有需要清理的已删除仿真任务");
            return;
        }

        log.info("找到{}个已标记删除的仿真任务", deletedSimulations.size());

        deleteSimulationDataBase(deletedSimulations);
        deleteSimulationFiles(deletedSimulations);

        log.info("仿真任务删除完毕");
    }
    private void deleteSimulationDataBase(List<Simulation> deletedSimulations) {
        // 记录仿真任务ID，用于后续可能的文件删除操作
        for (Simulation simulation : deletedSimulations) {
            log.info("准备删除仿真任务: ID={}, 名称={}", simulation.getSimulationId(), simulation.getSimulationName());
        }

        // 执行数据库物理删除
        int count = simulationDao.deleteAllMarkedAsDeleted();
        log.info("成功从数据库中物理删除{}个仿真任务", count);
    }
    private void deleteSimulationFiles(List<Simulation> deletedSimulations) {
        if (deletedSimulations == null || deletedSimulations.isEmpty()) {
            log.info("没有需要删除的仿真任务文件");
            return;
        }

        int successCount = 0;
        for (Simulation simulation : deletedSimulations) {
            // 构建仿真任务文件夹路径
            String simulationFolderPath = FileUtils.getCurrentPath() + "/data/" +
                    simulation.getProjectId() + "/" +
                    simulation.getSimulationId();

            File simulationFolder = new File(simulationFolderPath);

            if (simulationFolder.exists()) {
                try {
                    deleteDirectory(simulationFolder);
                    log.info("成功删除仿真任务文件夹: {}", simulationFolder.getAbsolutePath());
                    successCount++;
                } catch (IOException e) {
                    log.error("删除仿真任务文件夹失败: {}", simulationFolder.getAbsolutePath(), e);
                }
            } else {
                log.info("仿真任务文件夹不存在，无需删除: {}", simulationFolder.getAbsolutePath());
            }
        }
        log.info("成功清理{}个模拟的文件", successCount);
    }

}
