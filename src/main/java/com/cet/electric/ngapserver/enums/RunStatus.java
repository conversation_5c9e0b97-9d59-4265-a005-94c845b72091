package com.cet.electric.ngapserver.enums;

public enum RunStatus {
    READY,
    RUNNING,
    SUCCESS,
    FAILED;
    public static RunStatus fromString(String value) {
        for (RunStatus status : RunStatus.values()) {
            if (status.name().equalsIgnoreCase(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No enum constant " + RunStatus.class.getCanonicalName() + "." + value);
    }
}

