package com.cet.electric.ngapserver.util.voltage;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 电压处理性能监控器
 * 监控处理时间、内存使用和缓存效率
 * 
 * <AUTHOR>
 */
public class VoltagePerformanceMonitor {
    
    private static final Logger log = LoggerFactory.getLogger(VoltagePerformanceMonitor.class);
    
    private static volatile VoltagePerformanceMonitor instance;
    
    // 性能统计
    private final AtomicLong totalProcessingTime = new AtomicLong(0);
    private final AtomicLong totalProcessedFiles = new AtomicLong(0);
    private final AtomicLong totalProcessedRecords = new AtomicLong(0);
    private final ConcurrentHashMap<String, OperationStats> operationStats = new ConcurrentHashMap<>();
    
    private VoltagePerformanceMonitor() {
        log.info("电压性能监控器初始化完成");
    }
    
    /**
     * 获取单例实例
     */
    public static VoltagePerformanceMonitor getInstance() {
        if (instance == null) {
            synchronized (VoltagePerformanceMonitor.class) {
                if (instance == null) {
                    instance = new VoltagePerformanceMonitor();
                }
            }
        }
        return instance;
    }
    
    /**
     * 记录操作开始
     */
    public PerformanceTimer startOperation(String operationName) {
        return new PerformanceTimer(operationName);
    }
    
    /**
     * 记录文件处理完成
     */
    public void recordFileProcessed(String filePath, long processingTimeMs, int recordCount) {
        totalProcessingTime.addAndGet(processingTimeMs);
        totalProcessedFiles.incrementAndGet();
        totalProcessedRecords.addAndGet(recordCount);
        
        log.debug("文件处理完成: {} - 耗时: {}ms, 记录数: {}", filePath, processingTimeMs, recordCount);
    }
    
    /**
     * 记录操作统计
     */
    private void recordOperation(String operationName, long durationMs) {
        operationStats.compute(operationName, (key, stats) -> {
            if (stats == null) {
                stats = new OperationStats();
            }
            stats.addExecution(durationMs);
            return stats;
        });
    }
    
    /**
     * 获取性能报告
     */
    public PerformanceReport getPerformanceReport() {
        long avgProcessingTime = totalProcessedFiles.get() > 0 ? 
            totalProcessingTime.get() / totalProcessedFiles.get() : 0;
        
        return new PerformanceReport(
            totalProcessedFiles.get(),
            totalProcessedRecords.get(),
            totalProcessingTime.get(),
            avgProcessingTime,
            new ConcurrentHashMap<>(operationStats)
        );
    }
    
    /**
     * 重置统计数据
     */
    public void reset() {
        totalProcessingTime.set(0);
        totalProcessedFiles.set(0);
        totalProcessedRecords.set(0);
        operationStats.clear();
        log.info("性能统计数据已重置");
    }
    
    /**
     * 性能计时器
     */
    public class PerformanceTimer implements AutoCloseable {
        private final String operationName;
        private final long startTime;
        
        public PerformanceTimer(String operationName) {
            this.operationName = operationName;
            this.startTime = System.currentTimeMillis();
        }
        
        @Override
        public void close() {
            long duration = System.currentTimeMillis() - startTime;
            recordOperation(operationName, duration);
        }
        
        public long getDuration() {
            return System.currentTimeMillis() - startTime;
        }
    }
    
    /**
     * 操作统计信息
     */
    private static class OperationStats {
        private final AtomicLong executionCount = new AtomicLong(0);
        private final AtomicLong totalDuration = new AtomicLong(0);
        private volatile long minDuration = Long.MAX_VALUE;
        private volatile long maxDuration = Long.MIN_VALUE;
        
        public void addExecution(long durationMs) {
            executionCount.incrementAndGet();
            totalDuration.addAndGet(durationMs);
            
            // 更新最值（简单实现，可能有轻微竞争条件）
            if (durationMs < minDuration) {
                minDuration = durationMs;
            }
            if (durationMs > maxDuration) {
                maxDuration = durationMs;
            }
        }
        
        public long getExecutionCount() { return executionCount.get(); }
        public long getTotalDuration() { return totalDuration.get(); }
        public long getAverageDuration() { 
            long count = executionCount.get();
            return count > 0 ? totalDuration.get() / count : 0; 
        }
        public long getMinDuration() { return minDuration == Long.MAX_VALUE ? 0 : minDuration; }
        public long getMaxDuration() { return maxDuration == Long.MIN_VALUE ? 0 : maxDuration; }
        
        @Override
        public String toString() {
            return String.format("OperationStats{count=%d, total=%dms, avg=%dms, min=%dms, max=%dms}",
                getExecutionCount(), getTotalDuration(), getAverageDuration(), 
                getMinDuration(), getMaxDuration());
        }
    }
    
    /**
     * 性能报告
     */
    public static class PerformanceReport {
        public final long totalFiles;
        public final long totalRecords;
        public final long totalProcessingTime;
        public final long averageProcessingTime;
        public final ConcurrentHashMap<String, OperationStats> operationStats;
        
        public PerformanceReport(long totalFiles, long totalRecords, long totalProcessingTime,
                               long averageProcessingTime, ConcurrentHashMap<String, OperationStats> operationStats) {
            this.totalFiles = totalFiles;
            this.totalRecords = totalRecords;
            this.totalProcessingTime = totalProcessingTime;
            this.averageProcessingTime = averageProcessingTime;
            this.operationStats = operationStats;
        }
        
        /**
         * 获取处理速度（记录/秒）
         */
        public double getProcessingSpeed() {
            return totalProcessingTime > 0 ? (double) totalRecords / (totalProcessingTime / 1000.0) : 0;
        }
        
        /**
         * 获取文件处理速度（文件/秒）
         */
        public double getFileProcessingSpeed() {
            return totalProcessingTime > 0 ? (double) totalFiles / (totalProcessingTime / 1000.0) : 0;
        }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("VoltagePerformanceReport{\n");
            sb.append("  总文件数: ").append(totalFiles).append("\n");
            sb.append("  总记录数: ").append(totalRecords).append("\n");
            sb.append("  总处理时间: ").append(totalProcessingTime).append("ms\n");
            sb.append("  平均处理时间: ").append(averageProcessingTime).append("ms\n");
            sb.append("  处理速度: ").append(String.format("%.2f", getProcessingSpeed())).append(" 记录/秒\n");
            sb.append("  文件处理速度: ").append(String.format("%.2f", getFileProcessingSpeed())).append(" 文件/秒\n");
            
            if (!operationStats.isEmpty()) {
                sb.append("  操作统计:\n");
                operationStats.forEach((operation, stats) -> 
                    sb.append("    ").append(operation).append(": ").append(stats).append("\n"));
            }
            
            sb.append("}");
            return sb.toString();
        }
    }
    
    /**
     * 便捷方法：记录方法执行时间
     */
    public static <T> T timeOperation(String operationName, java.util.function.Supplier<T> operation) {
        VoltagePerformanceMonitor monitor = getInstance();
        try (PerformanceTimer timer = monitor.startOperation(operationName)) {
            return operation.get();
        }
    }
    
    /**
     * 便捷方法：记录无返回值方法执行时间
     */
    public static void timeOperation(String operationName, Runnable operation) {
        VoltagePerformanceMonitor monitor = getInstance();
        try (PerformanceTimer timer = monitor.startOperation(operationName)) {
            operation.run();
        }
    }
}
