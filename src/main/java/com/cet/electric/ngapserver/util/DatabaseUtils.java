package com.cet.electric.ngapserver.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * 数据库工具类
 * 提供数据库初始化和管理相关的工具方法
 * 
 * <AUTHOR>
 */
public class DatabaseUtils {
    
    private static final Logger log = LoggerFactory.getLogger(DatabaseUtils.class);
    
    /**
     * 检查数据库文件是否存在
     * 
     * @param databasePath 数据库文件路径
     * @return 如果数据库文件存在返回true，否则返回false
     */
    public static boolean isDatabaseExists(String databasePath) {
        if (databasePath == null || databasePath.trim().isEmpty()) {
            return false;
        }
        
        // 从JDBC URL中提取文件路径
        String filePath = extractFilePathFromJdbcUrl(databasePath);
        if (filePath == null) {
            return false;
        }
        
        File dbFile = new File(filePath);
        boolean exists = dbFile.exists() && dbFile.isFile();
        log.debug("检查数据库文件: {} - 存在: {}", filePath, exists);
        return exists;
    }
    
    /**
     * 从JDBC URL中提取文件路径
     * 
     * @param jdbcUrl JDBC连接URL
     * @return 文件路径，如果无法提取则返回null
     */
    private static String extractFilePathFromJdbcUrl(String jdbcUrl) {
        if (jdbcUrl == null || !jdbcUrl.startsWith("jdbc:sqlite:")) {
            return null;
        }
        
        return jdbcUrl.substring("jdbc:sqlite:".length());
    }
    
    /**
     * 创建数据库文件的父目录
     * 
     * @param databasePath 数据库文件路径
     * @return 如果目录创建成功或已存在返回true，否则返回false
     */
    public static boolean createDatabaseDirectory(String databasePath) {
        String filePath = extractFilePathFromJdbcUrl(databasePath);
        if (filePath == null) {
            return false;
        }
        
        File dbFile = new File(filePath);
        File parentDir = dbFile.getParentFile();
        
        if (parentDir != null && !parentDir.exists()) {
            boolean created = parentDir.mkdirs();
            log.info("创建数据库目录: {} - 成功: {}", parentDir.getAbsolutePath(), created);
            return created;
        }
        
        return true;
    }
    
    /**
     * 执行SQL脚本文件
     * 
     * @param connection 数据库连接
     * @param scriptPath 脚本文件路径（相对于classpath）
     * @throws SQLException SQL执行异常
     * @throws IOException 文件读取异常
     */
    public static void executeSqlScript(Connection connection, String scriptPath) throws SQLException, IOException {
        log.info("开始执行SQL脚本: {}", scriptPath);
        
        ClassPathResource resource = new ClassPathResource(scriptPath);
        if (!resource.exists()) {
            throw new IOException("SQL脚本文件不存在: " + scriptPath);
        }
        
        StringBuilder sqlContent = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8))) {
            
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                // 跳过注释和空行
                if (line.isEmpty() || line.startsWith("--")) {
                    continue;
                }
                sqlContent.append(line).append("\n");
            }
        }
        
        // 按分号分割SQL语句
        String[] sqlStatements = sqlContent.toString().split(";");
        
        try (Statement statement = connection.createStatement()) {
            for (String sql : sqlStatements) {
                sql = sql.trim();
                if (!sql.isEmpty()) {
                    log.debug("执行SQL: {}", sql);
                    statement.execute(sql);
                }
            }
        }
        
        log.info("SQL脚本执行完成: {}", scriptPath);
    }
    
    /**
     * 测试数据库连接
     * 
     * @param jdbcUrl JDBC连接URL
     * @return 如果连接成功返回true，否则返回false
     */
    public static boolean testConnection(String jdbcUrl) {
        try (Connection connection = DriverManager.getConnection(jdbcUrl)) {
            log.debug("数据库连接测试成功: {}", jdbcUrl);
            return true;
        } catch (SQLException e) {
            log.warn("数据库连接测试失败: {} - {}", jdbcUrl, e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查表是否存在
     * 
     * @param connection 数据库连接
     * @param tableName 表名
     * @return 如果表存在返回true，否则返回false
     */
    public static boolean isTableExists(Connection connection, String tableName) {
        try (Statement statement = connection.createStatement()) {
            String sql = "SELECT name FROM sqlite_master WHERE type='table' AND name='" + tableName + "'";
            return statement.executeQuery(sql).next();
        } catch (SQLException e) {
            log.warn("检查表是否存在时发生异常: {} - {}", tableName, e.getMessage());
            return false;
        }
    }
}
