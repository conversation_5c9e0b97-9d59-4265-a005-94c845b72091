package com.cet.electric.ngapserver.util.voltage;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 电压处理专用对象池
 * 管理电压数据处理过程中的可重用对象，减少GC压力
 * 
 * <AUTHOR>
 */
public class VoltageObjectPool {
    
    private static final Logger log = LoggerFactory.getLogger(VoltageObjectPool.class);
    
    // 池大小配置
    private static final int DEFAULT_POOL_SIZE = 20;
    private static final int MAX_POOL_SIZE = 50;
    
    // 对象池
    private final BlockingQueue<List<Double>> doubleListPool;
    private final BlockingQueue<VoltageStatisticsCalculator> calculatorPool;
    private final BlockingQueue<StringBuilder> stringBuilderPool;
    
    // 统计信息
    private final AtomicInteger doubleListCreated = new AtomicInteger(0);
    private final AtomicInteger calculatorCreated = new AtomicInteger(0);
    private final AtomicInteger stringBuilderCreated = new AtomicInteger(0);
    
    private static volatile VoltageObjectPool instance;
    
    private VoltageObjectPool() {
        this.doubleListPool = new LinkedBlockingQueue<>();
        this.calculatorPool = new LinkedBlockingQueue<>();
        this.stringBuilderPool = new LinkedBlockingQueue<>();
        
        // 预创建一些对象
        initializePools();
        
        log.info("电压对象池初始化完成");
    }
    
    /**
     * 获取单例实例
     */
    public static VoltageObjectPool getInstance() {
        if (instance == null) {
            synchronized (VoltageObjectPool.class) {
                if (instance == null) {
                    instance = new VoltageObjectPool();
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化对象池
     */
    private void initializePools() {
        // 预创建Double列表
        for (int i = 0; i < DEFAULT_POOL_SIZE / 2; i++) {
            doubleListPool.offer(new ArrayList<>(100));
            doubleListCreated.incrementAndGet();
        }
        
        // 预创建统计计算器
        for (int i = 0; i < DEFAULT_POOL_SIZE / 4; i++) {
            calculatorPool.offer(new VoltageStatisticsCalculator());
            calculatorCreated.incrementAndGet();
        }
        
        // 预创建StringBuilder
        for (int i = 0; i < DEFAULT_POOL_SIZE / 4; i++) {
            stringBuilderPool.offer(new StringBuilder(256));
            stringBuilderCreated.incrementAndGet();
        }
    }
    
    /**
     * 借用Double列表
     */
    public List<Double> borrowDoubleList() {
        List<Double> list = doubleListPool.poll();
        
        if (list == null) {
            if (doubleListCreated.get() < MAX_POOL_SIZE) {
                list = new ArrayList<>(100);
                doubleListCreated.incrementAndGet();
                log.debug("创建新的Double列表，当前总数: {}", doubleListCreated.get());
            } else {
                // 达到最大限制，等待可用对象
                try {
                    list = doubleListPool.take();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return new ArrayList<>(100);
                }
            }
        }
        
        return list;
    }
    
    /**
     * 归还Double列表
     */
    public void returnDoubleList(List<Double> list) {
        if (list != null) {
            list.clear();
            boolean offered = doubleListPool.offer(list);
            if (!offered) {
                log.debug("Double列表池已满，丢弃对象");
            }
        }
    }
    
    /**
     * 借用统计计算器
     */
    public VoltageStatisticsCalculator borrowCalculator() {
        VoltageStatisticsCalculator calculator = calculatorPool.poll();
        
        if (calculator == null) {
            if (calculatorCreated.get() < MAX_POOL_SIZE) {
                calculator = new VoltageStatisticsCalculator();
                calculatorCreated.incrementAndGet();
                log.debug("创建新的统计计算器，当前总数: {}", calculatorCreated.get());
            } else {
                try {
                    calculator = calculatorPool.take();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return new VoltageStatisticsCalculator();
                }
            }
        }
        
        return calculator;
    }
    
    /**
     * 归还统计计算器
     */
    public void returnCalculator(VoltageStatisticsCalculator calculator) {
        if (calculator != null) {
            calculator.reset();
            boolean offered = calculatorPool.offer(calculator);
            if (!offered) {
                log.debug("统计计算器池已满，丢弃对象");
            }
        }
    }
    
    /**
     * 借用StringBuilder
     */
    public StringBuilder borrowStringBuilder() {
        StringBuilder sb = stringBuilderPool.poll();
        
        if (sb == null) {
            if (stringBuilderCreated.get() < MAX_POOL_SIZE) {
                sb = new StringBuilder(256);
                stringBuilderCreated.incrementAndGet();
                log.debug("创建新的StringBuilder，当前总数: {}", stringBuilderCreated.get());
            } else {
                try {
                    sb = stringBuilderPool.take();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return new StringBuilder(256);
                }
            }
        }
        
        return sb;
    }
    
    /**
     * 归还StringBuilder
     */
    public void returnStringBuilder(StringBuilder sb) {
        if (sb != null) {
            sb.setLength(0);
            boolean offered = stringBuilderPool.offer(sb);
            if (!offered) {
                log.debug("StringBuilder池已满，丢弃对象");
            }
        }
    }
    
    /**
     * 获取池状态信息
     */
    public PoolStats getStats() {
        return new PoolStats(
            doubleListPool.size(),
            calculatorPool.size(),
            stringBuilderPool.size(),
            doubleListCreated.get(),
            calculatorCreated.get(),
            stringBuilderCreated.get()
        );
    }
    
    /**
     * 清空所有对象池
     */
    public void clear() {
        doubleListPool.clear();
        calculatorPool.clear();
        stringBuilderPool.clear();
        log.info("对象池已清空");
    }
    
    /**
     * 池状态统计
     */
    public static class PoolStats {
        public final int doubleListPoolSize;
        public final int calculatorPoolSize;
        public final int stringBuilderPoolSize;
        public final int doubleListCreated;
        public final int calculatorCreated;
        public final int stringBuilderCreated;
        
        public PoolStats(int doubleListPoolSize, int calculatorPoolSize, int stringBuilderPoolSize,
                        int doubleListCreated, int calculatorCreated, int stringBuilderCreated) {
            this.doubleListPoolSize = doubleListPoolSize;
            this.calculatorPoolSize = calculatorPoolSize;
            this.stringBuilderPoolSize = stringBuilderPoolSize;
            this.doubleListCreated = doubleListCreated;
            this.calculatorCreated = calculatorCreated;
            this.stringBuilderCreated = stringBuilderCreated;
        }
        
        @Override
        public String toString() {
            return String.format(
                "PoolStats{doubleList: %d/%d, calculator: %d/%d, stringBuilder: %d/%d}",
                doubleListPoolSize, doubleListCreated,
                calculatorPoolSize, calculatorCreated,
                stringBuilderPoolSize, stringBuilderCreated
            );
        }
    }
}
