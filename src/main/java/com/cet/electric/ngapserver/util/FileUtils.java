package com.cet.electric.ngapserver.util;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 */
public class FileUtils {

    /**
     * 下载文件
     *
     * @param filePath 文件路径
     * @throws IOException 如果复制过程中发生IO错误
     */
    public static void downloadFile(String filePath, HttpServletResponse response) {
        if (filePath == null || filePath.isEmpty()) {
            throw new IllegalArgumentException("File path cannot be empty");
        } else {
            // 获取文件对象
            File file = new File(filePath);

            // 检查文件是否存在并且可读
            if (!file.exists() || !file.canRead()) {
                throw new IllegalArgumentException("File not found or cannot be read: " + filePath);
            }

            try (InputStream inputStream = Files.newInputStream(file.toPath());
                 ServletOutputStream outputStream = response.getOutputStream()) {

                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment; filename=\"" + URLEncoder.encode(file.getName(), "UTF-8") + "\"");

                byte[] buffer = new byte[1024];
                int length;
                while ((length = inputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, length);
                }

                outputStream.flush();
            } catch (IOException e) {
                e.printStackTrace();
                throw new IllegalArgumentException("Error occurred while downloading file: " + e.getMessage());
            }
        }
    }

    /**
     * 获取当前工作目录路径
     * 提供安全的路径获取，防止在特殊环境下返回null
     *
     * @return 当前工作目录路径，如果无法获取则返回系统临时目录
     */
    public static String getCurrentPath() {
        String currentPath = System.getProperty("user.dir");
        if (currentPath == null || currentPath.trim().isEmpty()) {
            // 最后的fallback，使用当前目录
            return ".";
        }
        return currentPath;
    }

    /**
     * 复制目录及其下所有文件和子目录
     *
     * @param sourceDir 源目录
     * @param targetDir 目标目录
     * @throws IOException 如果复制过程中发生IO错误
     */
    public static void copyDirectory(File sourceDir, File targetDir) throws IOException {
        if (!sourceDir.exists()) {
            throw new IOException("源目录不存在: " + sourceDir.getAbsolutePath());
        }

        if (!sourceDir.isDirectory()) {
            throw new IOException("源路径不是目录: " + sourceDir.getAbsolutePath());
        }

        // 确保目标目录存在
        if (!targetDir.exists()) {
            if (!targetDir.mkdirs()) {
                throw new IOException("无法创建目标目录: " + targetDir.getAbsolutePath());
            }
        }

        if (!targetDir.isDirectory()) {
            throw new IOException("目标路径不是目录: " + targetDir.getAbsolutePath());
        }

        // 获取源目录下所有文件和子目录
        File[] files = sourceDir.listFiles();
        if (files == null) {
            // listFiles()可能返回null，例如在I/O错误或抽象路径名不表示目录时
            throw new IOException("无法列出源目录中的文件: " + sourceDir.getAbsolutePath());
        }

        // 复制每个文件和子目录
        for (File file : files) {
            File targetFile = new File(targetDir, file.getName());

            if (file.isDirectory()) {
                // 递归复制子目录
                copyDirectory(file, targetFile);
            } else {
                // 复制文件
                copyFile(file, targetFile);
            }
        }
    }

    /**
     * 复制单个文件
     *
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     * @throws IOException 如果复制过程中发生IO错误
     */
    private static void copyFile(File sourceFile, File targetFile) throws IOException {
        try (
                FileInputStream inputStream = new FileInputStream(sourceFile);
                FileOutputStream outputStream = new FileOutputStream(targetFile)
        ) {
            // 使用缓冲区进行复制，提高效率
            byte[] buffer = new byte[8192]; // 8KB缓冲区
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }

        // 保持文件的最后修改时间
        targetFile.setLastModified(sourceFile.lastModified());
    }

    /**
     * 将文件夹添加到ZIP输出流
     *
     * @param folder 要添加的文件夹
     * @param parentFolder 在ZIP中的父文件夹路径
     * @param zipOut ZIP输出流
     * @throws IOException 如果发生I/O错误
     */
    public static void zipFolder(File folder, String parentFolder, ZipOutputStream zipOut) throws IOException {
        File[] files = folder.listFiles();
        if (files == null) {
            return;
        }

        byte[] buffer = new byte[1024];

        for (File file : files) {
            if (file.isDirectory()) {
                // 递归处理子文件夹
                zipFolder(file, parentFolder + "/" + file.getName(), zipOut);
                continue;
            }

            try (FileInputStream fis = new FileInputStream(file)) {
                // 创建ZIP条目，使用相对路径
                String entryPath = parentFolder + "/" + file.getName();
                ZipEntry zipEntry = new ZipEntry(entryPath);
                zipOut.putNextEntry(zipEntry);

                // 写入文件内容
                int length;
                while ((length = fis.read(buffer)) > 0) {
                    zipOut.write(buffer, 0, length);
                }
            }
        }
    }

    /**
     * 解压ZIP文件到指定目录
     *
     * @param zipFile ZIP文件
     * @param targetDir 目标目录
     * @throws IOException 如果解压过程中发生IO错误
     */
    public static void unzipFile(File zipFile, File targetDir) throws IOException {
        try (ZipInputStream zipIn = new ZipInputStream(new FileInputStream(zipFile))) {
            ZipEntry entry;
            while ((entry = zipIn.getNextEntry()) != null) {
                String filePath = targetDir.getPath() + File.separator + entry.getName();
                if (!entry.isDirectory()) {
                    // 创建父目录
                    new File(filePath).getParentFile().mkdirs();

                    // 解压文件
                    try (BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(filePath))) {
                        byte[] buffer = new byte[4096];
                        int bytesRead;
                        while ((bytesRead = zipIn.read(buffer)) != -1) {
                            bos.write(buffer, 0, bytesRead);
                        }
                    }
                } else {
                    // 如果是目录，则创建目录
                    File dir = new File(filePath);
                    dir.mkdirs();
                }
                zipIn.closeEntry();
            }
        }
    }

    /**
     * 删除指定目录及其内容
     *
     * @param directory 要删除的目录
     * @throws IOException 如果删除过程中发生IO错误
     */
    public static void deleteDirectory(File directory) throws IOException {
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteDirectory(file); // 递归删除子文件和子目录
                }
            }
        }
        if (!directory.delete()) {
            throw new IOException("无法删除文件或目录: " + directory.getAbsolutePath());
        }
    }

    /**
     * 检查文件是否存在
     *
     * @param filePath 文件路径
     * @return 存在返回true，否则返回false
     */
    public static boolean checkFileExists(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return false;
        }
        File file = new File(filePath);
        return file.exists() && file.isFile();
    }


    /**
     * 删除指定目录下特定后缀的所有文件
     *
     * @param directoryPath 目录路径
     * @param extension 文件后缀（例如：".csv"）
     * @return 删除的文件数量
     */
    public static int deleteFilesByExtension(String directoryPath, String extension) {
        if (directoryPath == null || extension == null) {
            return 0;
        }

        int deletedCount = 0;

        File directory = new File(directoryPath);
        if (!directory.exists() || !directory.isDirectory()) {
            return 0;
        }

        File[] filesToDelete = directory.listFiles((dir, name) -> name.toLowerCase().endsWith(extension.toLowerCase()));
        if (filesToDelete != null) {
            for (File file : filesToDelete) {
                if (file.delete()) {
                    deletedCount++;
                }
            }
        }

        return deletedCount;
    }

}
