package com.cet.electric.ngapserver.util.voltage;

import com.cet.electric.ngapserver.entity.UserStatistics;

import java.util.List;

/**
 * 电压统计计算器
 * 高性能的电压数据统计计算，支持增量计算和对象重用
 * 
 * <AUTHOR>
 */
public class VoltageStatisticsCalculator {
    
    // 电压合格范围
    private static final double MIN_VOLTAGE = 198.0;
    private static final double MAX_VOLTAGE = 242.0;
    
    // 统计数据
    private int totalReadings;
    private int qualifiedReadings;
    private int aboveMaxReadings;
    private int belowMinReadings;
    private double maxVoltage;
    private double minVoltage;
    
    // 是否已初始化
    private boolean initialized;
    
    public VoltageStatisticsCalculator() {
        reset();
    }
    
    /**
     * 重置计算器状态
     */
    public void reset() {
        totalReadings = 0;
        qualifiedReadings = 0;
        aboveMaxReadings = 0;
        belowMinReadings = 0;
        maxVoltage = Double.MIN_VALUE;
        minVoltage = Double.MAX_VALUE;
        initialized = false;
    }
    
    /**
     * 添加电压值列表进行统计
     */
    public void addVoltageValues(List<Double> voltageValues) {
        if (voltageValues == null || voltageValues.isEmpty()) {
            return;
        }
        
        for (Double voltage : voltageValues) {
            if (voltage != null) {
                addVoltageValue(voltage);
            }
        }
    }
    
    /**
     * 添加单个电压值进行统计
     */
    public void addVoltageValue(double voltage) {
        totalReadings++;
        
        // 分类统计
        if (voltage >= MIN_VOLTAGE && voltage <= MAX_VOLTAGE) {
            qualifiedReadings++;
        } else if (voltage > MAX_VOLTAGE) {
            aboveMaxReadings++;
        } else if (voltage < MIN_VOLTAGE) {
            belowMinReadings++;
        }
        
        // 更新最值
        if (!initialized) {
            maxVoltage = voltage;
            minVoltage = voltage;
            initialized = true;
        } else {
            if (voltage > maxVoltage) {
                maxVoltage = voltage;
            }
            if (voltage < minVoltage) {
                minVoltage = voltage;
            }
        }
    }
    
    /**
     * 批量添加电压值（数组版本，性能更好）
     */
    public void addVoltageValues(double[] voltageValues) {
        if (voltageValues == null || voltageValues.length == 0) {
            return;
        }
        
        for (double voltage : voltageValues) {
            addVoltageValue(voltage);
        }
    }
    
    /**
     * 计算合格率
     */
    public double getQualifiedRate() {
        return totalReadings > 0 ? (double) qualifiedReadings / totalReadings * 100 : 0;
    }
    
    /**
     * 计算超上限率
     */
    public double getAboveMaxRate() {
        return totalReadings > 0 ? (double) aboveMaxReadings / totalReadings * 100 : 0;
    }
    
    /**
     * 计算低于下限率
     */
    public double getBelowMinRate() {
        return totalReadings > 0 ? (double) belowMinReadings / totalReadings * 100 : 0;
    }
    
    /**
     * 构建用户统计对象
     */
    public UserStatistics buildUserStatistics(String userName) {
        return UserStatistics.builder()
                .userName(userName)
                .qualifiedRate(getQualifiedRate())
                .aboveMaxRate(getAboveMaxRate())
                .belowMinRate(getBelowMinRate())
                .maxVoltage(initialized ? maxVoltage : 0.0)
                .minVoltage(initialized ? minVoltage : 0.0)
                .totalReadings(totalReadings)
                .qualifiedReadings(qualifiedReadings)
                .aboveMaxReadings(aboveMaxReadings)
                .belowMinReadings(belowMinReadings)
                .build();
    }
    
    /**
     * 合并另一个计算器的统计结果
     */
    public void merge(VoltageStatisticsCalculator other) {
        if (other == null || other.totalReadings == 0) {
            return;
        }
        
        this.totalReadings += other.totalReadings;
        this.qualifiedReadings += other.qualifiedReadings;
        this.aboveMaxReadings += other.aboveMaxReadings;
        this.belowMinReadings += other.belowMinReadings;
        
        if (!this.initialized) {
            this.maxVoltage = other.maxVoltage;
            this.minVoltage = other.minVoltage;
            this.initialized = other.initialized;
        } else if (other.initialized) {
            if (other.maxVoltage > this.maxVoltage) {
                this.maxVoltage = other.maxVoltage;
            }
            if (other.minVoltage < this.minVoltage) {
                this.minVoltage = other.minVoltage;
            }
        }
    }
    
    /**
     * 获取当前统计数据的快照
     */
    public StatisticsSnapshot getSnapshot() {
        return new StatisticsSnapshot(
            totalReadings,
            qualifiedReadings,
            aboveMaxReadings,
            belowMinReadings,
            initialized ? maxVoltage : 0.0,
            initialized ? minVoltage : 0.0
        );
    }
    
    /**
     * 统计数据快照
     */
    public static class StatisticsSnapshot {
        public final int totalReadings;
        public final int qualifiedReadings;
        public final int aboveMaxReadings;
        public final int belowMinReadings;
        public final double maxVoltage;
        public final double minVoltage;
        
        public StatisticsSnapshot(int totalReadings, int qualifiedReadings, int aboveMaxReadings,
                                int belowMinReadings, double maxVoltage, double minVoltage) {
            this.totalReadings = totalReadings;
            this.qualifiedReadings = qualifiedReadings;
            this.aboveMaxReadings = aboveMaxReadings;
            this.belowMinReadings = belowMinReadings;
            this.maxVoltage = maxVoltage;
            this.minVoltage = minVoltage;
        }
        
        public double getQualifiedRate() {
            return totalReadings > 0 ? (double) qualifiedReadings / totalReadings * 100 : 0;
        }
        
        public double getAboveMaxRate() {
            return totalReadings > 0 ? (double) aboveMaxReadings / totalReadings * 100 : 0;
        }
        
        public double getBelowMinRate() {
            return totalReadings > 0 ? (double) belowMinReadings / totalReadings * 100 : 0;
        }
        
        @Override
        public String toString() {
            return String.format(
                "StatisticsSnapshot{total=%d, qualified=%d(%.2f%%), aboveMax=%d(%.2f%%), belowMin=%d(%.2f%%), max=%.2f, min=%.2f}",
                totalReadings, qualifiedReadings, getQualifiedRate(),
                aboveMaxReadings, getAboveMaxRate(),
                belowMinReadings, getBelowMinRate(),
                maxVoltage, minVoltage
            );
        }
    }
    
    // Getter方法
    public int getTotalReadings() { return totalReadings; }
    public int getQualifiedReadings() { return qualifiedReadings; }
    public int getAboveMaxReadings() { return aboveMaxReadings; }
    public int getBelowMinReadings() { return belowMinReadings; }
    public double getMaxVoltage() { return initialized ? maxVoltage : 0.0; }
    public double getMinVoltage() { return initialized ? minVoltage : 0.0; }
    public boolean isInitialized() { return initialized; }
}
