package com.cet.electric.ngapserver.util.excel;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Excel缓存管理器
 * 提供文件元数据、列索引、处理结果等的缓存功能
 * 
 * <AUTHOR>
 */
public class ExcelCacheManager implements AutoCloseable {
    
    private static final Logger log = LoggerFactory.getLogger(ExcelCacheManager.class);
    
    // 缓存过期时间（毫秒）
    private static final long DEFAULT_EXPIRE_TIME = 30 * 60 * 1000; // 30分钟
    private static final long CLEANUP_INTERVAL = 5 * 60 * 1000; // 5分钟清理一次
    
    private final ConcurrentHashMap<String, CacheEntry<?>> cache;
    private final ScheduledExecutorService cleanupExecutor;
    private final AtomicLong hitCount;
    private final AtomicLong missCount;
    private final long expireTimeMs;
    
    private static volatile ExcelCacheManager instance;
    
    private ExcelCacheManager(long expireTimeMs) {
        this.cache = new ConcurrentHashMap<>();
        this.cleanupExecutor = Executors.newSingleThreadScheduledExecutor(
            r -> {
                Thread t = new Thread(r, "ExcelCache-Cleanup");
                t.setDaemon(true);
                return t;
            });
        this.hitCount = new AtomicLong(0);
        this.missCount = new AtomicLong(0);
        this.expireTimeMs = expireTimeMs;
        
        // 启动定期清理任务
        cleanupExecutor.scheduleAtFixedRate(this::cleanup, 
            CLEANUP_INTERVAL, CLEANUP_INTERVAL, TimeUnit.MILLISECONDS);
        
        log.info("Excel缓存管理器初始化完成，过期时间: {}ms", expireTimeMs);
    }
    
    /**
     * 获取单例实例
     */
    public static ExcelCacheManager getInstance() {
        if (instance == null) {
            synchronized (ExcelCacheManager.class) {
                if (instance == null) {
                    instance = new ExcelCacheManager(DEFAULT_EXPIRE_TIME);
                }
            }
        }
        return instance;
    }
    
    /**
     * 获取缓存值
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> type) {
        CacheEntry<?> entry = cache.get(key);
        
        if (entry == null || entry.isExpired()) {
            missCount.incrementAndGet();
            if (entry != null) {
                cache.remove(key);
            }
            return null;
        }
        
        hitCount.incrementAndGet();
        return (T) entry.getValue();
    }
    
    /**
     * 设置缓存值
     */
    public <T> void put(String key, T value) {
        put(key, value, expireTimeMs);
    }
    
    /**
     * 设置缓存值（指定过期时间）
     */
    public <T> void put(String key, T value, long expireTimeMs) {
        if (key == null || value == null) {
            return;
        }
        
        CacheEntry<T> entry = new CacheEntry<>(value, System.currentTimeMillis() + expireTimeMs);
        cache.put(key, entry);
    }
    
    /**
     * 移除缓存
     */
    public void remove(String key) {
        cache.remove(key);
    }
    
    /**
     * 清空所有缓存
     */
    public void clear() {
        cache.clear();
        log.info("缓存已清空");
    }
    
    /**
     * 获取文件元数据缓存键（优化版本）
     */
    public static String getFileMetaKey(String filePath) {
        File file = new File(filePath);
        // 使用绝对路径确保一致性
        String absolutePath = file.getAbsolutePath();
        long lastModified = file.lastModified();
        long length = file.length();

        // 生成更稳定的缓存键
        return "file_meta:" + absolutePath.hashCode() + ":" + lastModified + ":" + length;
    }
    
    /**
     * 获取列索引缓存键
     */
    public static String getColumnIndexKey(String filePath, String sheetName) {
        return "column_index:" + getFileMetaKey(filePath) + ":" + sheetName;
    }
    
    /**
     * 获取指标缓存键
     */
    public static String getMetricsKey(String filePath) {
        return "metrics:" + getFileMetaKey(filePath);
    }
    
    /**
     * 获取指标数据缓存键
     */
    public static String getMetricDataKey(String filePath, String metricId) {
        return "metric_data:" + getFileMetaKey(filePath) + ":" + metricId;
    }
    
    /**
     * 获取电压报表缓存键
     */
    public static String getVoltageReportKey(String filePath) {
        return "voltage_report:" + getFileMetaKey(filePath);
    }
    
    /**
     * 获取电压数据缓存键
     */
    public static String getVoltageDataKey(String filePath) {
        return "voltage_data:" + getFileMetaKey(filePath);
    }
    
    /**
     * 获取用户统计缓存键
     */
    public static String getUserStatsKey(String filePath) {
        return "user_stats:" + getFileMetaKey(filePath);
    }
    
    /**
     * 获取整体统计缓存键
     */
    public static String getOverallStatsKey(String filePath) {
        return "overall_stats:" + getFileMetaKey(filePath);
    }
    
    /**
     * 清理过期缓存
     */
    private void cleanup() {
        long currentTime = System.currentTimeMillis();
        int removedCount = 0;
        
        for (String key : cache.keySet()) {
            CacheEntry<?> entry = cache.get(key);
            if (entry != null && entry.isExpired(currentTime)) {
                cache.remove(key);
                removedCount++;
            }
        }
        
        if (removedCount > 0) {
            log.debug("清理过期缓存: {}个", removedCount);
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    public CacheStats getStats() {
        long hits = hitCount.get();
        long misses = missCount.get();
        long total = hits + misses;
        double hitRate = total > 0 ? (double) hits / total : 0.0;
        
        return new CacheStats(
            cache.size(),
            hits,
            misses,
            hitRate
        );
    }
    
    @Override
    public void close() {
        if (cleanupExecutor != null && !cleanupExecutor.isShutdown()) {
            cleanupExecutor.shutdown();
            try {
                if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    cleanupExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                cleanupExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        cache.clear();
        log.info("Excel缓存管理器已关闭");
    }
    
    /**
     * 缓存条目
     */
    private static class CacheEntry<T> {
        private final T value;
        private final long expireTime;
        
        public CacheEntry(T value, long expireTime) {
            this.value = value;
            this.expireTime = expireTime;
        }
        
        public T getValue() {
            return value;
        }
        
        public boolean isExpired() {
            return isExpired(System.currentTimeMillis());
        }
        
        public boolean isExpired(long currentTime) {
            return currentTime > expireTime;
        }
    }
    
    /**
     * 缓存统计信息
     */
    public static class CacheStats {
        public final int size;
        public final long hits;
        public final long misses;
        public final double hitRate;

        public CacheStats(int size, long hits, long misses, double hitRate) {
            this.size = size;
            this.hits = hits;
            this.misses = misses;
            this.hitRate = hitRate;
        }

        @Override
        public String toString() {
            return String.format("CacheStats{size=%d, hits=%d, misses=%d, hitRate=%.2f%%}",
                               size, hits, misses, hitRate * 100);
        }
    }

    /**
     * 常用缓存操作
     */
    public static class CacheOperations {

        private static final ExcelCacheManager cache = ExcelCacheManager.getInstance();

        /**
         * 缓存文件列索引
         */
        public static void cacheColumnIndex(String filePath, String sheetName,
                                          java.util.Map<String, Integer> columnIndex) {
            String key = getColumnIndexKey(filePath, sheetName);
            cache.put(key, columnIndex);
        }

        /**
         * 获取文件列索引
         */
        @SuppressWarnings("unchecked")
        public static java.util.Map<String, Integer> getColumnIndex(String filePath, String sheetName) {
            String key = getColumnIndexKey(filePath, sheetName);
            return cache.get(key, java.util.Map.class);
        }

        /**
         * 缓存指标列表
         */
        public static void cacheMetrics(String filePath, java.util.List<com.cet.electric.ngapserver.entity.Metric> metrics) {
            String key = getMetricsKey(filePath);
            cache.put(key, metrics);
        }

        /**
         * 获取指标列表
         */
        @SuppressWarnings("unchecked")
        public static java.util.List<com.cet.electric.ngapserver.entity.Metric> getMetrics(String filePath) {
            String key = getMetricsKey(filePath);
            return cache.get(key, java.util.List.class);
        }

        /**
         * 缓存指标数据
         */
        public static void cacheMetricData(String filePath, String metricId,
                                         com.cet.electric.ngapserver.entity.MetricData metricData) {
            String key = getMetricDataKey(filePath, metricId);
            cache.put(key, metricData);
        }

        /**
         * 获取指标数据
         */
        public static com.cet.electric.ngapserver.entity.MetricData getMetricData(String filePath, String metricId) {
            String key = getMetricDataKey(filePath, metricId);
            return cache.get(key, com.cet.electric.ngapserver.entity.MetricData.class);
        }

        /**
         * 缓存电压报表数据
         */
        public static void cacheVoltageReport(String filePath,
                                            com.cet.electric.ngapserver.entity.CourtsStatistics reportData) {
            String key = getVoltageReportKey(filePath);
            cache.put(key, reportData);
        }

        /**
         * 获取电压报表数据
         */
        public static com.cet.electric.ngapserver.entity.CourtsStatistics getVoltageReport(String filePath) {
            String key = getVoltageReportKey(filePath);
            return cache.get(key, com.cet.electric.ngapserver.entity.CourtsStatistics.class);
        }

        /**
         * 缓存电压数据
         */
        public static void cacheVoltageData(String filePath, java.util.List<?> voltageData) {
            String key = getVoltageDataKey(filePath);
            cache.put(key, voltageData);
        }

        /**
         * 获取电压数据
         */
        @SuppressWarnings("unchecked")
        public static java.util.List<?> getVoltageData(String filePath) {
            String key = getVoltageDataKey(filePath);
            return cache.get(key, java.util.List.class);
        }

        /**
         * 缓存用户统计数据
         */
        public static void cacheUserStats(String filePath, java.util.List<com.cet.electric.ngapserver.entity.UserStatistics> userStats) {
            String key = getUserStatsKey(filePath);
            cache.put(key, userStats);
        }

        /**
         * 获取用户统计数据
         */
        @SuppressWarnings("unchecked")
        public static java.util.List<com.cet.electric.ngapserver.entity.UserStatistics> getUserStats(String filePath) {
            String key = getUserStatsKey(filePath);
            return cache.get(key, java.util.List.class);
        }

        /**
         * 缓存整体统计数据
         */
        public static void cacheOverallStats(String filePath, com.cet.electric.ngapserver.entity.CourtsStatistics overallStats) {
            String key = getOverallStatsKey(filePath);
            cache.put(key, overallStats);
        }

        /**
         * 获取整体统计数据
         */
        public static com.cet.electric.ngapserver.entity.CourtsStatistics getOverallStats(String filePath) {
            String key = getOverallStatsKey(filePath);
            return cache.get(key, com.cet.electric.ngapserver.entity.CourtsStatistics.class);
        }

        /**
         * 清理文件相关的所有缓存
         */
        public static void clearFileCache(String filePath) {
            String fileMetaKey = getFileMetaKey(filePath);

            // 清理所有以该文件为前缀的缓存
            cache.cache.entrySet().removeIf(entry ->
                entry.getKey().contains(fileMetaKey));
        }
    }
}
