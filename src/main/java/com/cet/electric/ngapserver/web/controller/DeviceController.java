package com.cet.electric.ngapserver.web.controller;

import com.cet.electric.ngapserver.dto.ErrorMsg;
import com.cet.electric.ngapserver.dto.DeviceQueryDTO;
import com.cet.electric.ngapserver.dto.ResponseDTO;
import com.cet.electric.ngapserver.entity.Device;
import com.cet.electric.ngapserver.service.DeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 设备控制器
 * <AUTHOR>
 */
@RestController
@Api(value = "DeviceController API", tags = "Device")
@RequestMapping("/ngap-server/api/devices")
public class DeviceController {

    private static final Logger log = LoggerFactory.getLogger(DeviceController.class);

    @Autowired
    private DeviceService deviceService;

    @PostMapping("/save")
    @ApiOperation(value = "批量保存设备(创建或更新)")
    public ResponseDTO<List<Device>> saveDevice(@RequestBody List<Device> deviceEntities) {
        log.info("接收到批量保存设备请求，数量: {}", deviceEntities.size());

        List<Device> results = new ArrayList<>();
        List<String> messages = new ArrayList<>();
        boolean hasErrors = false;

        for (Device deviceEntity : deviceEntities) {
            boolean isUpdate = deviceEntity.getParameterId() != null;

            // 参数预处理
            if (deviceEntity.getDeviceType() != null) {
                deviceEntity.setDeviceType(deviceEntity.getDeviceType().trim());
            }
            if (deviceEntity.getParameterCode() != null) {
                deviceEntity.setParameterCode(deviceEntity.getParameterCode().trim());
            }
            // 版本由系统自动管理，不处理用户输入的版本信息
            if (!isUpdate) {
                deviceEntity.setVersion(null); // 清空用户可能传入的版本信息
            }

            try {
                Device result;
                if (isUpdate) {
                    result = deviceService.updateDevice(deviceEntity);
                    log.info("设备更新成功: {}", result);
                    results.add(result);
                } else {
                    result = deviceService.createDevice(deviceEntity);
                    log.info("设备创建成功，ID: {}", result.getParameterId());
                    results.add(result);
                }
            } catch (ErrorMsg e) {
                hasErrors = true;
                log.error("设备操作失败: {}", e.getMessage());
                messages.add(e.getMessage());
            }
        }

        String message = hasErrors ? String.join("; ", messages) : "操作成功";
        return new ResponseDTO<>(hasErrors ? -1 : 0, message, results);
    }

    @PostMapping("/query")
    @ApiOperation(value = "分页查询设备列表")
    public ResponseDTO<List<Device>> getDevices(@RequestBody DeviceQueryDTO queryDTO) {
        log.info("接收到分页查询设备请求: {}", queryDTO);

        // 从DTO中提取参数
        Integer page = queryDTO.getPage();
        Integer size = queryDTO.getSize();
        String sortBy = queryDTO.getSortBy();
        String sortOrder = queryDTO.getSortOrder();
        String keyword = queryDTO.getKeyword();

        List<Device> result = deviceService.getDevices(page, size, sortBy, sortOrder, keyword);
        Long total = deviceService.countDevices(keyword);

        log.info("设备查询成功，总数量: {}", total);
        return new ResponseDTO<>(0, "查询成功", result, total);
    }

    @GetMapping("/detail")
    @ApiOperation(value = "根据ID查询设备")
    public ResponseDTO<Device> getDeviceById(@RequestParam("parameterId") Long parameterId) {
        log.info("接收到查询设备请求: parameterId={}", parameterId);

        Device result = deviceService.getDeviceById(parameterId);

        log.info("设备查询成功: {}", result);
        return new ResponseDTO<>(0, "查询成功", result);
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "删除设备")
    public ResponseDTO<Boolean> deleteDevice(@RequestParam("parameterId") Long parameterId) {
        log.info("接收到删除设备请求: parameterId={}", parameterId);

        boolean result = deviceService.deleteDevice(parameterId);

        log.info("设备删除结果: {}", result);
        return new ResponseDTO<>(0, result ? "设备删除成功" : "设备删除失败", result);
    }
}
