package com.cet.electric.ngapserver.dao;

import com.cet.electric.ngapserver.util.ResultSetUtils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * DuckDB DAO层实现，用于将CSV文件转换为表
 */
public class DuckDBDao {
    private Connection connection;
    private String dbPath;

    /**
     * 构造函数，初始化DuckDB连接
     * @param dbPath DuckDB数据库文件路径，如果为null则使用内存模式
     * @throws SQLException 数据库连接异常
     */
    public DuckDBDao(String dbPath) throws SQLException {
        this.dbPath = dbPath;
        if (dbPath == null) {
            // 内存模式
            connection = DriverManager.getConnection("jdbc:duckdb:");
        } else {
            // 持久化模式
            connection = DriverManager.getConnection("jdbc:duckdb:" + dbPath);
        }
    }

    /**
     * 关闭连接
     */
    public void close() {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 方法1：直接从CSV文件读取数据，自动推断选项
     * 这种方法只是查询CSV，不会创建表
     * @param csvFilePath CSV文件路径
     * @return 查询结果
     */
    public List<Map<String, Object>> queryFromCsv(String csvFilePath) {
        String sql = "SELECT * FROM '" + csvFilePath + "'";
        return query(sql);
    }

    /**
     * 方法2：使用read_csv函数读取CSV并应用自定义选项
     * @param csvFilePath CSV文件路径
     * @param delimiter 分隔符
     * @param hasHeader 是否包含表头
     * @param columnTypes 列类型定义，格式为：{'列名1': '类型1', '列名2': '类型2', ...}
     * @return 查询结果
     */
    public List<Map<String, Object>> queryFromCsvWithOptions(String csvFilePath, String delimiter,
                                                             boolean hasHeader, String columnTypes) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM read_csv('").append(csvFilePath).append("'");

        // 添加选项
        if (delimiter != null) {
            sql.append(", delim = '").append(delimiter).append("'");
        }

        sql.append(", header = ").append(hasHeader);

        if (columnTypes != null && !columnTypes.isEmpty()) {
            sql.append(", columns = ").append(columnTypes);
        }

        sql.append(")");

        return query(sql.toString());
    }

    /**
     * 方法3：将CSV数据导入到已定义结构的表中
     * @param tableName 表名
     * @param createTableSql 建表SQL语句
     * @param csvFilePath CSV文件路径
     * @param options COPY语句的选项，例如："DELIMITER ',', HEADER"
     * @return 是否成功导入
     */
    public boolean importCsvToTable(String tableName, String createTableSql,
                                    String csvFilePath, String options) {
        try {
            // 如果表已存在则删除
            dropTableIfExists(tableName);

            // 创建表
            try (Statement stmt = connection.createStatement()) {
                stmt.execute(createTableSql);
            }

            // 执行COPY命令导入数据
            StringBuilder copySql = new StringBuilder();
            copySql.append("COPY ").append(tableName)
                    .append(" FROM '").append(csvFilePath).append("'");

            if (options != null && !options.isEmpty()) {
                copySql.append(" (").append(options).append(")");
            }

            try (Statement stmt = connection.createStatement()) {
                stmt.execute(copySql.toString());
                return true;
            }
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 方法4：使用CREATE TABLE AS语句直接从CSV创建表
     * @param tableName 表名
     * @param csvFilePath CSV文件路径
     * @param options read_csv函数的选项，格式为"delim=',', header=true"等
     * @return 是否成功创建表
     */
    public boolean createTableFromCsv(String tableName, String csvFilePath, String options) {
        try {
            // 如果表已存在则删除
            dropTableIfExists(tableName);

            // 构建CREATE TABLE AS语句
            StringBuilder createSql = new StringBuilder();
            createSql.append("CREATE TABLE ").append(tableName).append(" AS ");

            if (options != null && !options.isEmpty()) {
                createSql.append("SELECT * FROM read_csv('").append(csvFilePath)
                        .append("', ").append(options).append(")");
            } else {
                // 直接使用CSV文件路径，自动推断选项
                createSql.append("FROM '").append(csvFilePath).append("'");
            }

            try (Statement stmt = connection.createStatement()) {
                stmt.execute(createSql.toString());
                return true;
            }
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 方法5：与方法4类似，但使用可变参数传入CSV读取选项
     * @param tableName 表名
     * @param csvFilePath CSV文件路径
     * @param delimiter 分隔符
     * @param hasHeader 是否包含表头
     * @param extraOptions 额外选项，格式为"option1=value1, option2=value2"
     * @return 是否成功创建表
     */
    public boolean createTableFromCsv(String tableName, String csvFilePath, String delimiter,
                                      boolean hasHeader, String extraOptions) {
        // 构建options字符串
        StringBuilder optionsBuilder = new StringBuilder();

        if (delimiter != null) {
            optionsBuilder.append("delim='").append(delimiter).append("'");
        }

        if (optionsBuilder.length() > 0) {
            optionsBuilder.append(", ");
        }
        optionsBuilder.append("header=").append(hasHeader);

        // 添加容错选项
        if (optionsBuilder.length() > 0) {
            optionsBuilder.append(", ");
        }
        optionsBuilder.append("ignore_errors=true, null_padding=true");

        // 添加额外选项
        if (extraOptions != null && !extraOptions.isEmpty()) {
            if (optionsBuilder.length() > 0) {
                optionsBuilder.append(", ");
            }
            optionsBuilder.append(extraOptions);
        }

        return createTableFromCsv(tableName, csvFilePath, optionsBuilder.toString());
    }

    /**
     * 如果表存在则删除
     * @param tableName 表名
     * @throws SQLException SQL异常
     */
    private void dropTableIfExists(String tableName) throws SQLException {
        try (Statement stmt = connection.createStatement()) {
            stmt.execute("DROP TABLE IF EXISTS " + tableName);
        }
    }

    /**
     * 执行查询并返回结果列表
     * @param sql SQL查询语句
     * @return 结果列表，每一行是一个字段名到值的映射
     */
    public List<Map<String, Object>> query(String sql) {
        List<Map<String, Object>> results = new ArrayList<>();
        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            // 使用ResultSetHelper将结果集转换为Map列表
            results = ResultSetUtils.resultSetToList(rs);

        } catch (SQLException e) {
            e.printStackTrace();
        }
        return results;
    }

    /**
     * 获取表结构信息
     * @param tableName 表名
     * @return 表结构信息列表
     */
    public List<ColumnInfo> getTableSchema(String tableName) {
        List<ColumnInfo> columns = new ArrayList<>();
        String sql = "PRAGMA table_info(" + tableName + ")";

        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                ColumnInfo column = new ColumnInfo();
                column.setName(rs.getString("name"));
                column.setType(rs.getString("type"));
                column.setNotNull(rs.getBoolean("notnull"));
                column.setPrimaryKey(rs.getBoolean("pk"));
                columns.add(column);
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return columns;
    }

    /**
     * 表列信息类
     */
    public static class ColumnInfo {
        private String name;
        private String type;
        private boolean notNull;
        private boolean primaryKey;

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getType() { return type; }
        public void setType(String type) { this.type = type; }

        public boolean isNotNull() { return notNull; }
        public void setNotNull(boolean notNull) { this.notNull = notNull; }

        public boolean isPrimaryKey() { return primaryKey; }
        public void setPrimaryKey(boolean primaryKey) { this.primaryKey = primaryKey; }

        @Override
        public String toString() {
            return "ColumnInfo{" +
                    "name='" + name + '\'' +
                    ", type='" + type + '\'' +
                    ", notNull=" + notNull +
                    ", primaryKey=" + primaryKey +
                    '}';
        }
    }
}
