package com.cet.electric.ngapserver.dao;

import com.cet.electric.ngapserver.entity.LineCode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Component
public interface LineCodeDao {

    /**
     * 创建新线路代码
     *
     * @param lineCode 线路代码实体
     * @return 受影响的行数
     */
    int createLineCode(LineCode lineCode);

    /**
     * 分页查询线路代码
     *
     * @param offset    偏移量
     * @param size      每页大小
     * @param sortBy    排序字段
     * @param sortOrder 排序方向
     * @param keyword   搜索关键词(可选)
     * @return 线路代码列表
     */
    List<LineCode> getLineCodes(@Param("offset") int offset,
                                @Param("size") int size,
                                @Param("sortBy") String sortBy,
                                @Param("sortOrder") String sortOrder,
                                @Param("keyword") String keyword);

    /**
     * 查询线路代码总数
     *
     * @param keyword 搜索关键词(可选)
     * @return 总数
     */
    Long countLineCodes(@Param("keyword") String keyword);

    /**
     * 根据线路代码名称查询
     *
     * @param lineCodeName 线路代码名称
     * @return 线路代码实体，如果不存在返回null
     */
    LineCode findByLineCodeName(String lineCodeName);

    /**
     * 根据线路代码查询
     *
     * @param lineCode 线路代码
     * @return 线路代码实体，如果不存在返回null
     */
    LineCode findByLineCode(String lineCode);

    /**
     * 根据ID查询线路代码
     *
     * @param lineCodeId 线路代码ID
     * @return 线路代码实体，如果不存在返回null
     */
    LineCode findById(Long lineCodeId);

    /**
     * 更新线路代码信息
     *
     * @param lineCode 更新的线路代码实体
     * @return 受影响的行数
     */
    int updateLineCode(LineCode lineCode);

    /**
     * 删除线路代码
     *
     * @param lineCodeId 线路代码ID
     * @return 受影响的行数
     */
    int deleteLineCode(Long lineCodeId);
}

