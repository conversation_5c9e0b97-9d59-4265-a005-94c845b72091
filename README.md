# NGAP Server 项目文档

## 项目概述

NGAP Server 是一个基于 Spring Boot 的电力系统仿真管理平台，主要用于电力系统的项目管理和仿真任务执行。系统支持通用场景和电压合格率场景的仿真分析，提供完整的项目生命周期管理功能。

### 主要功能

- **项目管理**：创建、查询、更新和删除电力系统项目
- **仿真任务管理**：创建和管理仿真任务，支持多种仿真场景
- **电压合格率统计**：🆕 智能分析仿真电压数据，计算合格率统计
- **数据处理**：支持 Excel 文件导入导出，CSV 数据处理
- **DSS 集成**：集成 OpenDSS 电力系统仿真引擎
- **文件管理**：支持项目配置文件的导入导出
- **定时任务**：自动清理过期数据
- **数据库自动初始化**：首次启动时自动创建数据库和表结构

## 技术栈

### 核心框架
- **Spring Boot**: 2.3.7.RELEASE
- **Java**: 8
- **Maven**: 项目构建工具

### 数据库
- **SQLite**: 3.41.2.1 (主数据库)
- **DuckDB**: 1.1.2 (数据分析)
- **MyBatis**: 2.1.4 (ORM 框架)

### 工具库
- **Apache POI**: 5.2.3 (Excel 处理)
- **Lombok**: 1.18.24 (代码简化)
- **FastJSON**: 2.0.45 (JSON 处理)
- **Commons Lang3**: 3.8.1 (工具类)
- **Swagger**: 2.9.1 (API 文档)

### 测试框架
- **JUnit**: 4.12
- **Mockito**: 2.9.0
- **PowerMock**: 1.7.4

## 项目架构

```
ngap-server/
├── src/main/java/com/cet/electric/ngapserver/
│   ├── NgapServerApplication.java          # 应用启动类
│   ├── web/controller/                     # 控制器层
│   │   ├── ProjectController.java          # 项目管理接口
│   │   ├── SimulationController.java       # 仿真任务接口
│   │   └── DeviceController.java           # 设备管理接口
│   ├── service/                           # 服务层
│   │   ├── ProjectService.java            # 项目服务接口
│   │   ├── SimulationService.java         # 仿真服务接口
│   │   └── impl/                          # 服务实现
│   ├── dao/                               # 数据访问层
│   │   ├── ProjectDao.java                # 项目数据访问
│   │   ├── SimulationDao.java             # 仿真数据访问
│   │   ├── DeviceDao.java                 # 设备数据访问
│   │   └── DuckDBDao.java                 # DuckDB 数据访问
│   ├── entity/                            # 实体类
│   │   ├── Project.java                   # 项目实体
│   │   ├── Simulation.java                # 仿真实体
│   │   └── Device.java                    # 设备实体
│   ├── dto/                               # 数据传输对象
│   ├── util/                              # 工具类
│   ├── enums/                             # 枚举类
│   └── schedule/                          # 定时任务
└── src/main/resources/
    ├── application.yml                     # 应用配置
    ├── logback-spring.xml                 # 日志配置
    └── mapper/                            # MyBatis 映射文件
```

## 数据库设计

### 数据库架构

项目使用 SQLite 作为主数据库，存储项目和仿真任务的核心数据。数据库包含三个主要表：

1. **project** - 项目信息表
2. **simulation** - 仿真任务表
3. **device** - 设备管理表

### 建库脚本

```sql
-- 启用外键约束
PRAGMA foreign_keys = ON;

-- 删除已存在的表（如果存在）
DROP TABLE IF EXISTS simulation;
DROP TABLE IF EXISTS project;

-- 创建项目表
CREATE TABLE project (
    project_id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_name TEXT NOT NULL,
    project_type TEXT NOT NULL,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    is_deleted INTEGER DEFAULT 0 CHECK (is_deleted IN (0, 1)),
    CONSTRAINT idx_project_name_not_deleted UNIQUE(project_name, is_deleted)
);

-- 创建仿真表
CREATE TABLE simulation (
    simulation_id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    project_name TEXT NOT NULL,
    simulation_name TEXT NOT NULL,
    simulation_draft TEXT,
    simulation_model TEXT,
    input_data TEXT,
    output_data TEXT,
    measured_data TEXT,
    simulation_script TEXT,
    nodes TEXT,
    control_strategy TEXT,
    run_status TEXT CHECK (run_status IN ('READY', 'RUNNING', 'SUCCESS', 'FAILED')),
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    is_deleted INTEGER DEFAULT 0 CHECK (is_deleted IN (0, 1)),
    FOREIGN KEY (project_id) REFERENCES project(project_id) ON DELETE CASCADE
);

-- 创建设备管理表
CREATE TABLE IF NOT EXISTS device (
    parameter_id INTEGER PRIMARY KEY AUTOINCREMENT,
    device_type VARCHAR(50) NOT NULL,
    parameter_code TEXT NOT NULL,
    version TEXT NOT NULL
);
```

### 表结构说明

#### project 表
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| project_id | INTEGER | 项目ID | 主键，自增 |
| project_name | TEXT | 项目名称 | 非空 |
| project_type | TEXT | 项目类型 | 非空 |
| created_at | INTEGER | 创建时间 | 非空，时间戳 |
| updated_at | INTEGER | 更新时间 | 非空，时间戳 |
| is_deleted | INTEGER | 删除标记 | 默认0，0-未删除，1-已删除 |

#### simulation 表
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| simulation_id | INTEGER | 仿真ID | 主键，自增 |
| project_id | INTEGER | 关联项目ID | 外键，非空 |
| project_name | TEXT | 项目名称 | 非空 |
| simulation_name | TEXT | 仿真名称 | 非空 |
| simulation_draft | TEXT | 仿真草稿 | 可空 |
| simulation_model | TEXT | 仿真模型 | 可空 |
| input_data | TEXT | 输入数据 | 可空 |
| output_data | TEXT | 输出数据 | 可空 |
| measured_data | TEXT | 实测数据 | 可空 |
| simulation_script | TEXT | 仿真脚本 | 可空 |
| nodes | TEXT | 并网点 | 可空 |
| control_strategy | TEXT | 控制策略 | 可空 |
| run_status | TEXT | 运行状态 | 可空，READY/RUNNING/SUCCESS/FAILED |
| created_at | INTEGER | 创建时间 | 非空，时间戳 |
| updated_at | INTEGER | 更新时间 | 非空，时间戳 |
| is_deleted | INTEGER | 删除标记 | 默认0，0-未删除，1-已删除 |

#### device 表
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| parameter_id | INTEGER | 参数ID | 主键，自增 |
| device_type | VARCHAR(50) | 设备类型 | 非空 |
| parameter_code | TEXT | 参数代码 | 非空 |
| version | TEXT | 版本 | 非空 |

### 数据关系

- `simulation.project_id` → `project.project_id` (一对多关系)
- 项目可以包含多个仿真任务
- 删除项目时级联删除相关仿真任务

## API 接口文档

### 项目管理接口

#### 创建项目
- **URL**: `POST /api/projects`
- **参数**: 
  - `projectName`: 项目名称
  - `projectType`: 项目类型
- **响应**: 创建的项目信息

#### 查询项目列表
- **URL**: `GET /api/projects`
- **参数**: 分页和排序参数
- **响应**: 项目列表和分页信息

#### 获取项目详情
- **URL**: `GET /api/projects/{projectId}`
- **参数**: `projectId` - 项目ID
- **响应**: 项目详细信息

#### 更新项目
- **URL**: `PUT /api/projects/{projectId}`
- **参数**: 项目更新信息
- **响应**: 更新后的项目信息

#### 删除项目
- **URL**: `DELETE /api/projects/{projectId}`
- **参数**: `projectId` - 项目ID
- **响应**: 删除结果

#### 导入项目配置
- **URL**: `POST /api/projects/import`
- **参数**: `file` - 配置文件
- **响应**: 导入的项目信息

### 仿真任务接口

#### 创建仿真任务
- **URL**: `POST /api/simulations`
- **参数**: 
  - `projectId`: 项目ID
  - `simulationName`: 仿真名称
- **响应**: 创建的仿真任务信息

#### 查询仿真任务列表
- **URL**: `GET /api/simulations`
- **参数**: 分页和筛选参数
- **响应**: 仿真任务列表

#### 运行仿真任务
- **URL**: `POST /api/simulations/{simulationId}/run`
- **参数**: `simulationId` - 仿真任务ID
- **响应**: 运行结果

#### 上传仿真数据
- **URL**: `POST /api/simulations/{simulationId}/upload`
- **参数**:
  - `simulationId`: 仿真任务ID
  - `file`: 数据文件
- **响应**: 上传结果

#### 计算仿真电压合格率 🆕
- **URL**: `GET /api/simulations/{simulationId}/voltage-quality-rate`
- **参数**: `simulationId` - 仿真任务ID
- **功能**: 统计仿真产生的电压数据合格率
- **特性**:
  - 自动扫描仿真输出目录下的所有CSV文件
  - 智能识别电压相关列（voltage、V、U、电压等格式）
  - 使用198-242V标准计算合格率
  - 支持批量处理大量数据
- **响应**:
  ```json
  {
    "code": 0,
    "message": "计算仿真电压合格率成功",
    "data": {
      "totalUsers": 0,
      "qualifiedRate": 85.5,
      "aboveMaxRate": 10.2,
      "belowMinRate": 4.3,
      "maxVoltage": 250.5,
      "minVoltage": 190.2,
      "totalReadings": 10000,
      "qualifiedReadings": 8550,
      "aboveMaxReadings": 1020,
      "belowMinReadings": 430
    }
  }
  ```

### 设备管理接口

#### 批量保存设备
- **URL**: `POST /ngap-server/api/devices/save`
- **参数**: 设备实体列表（支持创建和更新）
- **说明**: 创建时版本由系统自动设置为1.0，更新时保持现有版本
- **响应**: 保存的设备信息列表

#### 分页查询设备列表
- **URL**: `POST /ngap-server/api/devices/query`
- **参数**: 分页和搜索参数（DeviceQueryDTO）
- **响应**: 设备列表和总数

#### 根据ID查询设备
- **URL**: `GET /ngap-server/api/devices/detail`
- **参数**: `parameterId` - 参数ID
- **响应**: 设备详细信息

#### 删除设备
- **URL**: `DELETE /ngap-server/api/devices/delete`
- **参数**: `parameterId` - 参数ID
- **响应**: 删除结果

## 配置说明

### 应用配置 (application.yml)

```yaml
server:
  port: 28080

mybatis:
  mapper-locations: classpath:mapper/*Mapper.xml

spring:
  datasource:
    url: ${DATABASE_URL:*****************************}
    driver-class-name: org.sqlite.JDBC
    username:
    password:
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

dss:
  url: http://localhost:5000
```

### 关键配置项

- **服务端口**: 28080
- **数据库**: SQLite，支持自动初始化，路径可通过环境变量配置
- **文件上传**: 最大10MB
- **DSS服务**: 默认端口5000

### 数据库自动初始化

应用首次启动时会自动检查并创建数据库：

1. **自动创建**: 如果数据库文件不存在，系统会自动创建
2. **表结构初始化**: 自动创建 project、simulation、device 表
3. **结构验证**: 启动时验证表结构完整性，缺失时自动修复
4. **灵活配置**: 支持通过环境变量自定义数据库路径

**环境变量配置示例**:
```bash
# 设置自定义数据库路径
export DATABASE_URL=************************************

# 或使用JVM参数
java -DDATABASE_URL=************************************ -jar ngap-server.jar
```

## 环境要求

### 开发环境
- **JDK**: 1.8 或以上
- **Maven**: 3.6 或以上
- **IDE**: IntelliJ IDEA 或 Eclipse

### 运行环境
- **Java Runtime**: 1.8 或以上
- **SQLite**: 数据库文件
- **OpenDSS**: 仿真引擎（可选）

## 构建和运行

### 1. 克隆项目
```bash
git clone <repository-url>
cd ngap-server
```

### 2. 编译项目
```bash
mvn clean compile
```

### 3. 运行测试
```bash
mvn test
```

### 4. 打包应用
```bash
mvn clean package
```

### 5. 运行应用
```bash
# 开发模式
mvn spring-boot:run

# 或运行打包后的jar
java -jar target/ngap-server.jar
```

**注意**: 首次启动时，应用会自动创建数据库文件和表结构，无需手动配置。

### 6. 访问应用
- **应用地址**: http://localhost:28080
- **API文档**: http://localhost:28080/swagger-ui.html

## 开发指南

### 代码规范
- 使用 Lombok 简化代码
- 遵循 RESTful API 设计原则
- 统一异常处理和响应格式
- 完善的日志记录

### 数据库操作
- 使用 MyBatis 进行数据访问
- 支持事务管理
- 软删除机制（is_deleted 字段）

### 文件处理
- 支持 Excel 文件导入导出
- CSV 数据处理和分析
- 文件上传大小限制

### 定时任务
- 自动清理过期数据
- 可配置执行频率

## 项目特色

1. **多数据库支持**: SQLite + DuckDB
2. **数据库自动初始化**: 首次启动自动创建数据库和表结构
3. **异步任务处理**: 支持长时间运行的仿真任务
4. **智能电压分析**: 🆕 自动识别电压列，计算合格率统计
5. **文件处理能力**: Excel/CSV 数据处理
6. **外部系统集成**: OpenDSS 仿真引擎
7. **完善的测试覆盖**: 单元测试和集成测试

## 联系信息

- **开发者**: 张蒲龙
- **项目版本**: 1.0.0-SNAPSHOT
- **最后更新**: 2025-06-19
