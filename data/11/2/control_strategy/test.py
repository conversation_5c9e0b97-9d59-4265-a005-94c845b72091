# -*- coding: utf-8 -*-
"""
Created on Tue May 20 16:03:25 2025

@author: jiabuliao
"""

import opendssdirect as dss
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
# 在python中实现每一个小时的储能控制

# 清除之前的模型并加载修改后的 DSS 脚本
# dss.Basic.ClearAll()
# dss.Text.Command('Clear')                                         # 清理旧模型
# dss.Text.Command('Compile D:/ai/test.dss')
# dss.Text.Command("Calcv")
# dss.Text.Command("set voltagebases=[10, 0.38]")
dss.Text.Command("Set mode=daily number=1 stepsize=3600")


# In[]

Load1_kW = 10
Load2_kW = 10



PV_pmpp = 30

# 定义24h的电价
price_profile =  [0.5, 0.45, 0.4, 0.35, 0.3, 0.3, 0.3, 0.3, 0.8, 1.0, 1.1, 1.2,
                 1.3, 1.3, 1.2, 1.1, 1.0, 0.9, 0.8, 0.7, 0.6, 0.55, 0.5, 0.45]
# 设置充放电门限
charge_threshold = 0.6    # 当电价低于此值时，充电
discharge_threshold = 1.2 # 当电价高于此值时，放电

# simulation setting
battery_mode_list: np.ndarray = np.zeros(24)
battery_power_list = []
battery_soc_list = []
pv_kw_list = []
voltage_node1_list = []  # node1 电压（标幺值）
voltage_node2_list = []  # node2 电压（标幺值）
voltage_node3_list = []  # node3 电压（标幺值）

# Simulation start
for hour in range(24):
    if price_profile[hour] < charge_threshold:
        control_state = "charging"
        battery_mode_list[hour] = -1
    elif price_profile[hour] >= discharge_threshold:
        control_state = "discharging"
        battery_mode_list[hour] = 1
    else:
        control_state = "idling"

    dss.Text.Command(f"Edit Storage.storage_1 state={control_state}")

    # ontime_load1 = Load_profile[hour] * Load1_kW
    # ontime_load2 = Charger_profile[hour] * Load2_kW

    # dss.Text.Command(f"Edit load.Load1 kw={ontime_load1}")
    # dss.Text.Command(f"Edit load.Load1 kw={ontime_load2}")


    # ontime_PV = PV_profile[hour] * PV_pmpp
    # dss.Text.Command(f"Edit PVsystem.PV1 pmpp={ontime_PV}")

    dss.Solution.Solve()
    # 1. 获取储能的充放电功率（单位：kW），必须用Monitor获取
    dss.Monitors.SaveAll()
    dss.Monitors.Name("monitor_storage_1_PQ")
    # 模式1下，通道顺序为 [P1, Q1, P2, Q2, P3, Q3]
    # 提取所有时间点的有功功率（kW）
    battery_power_list = 3*dss.Monitors.Channel(1)   # 这个是其中A相的功率，所以要乘以3

    # 2. 获取储能当前的 SOC 数据（假定为百分比）
    dss.Storages.Name("storage_1")
    soc = dss.Storages.puSOC()
    battery_soc_list.append(soc)

    # 3. 获取光伏的有功出力（单位：kW）
    dss.PVsystems.Name("pvSystem_1")
    pv_kw = dss.PVsystems.kW()
    pv_kw_list.append(pv_kw)
    #
    # 4. 获取 node1、node2、node3 节点的电压（标幺值）
    voltage_values = {}
    for bus in ["node_1", "node_2", "node_3"]:
        dss.Circuit.SetActiveBus(bus)
        # 使用 puVmagAngle() 获取标幺电压和相角，取第一个数值作为电压幅值
        vdata = dss.Bus.PuVoltage()
        voltage_values[bus] = vdata[0]
    voltage_node1_list.append(380*voltage_values["node_1"])
    voltage_node2_list.append(380*voltage_values["node_2"])
    voltage_node3_list.append(380*voltage_values["node_3"])

plt.subplot(3, 1, 1)
plt.plot(battery_power_list)

plt.subplot(3, 1, 2)
plt.plot(pv_kw_list)

plt.subplot(3, 1, 3)
plt.plot(battery_soc_list)

# plt.plot(np.array(pv_kw_list))
plt.show()

# In[]
import csv
import os

output_dir = 'D:/CETWorkSpace/ngap-server/data/3/30/simulation_script'
file_name = 'battery_soc_list.csv'
full_path = os.path.join(output_dir, file_name)

os.makedirs(output_dir, exist_ok=True)
hours = list(range(24))          # 0-23小时序列
t_values = [0] * 24             # 全0列
combined_data = [
    [ hour, t,soc]
    for hour, t,soc in zip( hours, t_values,battery_soc_list)
]
with open(full_path, 'w', newline='', encoding='utf-8') as f:
    writer = csv.writer(f)
    
    # 写入表头
    writer.writerow([ 'Hour', 't(sec)','SOC'])
    
    # 写入数据行
    writer.writerows(combined_data)