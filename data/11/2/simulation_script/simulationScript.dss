clear

New Circuit.circuit   Bus=node_1 Phases=3 basekv=10 pu=1 



set defaultbasefrequency=60
set mode=daily stepsize=1h number=24 hour=0 sec=0
set voltagebases=[0.22,0.38,10] 


new transformer.Transformer_1 phases=3 windings=2
~ wdg=1  bus=node_1 kv=10 kva=1000 conn=DELT
~ wdg=2  bus=node_3 kv=0.38 kva=1000 conn=WYE



new LineCode.IdealLineCode
~ nphases=3 basefreq=60 units=km
~ rmatrix = (0.000001 | 1e-7 0.000001 | 1e-7 1e-7 0.000001)
~ xmatrix = (0.000001 | 1e-7 0.000001 | 1e-7 1e-7 0.000001)

new LineCode.NewLineCode12
~ nphases=1 basefreq=60 units=km
~ rmatrix = (1)
~ xmatrix = (0)

new LineCode.NewLineCode
~ nphases=3 basefreq=60 units=km
~ rmatrix = (1 | 2 3 | 4 5 6)
~ xmatrix = (2 | 3 4 | 5 6 7)

new LineCode.NewLineCode_1
~ nphases=3 basefreq=60 units=km
~ rmatrix = (0 | 0 0 | 0 0 0)
~ xmatrix = (0 | 0 0 | 0 0 0)

new LineCode.NewLineCode_2
~ nphases=3 basefreq=60 units=m
~ rmatrix = (1 | 1 1 | 1 1 2)
~ xmatrix = (0 | 0 0 | 0 0 0)

new LineCode.NewLineCode_3_single
~ nphases=1 basefreq=60 units=km
~ rmatrix = (9)
~ xmatrix = (1)

new LineCode.Cu25_3P
~ nphases=3 basefreq=50 units=km
~ rmatrix = (0.73 | 0 0.3375 | 0 0 0.73)
~ xmatrix = (0.1 | 0.05 0.1 | 0.05 0.05 0.1)




New Line.bb6e39__0   Bus1=node_3   Bus2=node_2 Phases=3 LineCode=Cu25_3P Length=0.05
New Line.bb6e39__1   Bus1=node_3   Bus2=node_4 Phases=3 LineCode=Cu25_3P Length=0.05
New Line.bb6e39__2   Bus1=node_4   Bus2=node_5 Phases=3 LineCode=Cu25_3P Length=0.05


//负荷相关
new LoadShape.LoadShape_load_1
~ npts=24 interval=1
~ mult=(0.6,0.5,0.5,0.4,0.4,0.5,0.7,0.8,1,1.2,1.3,1.4,1.5,1.5,1.3,1.2,1.1,1,0.9,0.8,0.7,0.6,0.6,0.6)

new Load.load_1
~ phases=3 model=1
~ kv=0.38 kvar=0 kw=10
~ bus1=node_2
~ daily=LoadShape_load_1

new LoadShape.LoadShape_load_2
~ npts=24 interval=1
~ mult=(0.6,0.5,0.5,0.4,0.4,0.5,0.7,0.8,1,1.2,1.3,1.4,1.5,1.5,1.3,1.2,1.1,1,0.9,0.8,0.7,0.6,0.6,0.6)

new Load.load_2
~ phases=3 model=1
~ kv=0.38 kvar=0 kw=10
~ bus1=node_5
~ daily=LoadShape_load_2



//光伏相关
new LoadShape.pvLoadShape_pvSystem_1
~ npts=24 interval=1
~ mult=(0.2,0.17,0.17,0.13,0.13,0.17,0.23,0.27,0.33,0.4,0.43,0.47,0.5,0.5,0.43,0.4,0.37,0.33,0.3,0.27,0.23,0.2,0.2,0.2)

new PVSystem.pvSystem_1
~ phases=3 model=1 controlmode=GFL
~ kv=0.38 kva=30 pf=1
~ bus1=node_3
~ pmpp=30
~ daily=pvLoadShape_pvSystem_1



//储能相关
new LoadShape.storageLoadShape_storage_1
~ npts=24 interval=1
~ mult=(0.12,0.1,0.1,0.08,0.08,0.1,0.14,0.16,0.2,0.24,0.26,0.28,0.3,0.3,0.26,0.24,0.22,0.2,0.18,0.16,0.14,0.12,0.12,0.12)

new storage.storage_1
~ phases=3 state=IDLING
~ kv=0.38 kWhRated=50 kWRated=50
~ %effCharge=90
~ %effDischarge=90
~ %idlingkW=0
~ %stored=100
~ %reserve=20
~ %charge=100
~ %discharge=100
~ dischargeTrigger=0
~ chargeTrigger=0
~ bus1=node_4
~ daily=storageLoadShape_storage_1



//关键参数监控
New Monitor.monitor_load_1_VI  element=Load.load_1 terminal=1 mode=0
New Monitor.monitor_load_1_PQ  element=Load.load_1 terminal=1 mode=1
New Monitor.monitor_load_2_VI  element=Load.load_2 terminal=1 mode=0
New Monitor.monitor_load_2_PQ  element=Load.load_2 terminal=1 mode=1
New Monitor.monitor_pvSystem_1_VI  element=Pvsystem.pvSystem_1 terminal=1 mode=0
New Monitor.monitor_pvSystem_1_PQ  element=Pvsystem.pvSystem_1 terminal=1 mode=1
New Monitor.monitor_storage_1_VI  element=Storage.storage_1 terminal=1 mode=0
New Monitor.monitor_storage_1_PQ  element=Storage.storage_1 terminal=1 mode=1


calcv
solve
export monitor monitor_load_1_VI
export monitor monitor_load_1_PQ
export monitor monitor_load_2_VI
export monitor monitor_load_2_PQ
export monitor monitor_pvSystem_1_VI
export monitor monitor_pvSystem_1_PQ
export monitor monitor_storage_1_VI
export monitor monitor_storage_1_PQ