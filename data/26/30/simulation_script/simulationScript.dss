clear

new Circuit.circuit phases=3 bus=node_1
~ basekv=0 pu=1


set defaultbasefrequency=50
set mode=snapshot stepsize=1h number=1 hour=0 sec=0
set voltagebases=[0.22,0.38,10] 


new Transformer.Regulator_1
~ phases=3 windings=2 bank=RegControl_4_Regulator_1
~ XHL=0.01 %loadloss=0
~ maxtap=1.1 mintap=0.9 numtaps=32
~ wdg=1 bus=node_3 kv=0.4 kva=100 conn=DELT %R=0.1 tap=0
~ wdg=2 bus=node_4 kv=0.4 kva=100 conn=WYE %R=0.1 tap=0

new RegControl.RegControl_4_Regulator_1 transformer=Regulator_1 winding=1
~ vreg=11 band=0 ptratio=20
~ enabled=true
new Transformer.Transformer_2
~ phases=3 windings=2 
~ XHL=0.01 %loadloss=0.1
~ maxtap=1.1 mintap=0.9 numtaps=32
~ wdg=1 bus=node_1 kv=10 kva=100 conn=DELT %R=0.1 tap=0
~ wdg=2 bus=node_2 kv=0.4 kva=100 conn=WYE %R=0.1 tap=0















new LineCode.LGJ25
~ nphases=3 basefreq=50 units=km R1=1.37 X1=0.38

new LineCode.JKLYJ35
~ nphases=3 basefreq=50 units=km R1=0.3238 X1=0.303

new LineCode.JKLYJ70
~ nphases=3 basefreq=50 units=km R1=0.46 X1=0.398




new Line.259b0c3__0 phases=3 bus1=node_2 bus2=node_3
~ lineCode=JKLYJ35 length=3
new Line.19c450b2__0 phases=3 bus1=node_4 bus2=node_5
~ lineCode=JKLYJ35 length=1


//负荷相关
new Load.load_1 phases=3 bus1=node_5
~ model=1 kv=12.47 kvar=0 kw=1



//光伏相关


//储能相关


//关键参数监控
New Monitor.monitor_load_1_VI  element=Load.load_1 terminal=1 mode=0 ppolar=0
New Monitor.monitor_load_1_PQ  element=Load.load_1 terminal=1 mode=1 ppolar=0


calcv
solve
export monitor monitor_load_1_VI
export monitor monitor_load_1_PQ
